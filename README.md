# 📱 Cellphones Audit App

Ứng dụng kiểm tra và đánh giá cửa hàng điện thoại di động với giao diện thân thiện và tính năng đầy đủ.

## ✨ Tính năng chính

- 🔐 **<PERSON><PERSON> thống xác thực** - <PERSON><PERSON><PERSON>, đ<PERSON><PERSON> ký, quên mật khẩu
- 📊 **Dashboard** - Tổng quan thông tin audit
- 📝 **Form Audit chi tiết** - Đ<PERSON>h giá toàn diện cửa hàng
- 📸 **Upload hình ảnh** - Chụp và tải lên ảnh kiểm tra
- ✅ **Checklist đánh giá** - <PERSON><PERSON><PERSON> thu ngân, bàn tư vấn, cửa hàng chuyên nghiệp
- 🌙 **Dark/Light mode** - Hỗ trợ chế độ sáng/tối
- 📱 **Đa nền tảng** - Android, iOS, Web
- 🎨 **UI hiện đại** - NativeWind v4 + React Native Reusables

## 📋 Yêu cầu hệ thống

Trước khi b<PERSON><PERSON> đầu, hã<PERSON> đảm bảo bạn đã cài đặt:

### 🛠️ Phần mềm bắt buộc

- **Node.js** >= 18.0.0 ([Tải xuống](https://nodejs.org/))
- **npm** hoặc **yarn**
- **Git** ([Tải xuống](https://git-scm.com/))

### 📱 Cho phát triển Mobile

- **Expo CLI**: `npm install -g @expo/cli`
- **Android Studio** (cho Android) - [Hướng dẫn cài đặt](https://docs.expo.dev/workflow/android-studio-emulator/)
- **Xcode** (cho iOS, chỉ trên macOS) - [Hướng dẫn cài đặt](https://docs.expo.dev/workflow/ios-simulator/)

### 📱 Cho test trên thiết bị thật

- **Expo Go App** ([Android](https://play.google.com/store/apps/details?id=host.exp.exponent) | [iOS](https://apps.apple.com/app/expo-go/id982107779))

## 🚀 Cài đặt và chạy dự án

### 1️⃣ Clone dự án

```bash
git clone <repository-url>
cd cellphones-audit
```

### 2️⃣ Cài đặt dependencies

```bash
npm install
# hoặc
yarn install
```

### 3️⃣ Chạy dự án

#### 🌐 Chạy trên Web (khuyến nghị cho phát triển)

```bash
npm run dev:web
# hoặc
npm run web
```

Mở trình duyệt tại: `http://localhost:8081`

#### 📱 Chạy trên Android

```bash
npm run dev:android
# hoặc
npm run android
```

#### 🍎 Chạy trên iOS (chỉ macOS)

```bash
npm run ios
```

#### 🔄 Chạy development server (tất cả platforms)

```bash
npm run dev
```

Sau đó quét QR code bằng Expo Go app

## 📁 Cấu trúc dự án

```
cellphones-audit/
├── app/                          # Expo Router screens
│   ├── (auth)/                   # Nhóm màn hình xác thực
│   │   ├── login.tsx            # Màn hình đăng nhập
│   │   ├── register.tsx         # Màn hình đăng ký
│   │   └── forgot-password.tsx  # Quên mật khẩu
│   ├── (audit)/                 # Nhóm màn hình audit
│   │   ├── index.tsx           # Danh sách audit
│   │   └── audit-detail.tsx    # Chi tiết audit
│   ├── dashboard.tsx           # Màn hình dashboard
│   └── index.tsx              # Màn hình chính
├── src/                        # Source code chính
│   ├── modules/               # Các module tính năng
│   │   ├── auth/             # Module xác thực
│   │   ├── audit/            # Module audit
│   │   └── shared/           # Module chia sẻ
│   ├── components/           # Components tái sử dụng
│   ├── hooks/               # Custom hooks
│   ├── stores/              # State management
│   └── utils/               # Tiện ích
├── components/               # UI Components
│   ├── ui/                  # Base UI components
│   └── ThemeToggle.tsx     # Component chuyển đổi theme
├── assets/                  # Tài nguyên (ảnh, icon)
├── android/                # Code native Android
└── package.json            # Dependencies và scripts
```

## 🛠️ Scripts có sẵn

| Script                | Mô tả                             |
| --------------------- | --------------------------------- |
| `npm run dev`         | Khởi động development server      |
| `npm run dev:web`     | Chạy trên web browser             |
| `npm run dev:android` | Chạy trên Android emulator/device |
| `npm run ios`         | Chạy trên iOS simulator           |
| `npm run clean`       | Xóa cache và node_modules         |

## 🎨 Hướng dẫn phát triển

### 🎯 Thêm màn hình mới

1. Tạo file trong thư mục `app/` theo cấu trúc Expo Router
2. Export default component
3. Sử dụng TypeScript cho type safety

### 🎨 Styling với NativeWind

```tsx
// Sử dụng Tailwind classes
<View className="flex-1 bg-white p-4">
  <Text className="text-lg font-bold text-gray-800">Hello World</Text>
</View>
```

### 🧩 Sử dụng UI Components

```tsx
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";

<Card>
  <CardContent>
    <Button>Click me</Button>
  </CardContent>
</Card>;
```

## 🐛 Xử lý sự cố

### ❌ Lỗi Metro bundler

```bash
npm run clean
npm install
npm run dev
```

### ❌ Lỗi Android build

```bash
cd android
./gradlew clean
cd ..
npm run android
```

### ❌ Lỗi cache

```bash
expo start -c
# hoặc
npx expo start --clear
```

## 📱 Build cho Production

### 🤖 Android APK

```bash
npx expo build:android
```

### 🍎 iOS IPA (cần macOS)

```bash
npx expo build:ios
```

### 🌐 Web

```bash
npx expo build:web
```

## 🤝 Đóng góp

1. Fork dự án
2. Tạo branch tính năng: `git checkout -b feature/AmazingFeature`
3. Commit thay đổi: `git commit -m 'Add some AmazingFeature'`
4. Push to branch: `git push origin feature/AmazingFeature`
5. Mở Pull Request

## 📄 License

Dự án này được phát hành dưới MIT License.

## 📞 Hỗ trợ

Nếu gặp vấn đề, vui lòng:

1. Kiểm tra [Issues](../../issues) có sẵn
2. Tạo issue mới với mô tả chi tiết
3. Liên hệ team phát triển

---

🎉 **Chúc bạn phát triển thành công!** 🎉
