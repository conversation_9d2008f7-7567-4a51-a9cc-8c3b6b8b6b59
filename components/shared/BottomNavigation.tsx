import React from "react";
import { View, Text, Pressable, StyleSheet } from "react-native";
import { usePathname, useRouter } from "expo-router";
import { Home, ClipboardList, Bell, User } from "lucide-react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { LinearGradient } from "expo-linear-gradient";

interface TabItem {
  name: string;
  path: string;
  icon: React.ComponentType<{ size: number; color: string }>;
  label: string;
}

const tabs: TabItem[] = [
  {
    name: "home",
    path: "/(tabs)/home",
    icon: Home,
    label: "Trang chủ",
  },
  // {
  //   name: "audit",
  //   path: "/(audit)/plan",
  //   icon: ClipboardList,
  //   label: "Kế hoạch",
  // },
  {
    name: "profile",
    path: "/(tabs)/profile",
    icon: User,
    label: "Cá nhân",
  },
];

export function BottomNavigation() {
  const pathname = usePathname();
  const router = useRouter();
  const insets = useSafeAreaInsets();

  const isActive = (path: string) => {
    if (path === "/(tabs)/home") {
      return pathname === "/(tabs)/home";
    }
    if (path === "/(audit)/plan") {
      return pathname.startsWith("/(audit)");
    }
    return pathname.startsWith(path);
  };

  const handleTabPress = (path: string) => {
    router.push(path as any);
  };

  return (
    <View style={[styles.container, { paddingBottom: insets.bottom + 12 }]}>
      {/* Main floating card */}
      <View style={styles.floatingCard}>
        <View style={styles.cardInner}>
          <View style={styles.tabsContainer}>
            {tabs.map((tab) => {
              const active = isActive(tab.path);
              const Icon = tab.icon;

              return (
                <Pressable
                  key={tab.name}
                  onPress={() => handleTabPress(tab.path)}
                  style={styles.tabButton}
                >
                  {/* Icon container */}
                  <View
                    style={[
                      styles.iconContainer,
                      active
                        ? styles.activeIconContainer
                        : styles.inactiveIconContainer,
                    ]}
                  >
                    <Icon size={20} color={active ? "#ffffff" : "#9ca3af"} />
                  </View>

                  {/* Text label */}
                  <Text
                    style={[
                      styles.tabLabel,
                      active ? styles.activeLabel : styles.inactiveLabel,
                    ]}
                    numberOfLines={1}
                  >
                    {tab.label}
                  </Text>
                </Pressable>
              );
            })}
          </View>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: "relative",
    paddingHorizontal: 8,
  },

  floatingCard: {
    backgroundColor: "#ffffff",
    borderRadius: 20,
    marginHorizontal: 8,
    marginBottom: 8,
    shadowColor: "#1e293b",
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 28,
    elevation: 16,
    borderWidth: 1,
    borderColor: "#e2e8f0",
  },
  cardInner: {
    paddingVertical: 12,
    paddingHorizontal: 4,
    borderRadius: 19,
    backgroundColor: "rgba(255,255,255,0.95)",
  },
  tabsContainer: {
    flexDirection: "row",
  },
  tabButton: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingHorizontal: 12,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 16,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 4,
  },
  activeIconContainer: {
    backgroundColor: "#2563eb",
    shadowColor: "#2563eb",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  inactiveIconContainer: {
    backgroundColor: "#f3f4f6",
  },
  tabLabel: {
    fontSize: 12,
    textAlign: "center",
  },
  activeLabel: {
    color: "#2563eb",
    fontWeight: "600",
  },
  inactiveLabel: {
    color: "#6b7280",
    fontWeight: "500",
  },
});
