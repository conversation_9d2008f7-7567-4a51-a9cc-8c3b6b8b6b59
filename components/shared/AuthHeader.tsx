import { View, Text, Pressable } from "react-native";
import React from "react";
import { ChevronLeft } from "lucide-react-native";
import { useRouter } from "expo-router";

export default function AuthHeader() {
  const router = useRouter();

  const handleBackPress = () => {
    if (router.canGoBack()) {
      router.back();
    } else {
      router.push("/");
    }
  };

  return (
    <View className="w-full bg-primary px-2 py-3 flex-row items-center justify-between">
      {/* Back Button */}
      <Pressable onPress={handleBackPress}>
        <ChevronLeft size={24} color="white" />
      </Pressable>

      {/* Logo Cellphones */}
      <View className="flex-1 items-end">
        <Text className="text-white text-lg font-bold">CELLPHONES</Text>
      </View>
    </View>
  );
}
