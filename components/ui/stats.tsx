import React from "react";
import { View, Text } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { 
  TrendingUp, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  BarChart3,
  Target,
  Users,
  Store
} from "lucide-react-native";

interface StatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  trend?: "up" | "down" | "neutral";
  trendValue?: string;
  icon?: React.ComponentType<{ size: number; color: string }>;
  gradient?: [string, string];
  type?: "success" | "warning" | "info" | "danger";
}

export function StatCard({
  title,
  value,
  subtitle,
  trend,
  trendValue,
  icon: Icon,
  gradient,
  type = "info",
}: StatCardProps) {
  const getDefaultGradient = () => {
    switch (type) {
      case "success":
        return ['#10b981', '#059669'] as [string, string];
      case "warning":
        return ['#f59e0b', '#d97706'] as [string, string];
      case "danger":
        return ['#ef4444', '#dc2626'] as [string, string];
      default:
        return ['#3b82f6', '#2563eb'] as [string, string];
    }
  };

  const getDefaultIcon = () => {
    switch (type) {
      case "success":
        return CheckCircle;
      case "warning":
        return Clock;
      case "danger":
        return AlertCircle;
      default:
        return BarChart3;
    }
  };

  const IconComponent = Icon || getDefaultIcon();
  const colors = gradient || getDefaultGradient();

  return (
    <View className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
      <View className="flex-row items-center justify-between mb-4">
        <View className="flex-row items-center">
          <LinearGradient
            colors={colors}
            className="w-12 h-12 rounded-xl items-center justify-center mr-3"
          >
            <IconComponent size={24} color="white" />
          </LinearGradient>
          <View>
            <Text className="text-sm font-medium text-gray-600">{title}</Text>
            {subtitle && (
              <Text className="text-xs text-gray-500">{subtitle}</Text>
            )}
          </View>
        </View>
        
        {trend && (
          <View className={`flex-row items-center px-2 py-1 rounded-full ${
            trend === "up" ? "bg-green-100" : 
            trend === "down" ? "bg-red-100" : "bg-gray-100"
          }`}>
            <TrendingUp 
              size={12} 
              color={trend === "up" ? "#10b981" : trend === "down" ? "#ef4444" : "#6b7280"} 
            />
            <Text className={`text-xs font-medium ml-1 ${
              trend === "up" ? "text-green-600" : 
              trend === "down" ? "text-red-600" : "text-gray-600"
            }`}>
              {trendValue}
            </Text>
          </View>
        )}
      </View>
      
      <Text className="text-3xl font-bold text-gray-900">{value}</Text>
    </View>
  );
}

export function StatsGrid({ children }: { children: React.ReactNode }) {
  return (
    <View className="grid grid-cols-2 gap-4">
      {children}
    </View>
  );
}

export function AuditStats() {
  return (
    <View className="space-y-4">
      <Text className="text-lg font-bold text-gray-900 mb-4">Thống kê tổng quan</Text>
      
      <StatsGrid>
        <StatCard
          title="Tổng kế hoạch"
          value="24"
          subtitle="Trong tháng này"
          trend="up"
          trendValue="+12%"
          type="info"
          icon={Target}
        />
        
        <StatCard
          title="Đã hoàn thành"
          value="18"
          subtitle="Kế hoạch"
          trend="up"
          trendValue="+8%"
          type="success"
          icon={CheckCircle}
        />
        
        <StatCard
          title="Đang thực hiện"
          value="4"
          subtitle="Kế hoạch"
          type="warning"
          icon={Clock}
        />
        
        <StatCard
          title="Cửa hàng"
          value="156"
          subtitle="Đã kiểm tra"
          trend="up"
          trendValue="+5%"
          type="info"
          icon={Store}
        />
      </StatsGrid>
    </View>
  );
}

export function ProgressCard({
  title,
  current,
  total,
  subtitle,
  type = "info",
}: {
  title: string;
  current: number;
  total: number;
  subtitle?: string;
  type?: "success" | "warning" | "info" | "danger";
}) {
  const percentage = Math.round((current / total) * 100);
  
  const getProgressColor = () => {
    switch (type) {
      case "success":
        return "bg-green-500";
      case "warning":
        return "bg-yellow-500";
      case "danger":
        return "bg-red-500";
      default:
        return "bg-blue-500";
    }
  };

  return (
    <View className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
      <View className="flex-row items-center justify-between mb-4">
        <View>
          <Text className="text-lg font-semibold text-gray-900">{title}</Text>
          {subtitle && (
            <Text className="text-sm text-gray-600">{subtitle}</Text>
          )}
        </View>
        <Text className="text-2xl font-bold text-gray-900">
          {current}/{total}
        </Text>
      </View>
      
      <View className="w-full bg-gray-200 rounded-full h-3 mb-2">
        <View 
          className={`h-3 rounded-full ${getProgressColor()}`}
          style={{ width: `${percentage}%` }}
        />
      </View>
      
      <Text className="text-sm text-gray-600">
        {percentage}% hoàn thành
      </Text>
    </View>
  );
} 