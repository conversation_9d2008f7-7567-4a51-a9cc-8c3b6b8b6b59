import React from "react";
import { View, Text, Pressable } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { CheckCircle, Check, ArrowRight, Sparkles } from "lucide-react-native";

interface SuccessProps {
  title?: string;
  message?: string;
  onAction?: () => void;
  actionText?: string;
  showIcon?: boolean;
}

export function Success({
  title = "Thành công!",
  message = "Thao tác đã được thực hiện thành công.",
  onAction,
  actionText = "Tiếp tục",
  showIcon = true,
}: SuccessProps) {
  return (
    <View className="flex-1 justify-center items-center px-6">
      <LinearGradient
        colors={['#10b981', '#059669']}
        className="w-24 h-24 rounded-full items-center justify-center mb-6 shadow-lg"
      >
        {showIcon ? (
          <CheckCircle size={48} color="white" />
        ) : (
          <Check size={48} color="white" />
        )}
      </LinearGradient>
      
      <Text className="text-2xl font-bold text-gray-800 text-center mb-3">
        {title}
      </Text>
      
      <Text className="text-gray-600 text-center mb-8 leading-6">
        {message}
      </Text>
      
      {onAction && (
        <Pressable
          onPress={onAction}
          className="bg-gradient-to-r from-green-600 to-emerald-600 px-8 py-4 rounded-xl flex-row items-center shadow-lg"
        >
          <Text className="text-white font-semibold mr-2">{actionText}</Text>
          <ArrowRight size={18} color="white" />
        </Pressable>
      )}
    </View>
  );
}

export function SuccessCard({
  title,
  message,
  onPress,
}: {
  title: string;
  message: string;
  onPress?: () => void;
}) {
  return (
    <Pressable
      onPress={onPress}
      className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-2xl p-6 shadow-lg"
    >
      <View className="flex-row items-center">
        <View className="w-12 h-12 bg-green-100 rounded-full items-center justify-center mr-4">
          <CheckCircle size={24} color="#10b981" />
        </View>
        <View className="flex-1">
          <Text className="text-lg font-semibold text-gray-800 mb-1">
            {title}
          </Text>
          <Text className="text-gray-600 text-sm">
            {message}
          </Text>
        </View>
        {onPress && (
          <ArrowRight size={20} color="#10b981" />
        )}
      </View>
    </Pressable>
  );
}

export function SuccessAnimation() {
  return (
    <View className="flex-1 justify-center items-center">
      <LinearGradient
        colors={['#10b981', '#059669']}
        className="w-32 h-32 rounded-full items-center justify-center mb-6 shadow-lg"
      >
        <Sparkles size={48} color="white" />
      </LinearGradient>
      
      <Text className="text-xl font-bold text-gray-800 text-center mb-2">
        Hoàn thành!
      </Text>
      
      <Text className="text-gray-600 text-center">
        Dữ liệu đã được lưu thành công
      </Text>
    </View>
  );
} 