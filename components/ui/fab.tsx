import React from "react";
import { View, Pressable, Text } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { Plus, Camera, FileText, Check } from "lucide-react-native";

interface FABProps {
  onPress: () => void;
  icon?: React.ComponentType<{ size: number; color: string }>;
  label?: string;
  size?: "small" | "medium" | "large";
  position?: "bottom-right" | "bottom-center" | "bottom-left";
  variant?: "primary" | "secondary" | "success";
}

export function FAB({
  onPress,
  icon: Icon = Plus,
  label,
  size = "medium",
  position = "bottom-right",
  variant = "primary",
}: FABProps) {
  const getSize = () => {
    switch (size) {
      case "small":
        return "w-12 h-12";
      case "large":
        return "w-16 h-16";
      default:
        return "w-14 h-14";
    }
  };

  const getIconSize = () => {
    switch (size) {
      case "small":
        return 20;
      case "large":
        return 28;
      default:
        return 24;
    }
  };

  const getGradient = () => {
    switch (variant) {
      case "secondary":
        return ['#6b7280', '#4b5563'];
      case "success":
        return ['#10b981', '#059669'];
      default:
        return ['#3b82f6', '#2563eb'];
    }
  };

  const getPosition = () => {
    switch (position) {
      case "bottom-center":
        return "bottom-6 left-1/2 transform -translate-x-1/2";
      case "bottom-left":
        return "bottom-6 left-6";
      default:
        return "bottom-6 right-6";
    }
  };

  return (
    <View className={`absolute ${getPosition()} z-50`}>
      <Pressable
        onPress={onPress}
        className={`${getSize()} rounded-full shadow-lg`}
        style={{
          elevation: 8,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.3,
          shadowRadius: 8,
        }}
      >
        <LinearGradient
          colors={getGradient()}
          className={`${getSize()} rounded-full items-center justify-center`}
        >
          <Icon size={getIconSize()} color="white" />
        </LinearGradient>
      </Pressable>
      
      {label && (
        <View className="absolute -top-2 -right-2 bg-white rounded-full px-3 py-1 shadow-sm border border-gray-200">
          <Text className="text-xs font-medium text-gray-700">{label}</Text>
        </View>
      )}
    </View>
  );
}

interface FABGroupProps {
  children: React.ReactNode;
  isOpen?: boolean;
  onToggle?: () => void;
}

export function FABGroup({ children, isOpen = false, onToggle }: FABGroupProps) {
  return (
    <View className="absolute bottom-6 right-6 z-50">
      {isOpen && (
        <View className="absolute bottom-16 right-0 space-y-3">
          {children}
        </View>
      )}
      
      <Pressable
        onPress={onToggle}
        className="w-14 h-14 rounded-full shadow-lg"
        style={{
          elevation: 8,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.3,
          shadowRadius: 8,
        }}
      >
        <LinearGradient
          colors={['#3b82f6', '#2563eb']}
          className="w-14 h-14 rounded-full items-center justify-center"
        >
          <Plus 
            size={24} 
            color="white" 
            style={{
              transform: [{ rotate: isOpen ? '45deg' : '0deg' }],
            }}
          />
        </LinearGradient>
      </Pressable>
    </View>
  );
}

export function FABItem({
  onPress,
  icon: Icon,
  label,
  variant = "primary",
}: {
  onPress: () => void;
  icon: React.ComponentType<{ size: number; color: string }>;
  label: string;
  variant?: "primary" | "secondary" | "success";
}) {
  const getGradient = () => {
    switch (variant) {
      case "secondary":
        return ['#6b7280', '#4b5563'];
      case "success":
        return ['#10b981', '#059669'];
      default:
        return ['#3b82f6', '#2563eb'];
    }
  };

  return (
    <Pressable
      onPress={onPress}
      className="w-12 h-12 rounded-full shadow-lg"
      style={{
        elevation: 6,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 6,
      }}
    >
      <LinearGradient
        colors={getGradient()}
        className="w-12 h-12 rounded-full items-center justify-center"
      >
        <Icon size={20} color="white" />
      </LinearGradient>
      
      <View className="absolute -top-2 -right-2 bg-white rounded-full px-2 py-1 shadow-sm border border-gray-200">
        <Text className="text-xs font-medium text-gray-700">{label}</Text>
      </View>
    </Pressable>
  );
}

// Predefined FAB components
export function AddAuditFAB({ onPress }: { onPress: () => void }) {
  return (
    <FAB
      onPress={onPress}
      icon={Plus}
      label="Tạo audit"
      size="large"
    />
  );
}

export function CameraFAB({ onPress }: { onPress: () => void }) {
  return (
    <FAB
      onPress={onPress}
      icon={Camera}
      label="Chụp ảnh"
      variant="success"
    />
  );
}

export function SubmitFAB({ onPress }: { onPress: () => void }) {
  return (
    <FAB
      onPress={onPress}
      icon={Check}
      label="Gửi"
      variant="success"
      size="large"
    />
  );
} 