import React from "react";
import { View, Text, ActivityIndicator } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { Loader2, Sparkles } from "lucide-react-native";

interface LoadingProps {
  message?: string;
  size?: "small" | "large";
  showIcon?: boolean;
}

export function Loading({
  message = "Đang tải...",
  size = "large",
  showIcon = true,
}: LoadingProps) {
  return (
    <View className="flex-1 justify-center items-center">
      <LinearGradient
        colors={['#3b82f6', '#8b5cf6']}
        className="w-20 h-20 rounded-full items-center justify-center mb-4 shadow-lg"
      >
        {showIcon ? (
          <Sparkles size={32} color="white" />
        ) : (
          <ActivityIndicator size={size === "large" ? "large" : "small"} color="white" />
        )}
      </LinearGradient>
      
      <Text className="text-gray-700 text-lg font-semibold mb-2">
        {message}
      </Text>
      
      <View className="flex-row items-center">
        <Loader2 size={16} color="#6b7280" className="animate-spin" />
        <Text className="text-gray-500 text-sm ml-2">
          Vui lòng chờ trong giây lát
        </Text>
      </View>
    </View>
  );
}

export function LoadingCard() {
  return (
    <View className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
      <View className="flex-row items-center mb-4">
        <View className="w-12 h-12 bg-gray-200 rounded-full mr-4 animate-pulse" />
        <View className="flex-1">
          <View className="h-4 bg-gray-200 rounded mb-2 animate-pulse" />
          <View className="h-3 bg-gray-200 rounded w-2/3 animate-pulse" />
        </View>
      </View>
      
      <View className="space-y-2">
        <View className="h-3 bg-gray-200 rounded animate-pulse" />
        <View className="h-3 bg-gray-200 rounded w-4/5 animate-pulse" />
        <View className="h-3 bg-gray-200 rounded w-3/5 animate-pulse" />
      </View>
    </View>
  );
}

export function LoadingList({ count = 3 }: { count?: number }) {
  return (
    <View className="space-y-4">
      {Array.from({ length: count }).map((_, index) => (
        <LoadingCard key={index} />
      ))}
    </View>
  );
} 