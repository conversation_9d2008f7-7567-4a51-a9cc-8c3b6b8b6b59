import React from "react";
import { View, Text, Pressable } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { AlertCircle, RefreshCw, Wifi, WifiOff } from "lucide-react-native";

interface ErrorProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  type?: "network" | "server" | "general";
  showRetry?: boolean;
}

export function Error({
  title = "Đã xảy ra lỗi",
  message = "Không thể tải dữ liệu. Vui lòng thử lại.",
  onRetry,
  type = "general",
  showRetry = true,
}: ErrorProps) {
  const getIcon = () => {
    switch (type) {
      case "network":
        return <WifiOff size={48} color="#ef4444" />;
      case "server":
        return <AlertCircle size={48} color="#f59e0b" />;
      default:
        return <AlertCircle size={48} color="#ef4444" />;
    }
  };

  const getGradientColors = (): [string, string] => {
    switch (type) {
      case "network":
        return ['#fef2f2', '#fee2e2'];
      case "server":
        return ['#fffbeb', '#fef3c7'];
      default:
        return ['#fef2f2', '#fee2e2'];
    }
  };

  const getTitleColor = () => {
    switch (type) {
      case "network":
        return "text-red-800";
      case "server":
        return "text-amber-800";
      default:
        return "text-red-800";
    }
  };

  return (
    <View className="flex-1 justify-center items-center px-6">
      <LinearGradient
        colors={getGradientColors()}
        className="w-24 h-24 rounded-full items-center justify-center mb-6 shadow-lg"
      >
        {getIcon()}
      </LinearGradient>
      
      <Text className={`text-xl font-bold text-center mb-3 ${getTitleColor()}`}>
        {title}
      </Text>
      
      <Text className="text-gray-600 text-center mb-8 leading-6">
        {message}
      </Text>
      
      {showRetry && onRetry && (
        <Pressable
          onPress={onRetry}
          className="bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-4 rounded-xl flex-row items-center shadow-lg"
        >
          <RefreshCw size={18} color="white" />
          <Text className="text-white font-semibold ml-2">Thử lại</Text>
        </Pressable>
      )}
    </View>
  );
}

export function NetworkError({ onRetry }: { onRetry?: () => void }) {
  return (
    <Error
      title="Lỗi kết nối mạng"
      message="Không thể kết nối đến máy chủ. Vui lòng kiểm tra kết nối internet của bạn và thử lại."
      type="network"
      onRetry={onRetry}
    />
  );
}

export function ServerError({ onRetry }: { onRetry?: () => void }) {
  return (
    <Error
      title="Lỗi máy chủ"
      message="Máy chủ đang gặp sự cố. Vui lòng thử lại sau vài phút."
      type="server"
      onRetry={onRetry}
    />
  );
}

export function EmptyState({
  title = "Không có dữ liệu",
  message = "Hiện tại không có dữ liệu nào để hiển thị.",
  icon: Icon = AlertCircle,
}: {
  title?: string;
  message?: string;
  icon?: React.ComponentType<{ size: number; color: string }>;
}) {
  return (
    <View className="flex-1 justify-center items-center px-6">
      <View className="w-24 h-24 bg-gray-100 rounded-full items-center justify-center mb-6">
        <Icon size={48} color="#6b7280" />
      </View>
      
      <Text className="text-xl font-bold text-gray-800 text-center mb-3">
        {title}
      </Text>
      
      <Text className="text-gray-600 text-center leading-6">
        {message}
      </Text>
    </View>
  );
} 