import React, { useState } from "react";
import { View, Text, Alert } from "react-native";
import { Button } from "~/components/ui/button";
import { UserAnswerService } from "~/src/api/userAnswers";
import { PlanService } from "~/src/api/plans";
import { useAuth } from "~/src/hooks/useAuth";
import { useUserPlans } from "~/src/hooks/usePlans";

export function UserAnswerTest() {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const { data: plansResponse } = useUserPlans();
  const plans = plansResponse?.data || [];

  const testSaveUserAnswer = async () => {
    if (!user?.id) {
      Alert.alert("Error", "User not authenticated");
      return;
    }

    setIsLoading(true);

    try {
      // Test data - replace with actual values from your system
      const testAnswerData = {
        text_value: "Test answer",
        merchandiser: parseInt(user.id),
        plan: 1, // Replace with actual plan ID
        question: 1, // Replace with actual question ID
      };

      console.log("Testing user answer save with data:", testAnswerData);

      const result = await UserAnswerService.saveUserAnswer(testAnswerData);

      console.log("Save result:", result);
      Alert.alert("Success", "User answer saved successfully!");
    } catch (error: any) {
      console.error("Test failed:", error);
      Alert.alert("Error", error.message || "Failed to save user answer");
    } finally {
      setIsLoading(false);
    }
  };

  const testGetUserAnswers = async () => {
    if (plans.length === 0) {
      Alert.alert("Error", "No plans available to test with");
      return;
    }

    setIsLoading(true);

    try {
      const firstPlan = plans[0];

      if (!firstPlan.documentId) {
        Alert.alert("Error", "Plan documentId not found");
        return;
      }

      const answers = await UserAnswerService.getUserAnswersByPlan(
        firstPlan.documentId
      );

      console.log("Retrieved user answers:", answers);
      Alert.alert("Success", `Retrieved ${answers.length} user answers`);
    } catch (error: any) {
      console.error("Get test failed:", error);
      Alert.alert("Error", error.message || "Failed to get user answers");
    } finally {
      setIsLoading(false);
    }
  };

  const listAllPlans = () => {
    console.log("=== ALL AVAILABLE PLANS ===");
    plans.forEach((plan, index) => {
      console.log(`Plan ${index + 1}:`, {
        id: plan.id,
        idType: typeof plan.id,
        documentId: plan.documentId,
        documentIdType: typeof plan.documentId,
        title: plan.plan_title,
        status: plan.plan_status,
        store: plan.store?.store_name,
      });
    });
    console.log("=== END PLANS LIST ===");

    Alert.alert(
      "Plans Listed",
      `${plans.length} plans logged to console. Check console for details.`
    );
  };

  const testUpdatePlanStatus = async () => {
    if (plans.length === 0) {
      Alert.alert("Error", "No plans available to test with");
      return;
    }

    setIsLoading(true);

    try {
      const firstPlan = plans[0];
      console.log("Testing with plan:", firstPlan);

      const result = await PlanService.updatePlanStatus(
        firstPlan.id,
        "completed"
      );

      console.log("Plan status update result:", result);
      Alert.alert("Success", "Plan status updated to completed!");
    } catch (error: any) {
      console.error("Plan status update test failed:", error);
      Alert.alert("Error", error.message || "Failed to update plan status");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View className="p-4 space-y-4">
      <Text className="text-lg font-bold">User Answer API Test</Text>

      <Text className="text-sm text-gray-600">
        Available plans: {plans.length}
        {plans.length > 0 && ` (First plan ID: ${plans[0].id})`}
      </Text>

      <Button
        onPress={testSaveUserAnswer}
        disabled={isLoading}
        className="mb-2"
      >
        <Text className="text-white">Test Save User Answer</Text>
      </Button>

      <Button
        onPress={testGetUserAnswers}
        disabled={isLoading}
        variant="outline"
        className="mb-2"
      >
        <Text className="text-blue-600">Test Get User Answers</Text>
      </Button>

      <Button
        onPress={testUpdatePlanStatus}
        disabled={isLoading}
        variant="outline"
        className="mb-2"
      >
        <Text className="text-green-600">Test Update Plan Status</Text>
      </Button>

      <Button onPress={listAllPlans} disabled={isLoading} variant="outline">
        <Text className="text-purple-600">List All Plans (Console)</Text>
      </Button>

      {isLoading && (
        <Text className="text-gray-500 text-center">Loading...</Text>
      )}
    </View>
  );
}

export default UserAnswerTest;
