import React, { useState } from "react";
import { View, Text, TouchableOpacity, Alert, ScrollView } from "react-native";
import { UserAnswerService, CreateUserAnswerData } from "../../api/userAnswers";

/**
 * Debug component to test user answer relation format
 */
export const UserAnswerRelationTest: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [lastResult, setLastResult] = useState<string>("");

  const testUserAnswerCreation = async () => {
    setIsLoading(true);
    setLastResult("");

    try {
      // Test data with different ID formats
      const testAnswerData: CreateUserAnswerData = {
        merchandiser: 1, // User ID (numeric)
        plan: "plan_documentId_123", // Plan documentId (string)
        question: "question_documentId_456", // Question documentId (string)
        text_value: "Test answer for relation debugging",
      };

      console.log("Testing user answer creation with data:", testAnswerData);

      const result = await UserAnswerService.saveUserAnswer(testAnswerData);

      setLastResult(`✅ SUCCESS: User answer created with ID ${result.id}`);
      console.log("User answer creation successful:", result);

      Alert.alert(
        "Success",
        `User answer created successfully!\nID: ${result.id}\nPlan: ${result.plan?.id}\nQuestion: ${result.question?.id}`
      );
    } catch (error: any) {
      const errorMessage = error?.message || "Unknown error";
      setLastResult(`❌ ERROR: ${errorMessage}`);
      console.error("User answer creation failed:", error);

      Alert.alert(
        "Error",
        `Failed to create user answer:\n${errorMessage}\n\nCheck console for details.`
      );
    } finally {
      setIsLoading(false);
    }
  };

  const testWithNumericIds = async () => {
    setIsLoading(true);
    setLastResult("");

    try {
      // Test data with numeric IDs
      const testAnswerData: CreateUserAnswerData = {
        merchandiser: 1, // User ID (numeric)
        plan: 1, // Plan ID (numeric)
        question: 1, // Question ID (numeric)
        text_value: "Test answer with numeric IDs",
      };

      console.log(
        "Testing user answer creation with numeric IDs:",
        testAnswerData
      );

      const result = await UserAnswerService.saveUserAnswer(testAnswerData);

      setLastResult(
        `✅ SUCCESS (Numeric): User answer created with ID ${result.id}`
      );
      console.log("User answer creation successful (numeric):", result);

      Alert.alert(
        "Success",
        `User answer created successfully with numeric IDs!\nID: ${result.id}`
      );
    } catch (error: any) {
      const errorMessage = error?.message || "Unknown error";
      setLastResult(`❌ ERROR (Numeric): ${errorMessage}`);
      console.error("User answer creation failed (numeric):", error);

      Alert.alert(
        "Error",
        `Failed to create user answer with numeric IDs:\n${errorMessage}`
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ScrollView className="flex-1 p-4 bg-gray-50">
      <Text className="text-xl font-bold mb-4 text-center">
        User Answer Relation Test
      </Text>

      <Text className="text-sm text-gray-600 mb-4">
        This component tests the user answer creation with different relation
        formats to debug Strapi v5 relation issues.
      </Text>

      <View className="space-y-4">
        <TouchableOpacity
          onPress={testUserAnswerCreation}
          disabled={isLoading}
          className={`p-4 rounded-lg ${
            isLoading ? "bg-gray-300" : "bg-blue-500"
          }`}
        >
          <Text className="text-white text-center font-semibold">
            {isLoading ? "Testing..." : "Test with DocumentId (String)"}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={testWithNumericIds}
          disabled={isLoading}
          className={`p-4 rounded-lg ${
            isLoading ? "bg-gray-300" : "bg-green-500"
          }`}
        >
          <Text className="text-white text-center font-semibold">
            {isLoading ? "Testing..." : "Test with Numeric IDs"}
          </Text>
        </TouchableOpacity>
      </View>

      {lastResult && (
        <View className="mt-6 p-4 bg-white rounded-lg border">
          <Text className="font-semibold mb-2">Last Result:</Text>
          <Text className="text-sm">{lastResult}</Text>
        </View>
      )}

      <View className="mt-6 p-4 bg-yellow-50 rounded-lg border border-yellow-200">
        <Text className="font-semibold text-yellow-800 mb-2">Debug Info:</Text>
        <Text className="text-xs text-yellow-700">
          • Check console logs for detailed API requests{"\n"}• Check network
          tab for actual payload sent to Strapi{"\n"}• Verify relation format:
          connect: [documentId] vs numeric ID{"\n"}• Test both string documentId
          and numeric ID formats
        </Text>
      </View>
    </ScrollView>
  );
};
