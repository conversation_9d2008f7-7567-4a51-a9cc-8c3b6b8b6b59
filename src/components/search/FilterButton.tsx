import React from "react";
import { Pressable, Text, View } from "react-native";
import { Filter } from "lucide-react-native";
import { useColorScheme } from "~/lib/useColorScheme";

interface FilterButtonProps {
  onPress: () => void;
  activeFilterCount: number;
}

export function FilterButton({ onPress, activeFilterCount }: FilterButtonProps) {
  const { isDarkColorScheme } = useColorScheme();

  return (
    <Pressable
      onPress={onPress}
      className={`flex-row items-center justify-center px-4 py-3 rounded-xl border ${
        activeFilterCount > 0
          ? isDarkColorScheme
            ? "bg-blue-900/30 border-blue-500"
            : "bg-blue-50 border-blue-200"
          : isDarkColorScheme
          ? "bg-gray-800 border-gray-700"
          : "bg-white border-gray-200"
      } shadow-sm`}
      style={{ minWidth: 60 }}
    >
      <Filter
        size={20}
        color={
          activeFilterCount > 0
            ? isDarkColorScheme
              ? "#60A5FA"
              : "#3B82F6"
            : isDarkColorScheme
            ? "#9CA3AF"
            : "#6B7280"
        }
      />
      {activeFilterCount > 0 && (
        <View className="ml-2 flex-row items-center">
          <View className="w-5 h-5 bg-blue-500 rounded-full items-center justify-center">
            <Text className="text-white text-xs font-bold">
              {activeFilterCount > 9 ? "9+" : activeFilterCount}
            </Text>
          </View>
        </View>
      )}
    </Pressable>
  );
}
