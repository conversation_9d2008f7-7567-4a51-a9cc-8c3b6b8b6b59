import React from "react";
import {
  View,
  Text,
  Modal,
  Pressable,
  ScrollView,
  Dimensions,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { X, Filter, RotateCcw } from "lucide-react-native";
import { But<PERSON> } from "~/components/ui/button";
import { useColorScheme } from "~/lib/useColorScheme";
import { StatusFilter } from "./filters/StatusFilter";
import { DateRangeFilter } from "./filters/DateRangeFilter";

const { height: screenHeight } = Dimensions.get("window");

export interface FilterValues {
  search: string;
  status: string[];
  dateRange: {
    startDate?: Date;
    endDate?: Date;
  };
  assignedDateRange: {
    startDate?: Date;
    endDate?: Date;
  };
  stores: string[];
  merchandisers: string[];
}

interface FilterSheetProps {
  visible: boolean;
  onClose: () => void;
  filters: FilterValues;
  onFiltersChange: (filters: FilterValues) => void;
  onApplyFilters: () => void;
  onClearFilters: () => void;
  activeFilterCount: number;
}

export function FilterSheet({
  visible,
  onClose,
  filters,
  onFiltersChange,
  onApplyFilters,
  onClearFilters,
  activeFilterCount,
}: FilterSheetProps) {
  const { isDarkColorScheme } = useColorScheme();

  const handleApply = () => {
    onApplyFilters();
    onClose();
  };

  const handleClear = () => {
    onClearFilters();
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView
        className={`flex-1 ${isDarkColorScheme ? "bg-gray-900" : "bg-gray-50"}`}
      >
        {/* Header */}
        <View
          className={`flex-row items-center justify-between px-4 py-4 border-b ${
            isDarkColorScheme
              ? "bg-gray-800 border-gray-700"
              : "bg-white border-gray-200"
          }`}
        >
          <View className="flex-row items-center">
            <Filter
              size={24}
              color={isDarkColorScheme ? "#F3F4F6" : "#1F2937"}
              className="mr-2"
            />
            <Text
              className={`text-xl font-semibold ${
                isDarkColorScheme ? "text-white" : "text-gray-900"
              }`}
            >
              Bộ lọc
            </Text>
            {activeFilterCount > 0 && (
              <View className="ml-2 px-2 py-1 bg-blue-500 rounded-full">
                <Text className="text-white text-xs font-medium">
                  {activeFilterCount}
                </Text>
              </View>
            )}
          </View>
          <Pressable
            onPress={onClose}
            className="p-2 rounded-full"
            hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
          >
            <X size={24} color={isDarkColorScheme ? "#F3F4F6" : "#1F2937"} />
          </Pressable>
        </View>

        {/* Filter Content */}
        <ScrollView
          className="flex-1 px-4"
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 100 }}
        >
          <View className="py-4">
            {/* Status Filter */}
            <StatusFilter
              selectedStatuses={filters.status}
              onStatusChange={(status) =>
                onFiltersChange({ ...filters, status })
              }
            />

            {/* Date Range Filter */}
            <DateRangeFilter
              title="Thời gian thực hiện"
              dateRange={filters.dateRange}
              onDateRangeChange={(dateRange) =>
                onFiltersChange({ ...filters, dateRange })
              }
            />

            {/* Assigned Date Range Filter */}
            <DateRangeFilter
              title="Ngày được giao"
              dateRange={filters.assignedDateRange}
              onDateRangeChange={(assignedDateRange) =>
                onFiltersChange({ ...filters, assignedDateRange })
              }
            />
          </View>
        </ScrollView>

        {/* Bottom Actions */}
        <View
          className={`px-4 py-4 border-t ${
            isDarkColorScheme
              ? "bg-gray-800 border-gray-700"
              : "bg-white border-gray-200"
          }`}
        >
          <View className="flex-row space-x-3">
            <Button
              variant="outline"
              onPress={handleClear}
              className="flex-1 flex-row items-center justify-center"
              disabled={activeFilterCount === 0}
            >
              <RotateCcw size={16} className="mr-2" />
              <Text className="font-medium">Xóa tất cả</Text>
            </Button>
            <Button
              onPress={handleApply}
              className="flex-1 flex-row items-center justify-center"
            >
              <Filter size={16} className="mr-2" />
              <Text className="text-white font-medium">Áp dụng</Text>
            </Button>
          </View>
        </View>
      </SafeAreaView>
    </Modal>
  );
}
