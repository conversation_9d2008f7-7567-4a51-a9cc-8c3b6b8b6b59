import React, { useState, useEffect } from "react";
import { View, TextInput, Pressable } from "react-native";
import { Search, X } from "lucide-react-native";
import { useColorScheme } from "~/lib/useColorScheme";

interface SearchBarProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  onClear?: () => void;
  debounceMs?: number;
}

export function SearchBar({
  value,
  onChangeText,
  placeholder = "Tìm kiếm kế hoạch...",
  onClear,
  debounceMs = 300,
}: SearchBarProps) {
  const { isDarkColorScheme } = useColorScheme();
  const [localValue, setLocalValue] = useState(value);

  // Debounce the search input
  useEffect(() => {
    const timer = setTimeout(() => {
      onChangeText(localValue);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [localValue, onChangeText, debounceMs]);

  // Update local value when external value changes
  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  const handleClear = () => {
    setLocalValue("");
    onChangeText("");
    onClear?.();
  };

  return (
    <View>
      <View
        className={`flex-row items-center px-4 py-3 rounded-xl border ${
          isDarkColorScheme
            ? "bg-gray-800 border-gray-700"
            : "bg-white border-gray-200"
        } shadow-sm`}
      >
        <Search
          size={20}
          color={isDarkColorScheme ? "#9CA3AF" : "#6B7280"}
          className="mr-3"
        />
        <TextInput
          value={localValue}
          onChangeText={setLocalValue}
          placeholder={placeholder}
          placeholderTextColor={isDarkColorScheme ? "#9CA3AF" : "#6B7280"}
          className={`flex-1 text-base ${
            isDarkColorScheme ? "text-white" : "text-gray-900"
          }`}
          autoCapitalize="none"
          autoCorrect={false}
          returnKeyType="search"
        />
        {localValue.length > 0 && (
          <Pressable
            onPress={handleClear}
            className="ml-2 p-1 rounded-full"
            hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
          >
            <X size={18} color={isDarkColorScheme ? "#9CA3AF" : "#6B7280"} />
          </Pressable>
        )}
      </View>
    </View>
  );
}
