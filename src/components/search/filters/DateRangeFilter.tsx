import React from "react";
import { View, Text, Pressable } from "react-native";
import { Calendar, X } from "lucide-react-native";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import { useColorScheme } from "~/lib/useColorScheme";
import { DateTimePicker } from "~/src/components/ui/DateTimePicker";

interface DateRange {
  startDate?: Date;
  endDate?: Date;
}

interface DateRangeFilterProps {
  title: string;
  dateRange: DateRange;
  onDateRangeChange: (range: DateRange) => void;
}

export function DateRangeFilter({
  title,
  dateRange,
  onDateRangeChange,
}: DateRangeFilterProps) {
  const { isDarkColorScheme } = useColorScheme();

  const handleStartDateChange = (date: Date | undefined) => {
    onDateRangeChange({
      ...dateRange,
      startDate: date,
    });
  };

  const handleEndDateChange = (date: Date | undefined) => {
    onDateRangeChange({
      ...dateRange,
      endDate: date,
    });
  };

  const clearDateRange = () => {
    onDateRangeChange({
      startDate: undefined,
      endDate: undefined,
    });
  };

  const hasDateRange = dateRange.startDate || dateRange.endDate;

  return (
    <View className="mb-6">
      <View className="flex-row items-center justify-between mb-3">
        <Text
          className={`text-lg font-semibold ${
            isDarkColorScheme ? "text-white" : "text-gray-900"
          }`}
        >
          {title}
        </Text>
        {hasDateRange && (
          <Pressable
            onPress={clearDateRange}
            className="p-1 rounded-full"
            hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
          >
            <X size={18} color={isDarkColorScheme ? "#9CA3AF" : "#6B7280"} />
          </Pressable>
        )}
      </View>

      <View className="space-y-3">
        {/* Start Date */}
        <View>
          <Text
            className={`text-sm font-medium mb-2 ${
              isDarkColorScheme ? "text-gray-300" : "text-gray-700"
            }`}
          >
            Từ ngày
          </Text>
          <DateTimePicker
            date={dateRange.startDate}
            setDate={handleStartDateChange}
            placeholder="Chọn ngày bắt đầu"
          />
        </View>

        {/* End Date */}
        <View>
          <Text
            className={`text-sm font-medium mb-2 ${
              isDarkColorScheme ? "text-gray-300" : "text-gray-700"
            }`}
          >
            Đến ngày
          </Text>
          <DateTimePicker
            date={dateRange.endDate}
            setDate={handleEndDateChange}
            placeholder="Chọn ngày kết thúc"
          />
        </View>

        {/* Date Range Summary */}
        {hasDateRange && (
          <View
            className={`p-3 rounded-lg border ${
              isDarkColorScheme
                ? "bg-blue-900/20 border-blue-700"
                : "bg-blue-50 border-blue-200"
            }`}
          >
            <View className="flex-row items-center">
              <Calendar
                size={16}
                color={isDarkColorScheme ? "#60A5FA" : "#3B82F6"}
                className="mr-2"
              />
              <Text
                className={`text-sm font-medium ${
                  isDarkColorScheme ? "text-blue-300" : "text-blue-700"
                }`}
              >
                {dateRange.startDate && dateRange.endDate
                  ? `${format(dateRange.startDate, "dd/MM/yyyy", {
                      locale: vi,
                    })} - ${format(dateRange.endDate, "dd/MM/yyyy", {
                      locale: vi,
                    })}`
                  : dateRange.startDate
                  ? `Từ ${format(dateRange.startDate, "dd/MM/yyyy", {
                      locale: vi,
                    })}`
                  : dateRange.endDate
                  ? `Đến ${format(dateRange.endDate, "dd/MM/yyyy", {
                      locale: vi,
                    })}`
                  : ""}
              </Text>
            </View>
          </View>
        )}
      </View>
    </View>
  );
}
