import React from "react";
import { View, Text, Pressable } from "react-native";
import { CheckCircle, Clock, Circle, AlertCircle } from "lucide-react-native";
import { useColorScheme } from "~/lib/useColorScheme";

interface StatusOption {
  value: string;
  label: string;
  icon: React.ComponentType<{ size: number; color: string }>;
  color: string;
}

const statusOptions: StatusOption[] = [
  {
    value: "pending",
    label: "Chờ xử lý",
    icon: Clock,
    color: "#F59E0B",
  },
  {
    value: "in_progress",
    label: "<PERSON>ang thực hiện",
    icon: Circle,
    color: "#3B82F6",
  },
  {
    value: "completed",
    label: "<PERSON><PERSON>n thành",
    icon: CheckCircle,
    color: "#10B981",
  },
  {
    value: "expired",
    label: "Hết hạn",
    icon: AlertCircle,
    color: "#EF4444",
  },
];

interface StatusFilterProps {
  selectedStatuses: string[];
  onStatusChange: (statuses: string[]) => void;
}

export function StatusFilter({
  selectedStatuses,
  onStatusChange,
}: StatusFilterProps) {
  const { isDarkColorScheme } = useColorScheme();

  const toggleStatus = (status: string) => {
    if (selectedStatuses.includes(status)) {
      onStatusChange(selectedStatuses.filter((s) => s !== status));
    } else {
      onStatusChange([...selectedStatuses, status]);
    }
  };

  return (
    <View className="mb-6">
      <Text
        className={`text-lg font-semibold mb-3 ${
          isDarkColorScheme ? "text-white" : "text-gray-900"
        }`}
      >
        Trạng thái
      </Text>
      <View className="space-y-2">
        {statusOptions.map((option) => {
          const isSelected = selectedStatuses.includes(option.value);
          const IconComponent = option.icon;

          return (
            <Pressable
              key={option.value}
              onPress={() => toggleStatus(option.value)}
              className={`flex-row items-center p-3 rounded-lg border ${
                isSelected
                  ? isDarkColorScheme
                    ? "bg-blue-900/30 border-blue-500"
                    : "bg-blue-50 border-blue-200"
                  : isDarkColorScheme
                  ? "bg-gray-800 border-gray-700"
                  : "bg-white border-gray-200"
              }`}
            >
              <View
                className={`w-5 h-5 rounded border-2 mr-3 items-center justify-center ${
                  isSelected
                    ? "bg-blue-500 border-blue-500"
                    : isDarkColorScheme
                    ? "border-gray-600"
                    : "border-gray-300"
                }`}
              >
                {isSelected && (
                  <View className="w-2 h-2 bg-white rounded-full" />
                )}
              </View>
              <IconComponent
                size={20}
                color={option.color}
                className="mr-3"
              />
              <Text
                className={`flex-1 font-medium ${
                  isDarkColorScheme ? "text-white" : "text-gray-900"
                }`}
              >
                {option.label}
              </Text>
            </Pressable>
          );
        })}
      </View>
    </View>
  );
}
