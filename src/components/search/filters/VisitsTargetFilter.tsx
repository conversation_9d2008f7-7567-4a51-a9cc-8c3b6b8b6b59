import React, { useState } from "react";
import { View, Text, TextInput, Pressable } from "react-native";
import { Target, X } from "lucide-react-native";
import { useColorScheme } from "~/lib/useColorScheme";

interface VisitsTargetRange {
  min?: number;
  max?: number;
}

interface VisitsTargetFilterProps {
  visitsTarget: VisitsTargetRange;
  onVisitsTargetChange: (range: VisitsTargetRange) => void;
}

const presetRanges = [
  { label: "1-5 lần", min: 1, max: 5 },
  { label: "6-10 lần", min: 6, max: 10 },
  { label: "11-20 lần", min: 11, max: 20 },
  { label: "20+ lần", min: 21, max: undefined },
];

export function VisitsTargetFilter({
  visitsTarget,
  onVisitsTargetChange,
}: VisitsTargetFilterProps) {
  const { isDarkColorScheme } = useColorScheme();
  const [minValue, setMinValue] = useState(
    visitsTarget.min?.toString() || ""
  );
  const [maxValue, setMaxValue] = useState(
    visitsTarget.max?.toString() || ""
  );

  const handleMinChange = (text: string) => {
    setMinValue(text);
    const num = parseInt(text) || undefined;
    onVisitsTargetChange({
      ...visitsTarget,
      min: num,
    });
  };

  const handleMaxChange = (text: string) => {
    setMaxValue(text);
    const num = parseInt(text) || undefined;
    onVisitsTargetChange({
      ...visitsTarget,
      max: num,
    });
  };

  const selectPreset = (preset: typeof presetRanges[0]) => {
    setMinValue(preset.min.toString());
    setMaxValue(preset.max?.toString() || "");
    onVisitsTargetChange({
      min: preset.min,
      max: preset.max,
    });
  };

  const clearRange = () => {
    setMinValue("");
    setMaxValue("");
    onVisitsTargetChange({
      min: undefined,
      max: undefined,
    });
  };

  const hasRange = visitsTarget.min || visitsTarget.max;

  return (
    <View className="mb-6">
      <View className="flex-row items-center justify-between mb-3">
        <Text
          className={`text-lg font-semibold ${
            isDarkColorScheme ? "text-white" : "text-gray-900"
          }`}
        >
          Số lần thăm
        </Text>
        {hasRange && (
          <Pressable
            onPress={clearRange}
            className="p-1 rounded-full"
            hitSlop={{ top: 8, bottom: 8, left: 8, right: 8 }}
          >
            <X
              size={18}
              color={isDarkColorScheme ? "#9CA3AF" : "#6B7280"}
            />
          </Pressable>
        )}
      </View>

      {/* Preset Ranges */}
      <View className="mb-4">
        <Text
          className={`text-sm font-medium mb-2 ${
            isDarkColorScheme ? "text-gray-300" : "text-gray-700"
          }`}
        >
          Lựa chọn nhanh
        </Text>
        <View className="flex-row flex-wrap gap-2">
          {presetRanges.map((preset, index) => (
            <Pressable
              key={index}
              onPress={() => selectPreset(preset)}
              className={`px-3 py-2 rounded-lg border ${
                isDarkColorScheme
                  ? "bg-gray-800 border-gray-700"
                  : "bg-white border-gray-200"
              }`}
            >
              <Text
                className={`text-sm font-medium ${
                  isDarkColorScheme ? "text-white" : "text-gray-900"
                }`}
              >
                {preset.label}
              </Text>
            </Pressable>
          ))}
        </View>
      </View>

      {/* Custom Range */}
      <View>
        <Text
          className={`text-sm font-medium mb-2 ${
            isDarkColorScheme ? "text-gray-300" : "text-gray-700"
          }`}
        >
          Tùy chỉnh
        </Text>
        <View className="flex-row items-center space-x-3">
          <View className="flex-1">
            <TextInput
              value={minValue}
              onChangeText={handleMinChange}
              placeholder="Tối thiểu"
              placeholderTextColor={isDarkColorScheme ? "#9CA3AF" : "#6B7280"}
              keyboardType="numeric"
              className={`p-3 rounded-lg border text-center ${
                isDarkColorScheme
                  ? "bg-gray-800 border-gray-700 text-white"
                  : "bg-white border-gray-200 text-gray-900"
              }`}
            />
          </View>
          <Text
            className={`text-lg font-medium ${
              isDarkColorScheme ? "text-gray-400" : "text-gray-600"
            }`}
          >
            -
          </Text>
          <View className="flex-1">
            <TextInput
              value={maxValue}
              onChangeText={handleMaxChange}
              placeholder="Tối đa"
              placeholderTextColor={isDarkColorScheme ? "#9CA3AF" : "#6B7280"}
              keyboardType="numeric"
              className={`p-3 rounded-lg border text-center ${
                isDarkColorScheme
                  ? "bg-gray-800 border-gray-700 text-white"
                  : "bg-white border-gray-200 text-gray-900"
              }`}
            />
          </View>
        </View>
      </View>

      {/* Range Summary */}
      {hasRange && (
        <View
          className={`mt-3 p-3 rounded-lg border ${
            isDarkColorScheme
              ? "bg-green-900/20 border-green-700"
              : "bg-green-50 border-green-200"
          }`}
        >
          <View className="flex-row items-center">
            <Target
              size={16}
              color={isDarkColorScheme ? "#34D399" : "#10B981"}
              className="mr-2"
            />
            <Text
              className={`text-sm font-medium ${
                isDarkColorScheme ? "text-green-300" : "text-green-700"
              }`}
            >
              {visitsTarget.min && visitsTarget.max
                ? `${visitsTarget.min} - ${visitsTarget.max} lần`
                : visitsTarget.min
                ? `Từ ${visitsTarget.min} lần`
                : visitsTarget.max
                ? `Tối đa ${visitsTarget.max} lần`
                : ""}
            </Text>
          </View>
        </View>
      )}
    </View>
  );
}
