import React from "react";
import { View, Text } from "react-native";
import { Search, Filter } from "lucide-react-native";
import { useColorScheme } from "~/lib/useColorScheme";

interface SearchResultsHeaderProps {
  resultCount: number;
  totalCount: number;
  searchTerm: string;
  activeFilterCount: number;
}

export function SearchResultsHeader({
  resultCount,
  totalCount,
  searchTerm,
  activeFilterCount,
}: SearchResultsHeaderProps) {
  const { isDarkColorScheme } = useColorScheme();

  const isFiltered = searchTerm.trim() || activeFilterCount > 0;

  if (!isFiltered) {
    return null;
  }

  return (
    <View className="mx-4 mb-3">
      <View
        className={`p-3 rounded-lg border ${
          isDarkColorScheme
            ? "bg-gray-800/50 border-gray-700"
            : "bg-gray-50 border-gray-200"
        }`}
      >
        <View className="flex-row items-center justify-between">
          <View className="flex-row items-center flex-1">
            {searchTerm.trim() ? (
              <Search
                size={16}
                color={isDarkColorScheme ? "#9CA3AF" : "#6B7280"}
                className="mr-2"
              />
            ) : (
              <Filter
                size={16}
                color={isDarkColorScheme ? "#9CA3AF" : "#6B7280"}
                className="mr-2"
              />
            )}
            <Text
              className={`text-sm font-medium ${
                isDarkColorScheme ? "text-gray-300" : "text-gray-700"
              }`}
            >
              {searchTerm.trim()
                ? `Tìm kiếm "${searchTerm}"`
                : `Đã áp dụng ${activeFilterCount} bộ lọc`}
            </Text>
          </View>
          <View
            className={`px-2 py-1 rounded-full ${
              resultCount === 0
                ? isDarkColorScheme
                  ? "bg-red-900/30"
                  : "bg-red-50"
                : isDarkColorScheme
                ? "bg-blue-900/30"
                : "bg-blue-50"
            }`}
          >
            <Text
              className={`text-xs font-semibold ${
                resultCount === 0
                  ? isDarkColorScheme
                    ? "text-red-300"
                    : "text-red-700"
                  : isDarkColorScheme
                  ? "text-blue-300"
                  : "text-blue-700"
              }`}
            >
              {resultCount}/{totalCount}
            </Text>
          </View>
        </View>
        
        {resultCount === 0 && (
          <Text
            className={`text-xs mt-1 ${
              isDarkColorScheme ? "text-gray-400" : "text-gray-600"
            }`}
          >
            Không tìm thấy kết quả phù hợp
          </Text>
        )}
      </View>
    </View>
  );
}
