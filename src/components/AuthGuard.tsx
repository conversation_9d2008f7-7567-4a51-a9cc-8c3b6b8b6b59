import React, { useEffect } from "react";
import { View, ActivityIndicator } from "react-native";
import { router, useSegments } from "expo-router";
import { useAuthContext } from "../providers/AuthProvider";
import { Text } from "~/components/ui/text";

/**
 * AuthGuard component to handle authentication routing
 * Redirects users based on authentication state
 */
export function AuthGuard({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, isLoading } = useAuthContext();
  const segments = useSegments();

  useEffect(() => {
    if (isLoading) return; // Don't redirect while checking auth

    const inAuthGroup = segments[0] === "(auth)";

    if (!isAuthenticated && !inAuthGroup) {
      // User is not authenticated and not in auth pages, redirect to login
      router.push("/(auth)/login");
    } else if (isAuthenticated && inAuthGroup) {
      // User is authenticated but on auth pages, redirect to home
      router.push("/(tabs)/home");
    }
  }, [isAuthenticated, isLoading, segments]);

  // Show loading screen while checking authentication
  if (isLoading) {
    return (
      <View className="flex-1 justify-center items-center bg-background">
        <ActivityIndicator size="large" />
        <Text className="mt-4 text-gray-600">Đang kiểm tra đăng nhập...</Text>
      </View>
    );
  }

  return <>{children}</>;
}
