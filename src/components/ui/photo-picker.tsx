import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Pressable,
  TouchableOpacity,
  Alert,
  Image,
  ActivityIndicator,
  Platform,
  Modal,
} from "react-native";
import { useCameraPermissions, CameraView, CameraType } from "expo-camera";
import { MaterialIcons } from "@expo/vector-icons";
import { SafeAreaView } from "react-native-safe-area-context";
import { Button } from "~/components/ui/button";
import { Text } from "~/components/ui/text";
import {
  X,
  Camera as CameraIcon,
  Image as ImageIcon,
  Upload,
  Trash2,
} from "lucide-react-native";
import {
  compressImage,
  CompressedImage,
  getOptimalCompressionSettings,
} from "~/src/utils/imageUtils";
import { ImageStorageManager, StoredImage } from "~/src/utils/imageStorage";
import {format} from 'date-fns'

interface PhotoPickerProps {
  value?: string[];
  onChange?: (photos: string[]) => void;
  maxPhotos?: number;
  placeholder?: string;
  questionId?: string; // For associating images with questions
  enableCompression?: boolean; // Enable automatic compression
  showPreview?: boolean; // Show image previews
  onImagesStored?: (storedImages: StoredImage[]) => void; // Callback when images are stored
}

const watermarkAndGenerateBlobImage = (file: File): Promise<{file: File, blob: string}> => {
  return new Promise((resolve) => {
    const reader = new FileReader();

    reader.onload = (e: ProgressEvent<FileReader>) => {
      const result = e.target?.result;
      if (!result || typeof result !== 'string') return;

      const img = new window.Image();

      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        canvas.width = img.width;
        canvas.height = img.height;

        ctx.drawImage(img, 0, 0);

        const watermarkText = format(new Date(), 'yyyy-MM-dd HH:mm:ss');
        ctx.font = `${img.width / 25}px Arial`;
        ctx.fillStyle = 'rgba(0, 0, 0, 1)';
        ctx.textAlign = 'left';
        ctx.textBaseline = 'top';
        ctx.fillText(watermarkText, 10, 10);

        canvas.toBlob((blob) => {
          if (!blob) return;

          const watermarkedFile = new File([blob], `watermarked-${file.name}`, {
            type: 'image/jpeg',
          });

          const previewUrl = URL.createObjectURL(blob);

          resolve({file: watermarkedFile, blob: previewUrl});
        }, 'image/jpeg');
      };

      img.src = result;
    };

    reader.readAsDataURL(file);
  });
};


export function PhotoPicker({
  value = [],
  onChange,
  maxPhotos = 5,
  placeholder = "Chụp ảnh hoặc chọn từ thư viện",
  questionId,
  enableCompression = true,
  showPreview = true,
  onImagesStored,
}: PhotoPickerProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [storedImages, setStoredImages] = useState<StoredImage[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null); // For web file input

  // expo-camera state
  const [permission, requestPermission] = useCameraPermissions();
  const cameraRef = useRef<CameraView>(null);
  const [showCamera, setShowCamera] = useState(false);
  const [facing, setFacing] = useState<CameraType>('back');
  const [previewUri, setPreviewUri] = useState<string | undefined>(undefined);
  const [previewSize, setPreviewSize] = useState<{ width: number; height: number } | undefined>(undefined);

  // Enhanced debug logging
  console.log("🎯 PhotoPicker RENDER:", {
    questionId,
    valueLength: value.length,
    maxPhotos,
    isProcessing,
    storedImagesCount: storedImages.length,
    platform: Platform.OS,
    fileInputRefExists: !!fileInputRef.current,
  });

  // Load stored images on mount
  useEffect(() => {
    if (questionId) {
      loadStoredImages();
    }
  }, [questionId]);

  const loadStoredImages = async () => {
    if (!questionId) return;

    try {
      const images = await ImageStorageManager.getQuestionImages(questionId);
      setStoredImages(images);
    } catch (error) {
      console.error("Error loading stored images:", error);
    }
  };

  // Web-specific file handling
  const handleWebFileSelect = async (files: FileList) => {
    console.log("🌐 Web file selection started:", files.length, "files");

    if (files.length === 0) return;

    if (value.length + files.length > maxPhotos) {
      alert(`Bạn chỉ có thể chọn tối đa ${maxPhotos} ảnh.`);
      return;
    }

    const fileArray = Array.from(files);
    const imageFiles = fileArray.filter((file) =>
      file.type.startsWith("image/")
    );

    if (imageFiles.length === 0) {
      alert("Vui lòng chọn file ảnh hợp lệ.");
      return;
    }

    console.log("🖼️ Processing", imageFiles.length, "image files");

    const results = await Promise.all(imageFiles.map(watermarkAndGenerateBlobImage));
    const imageUris: string[] = results.map((item) => item.blob);

    await processAndStoreImages(imageUris);
  };

  const handleWebFileInputChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    console.log("🌐 Web file input change event triggered");
    console.log("🌐 Question ID:", questionId);
    console.log("🌐 Current value length:", value.length);
    const files = event.target.files;
    console.log("🌐 Files selected:", files?.length || 0);

    if (files && files.length > 0) {
      console.log(
        "🌐 File details:",
        Array.from(files).map((f) => ({
          name: f.name,
          size: f.size,
          type: f.type,
        }))
      );
      handleWebFileSelect(files);
    } else {
      console.log("🌐 No files selected or files is null");
    }

    // Reset input value to allow selecting the same file again
    event.target.value = "";
  };
  const requestMediaLibraryPermission = async () => {
    // Web uses input element; native library picking is disabled (no ImagePicker)
    return Platform.OS === "web";
  };

  const requestCameraPermission = async () => {
    if (permission?.granted) return true;
    const res = await requestPermission();
    if (!res?.granted) {
      Alert.alert("Cần quyền camera", "Ứng dụng cần quyền camera để chụp ảnh.");
      return false;
    }
    return true;
  };

  const processAndStoreImages = async (imageUris: string[], skipCompression: boolean = false) => {
    console.log("🔄 processAndStoreImages called:", {
      questionId,
      imageUrisCount: imageUris.length,
      imageUris: imageUris.slice(0, 3), // Log first 3 URIs
      enableCompression,
    });

    if (!questionId) {
      console.log("⚠️ No questionId provided, using fallback behavior");
      // Fallback to original behavior if no questionId
      const updatedPhotos = [...value, ...imageUris].slice(0, maxPhotos);
      onChange?.(updatedPhotos);
      return;
    }

    setIsProcessing(true);
    console.log("🔄 Set isProcessing to true");

    try {
      const compressedImages: CompressedImage[] = [];
      console.log("🔄 Starting image compression/processing...");

      for (let i = 0; i < imageUris.length; i++) {
        const uri = imageUris[i];
        console.log(`🔄 Processing image ${i + 1}/${imageUris.length}:`, uri);

        if (enableCompression && !skipCompression) {
          console.log("🔄 Compression enabled, compressing image...");
          console.log("🔄 Original URI:", uri);
          console.log("🔄 Preview size before compression:", previewSize);

          // Get optimal compression settings
          const compressionSettings = getOptimalCompressionSettings(0); // We'll get actual size in compressImage
          console.log("🔄 Compression settings:", compressionSettings);

          const compressed = await compressImage(uri, compressionSettings);
          compressedImages.push(compressed);
          console.log("✅ Image compressed:", compressed.name, compressed.size, "dimensions:", compressed.width, "x", compressed.height);
        } else {
          console.log(
            "🔄 Compression disabled, creating CompressedImage object..."
          );
          // Create CompressedImage without compression
          const compressedImage = {
            uri,
            name: `image_${Date.now()}_${i}.jpg`,
            size: 0,
            type: "image/jpeg",
            width: previewSize?.width,
            height: previewSize?.height,
          };
          compressedImages.push(compressedImage);
          console.log("✅ CompressedImage created (no compression):", compressedImage.name, "dimensions:", compressedImage.width, "x", compressedImage.height);
        }
      }

      console.log("🔄 All images processed, storing in ImageStorageManager...");
      console.log("🔄 Compressed images count:", compressedImages.length);

      // Store images with question association
      const stored = await ImageStorageManager.storeQuestionImages(
        questionId,
        compressedImages
      );
      console.log("✅ Images stored in ImageStorageManager:", stored.length);

      // Update local state
      setStoredImages((prev) => {
        const updated = [...prev, ...stored];
        console.log("🔄 Updated storedImages state:", updated.length);
        return updated;
      });

      // Update parent component
      // If we skipped compression, use original URIs to avoid showing compressed/distorted versions
      const newUris = skipCompression ? imageUris : compressedImages.map((img) => img.uri);
      const allUris = [...value, ...newUris].slice(0, maxPhotos);
      console.log("🔄 Calling onChange with URIs:", allUris.length, "skipCompression:", skipCompression);
      console.log("🔄 New URIs:", newUris);
      onChange?.(allUris);

      // Notify parent about stored images
      console.log(
        "🔄 Calling onImagesStored with stored images:",
        stored.length
      );
      onImagesStored?.(stored);

      console.log(
        "✅ Images processed and stored successfully:",
        stored.length
      );
    } catch (error) {
      console.error("❌ Error processing images:", error);
      Alert.alert("Lỗi", "Không thể xử lý ảnh. Vui lòng thử lại.");
    } finally {
      setIsProcessing(false);
      console.log("🔄 Set isProcessing to false");
    }
  };

  const handlePickImage = async () => {
    console.log("📱 handlePickImage called, Platform:", Platform.OS);
    console.log("📱 Question ID:", questionId);
    console.log("📱 Current images:", value.length);

    // Web: Use file input
    if (Platform.OS === "web") {
      console.log("🌐 Using web file input");
      console.log("🌐 File input ref:", fileInputRef.current);
      if (fileInputRef.current) {
        console.log("🌐 Clicking file input...");
        fileInputRef.current.click();
        console.log("🌐 File input clicked!");
      } else {
        console.error("🌐 File input ref is null!");
      }
      return;
    }

    // Mobile: Picking from library is disabled without ImagePicker
    Alert.alert("Không hỗ trợ", "Tính năng chọn ảnh từ thư viện tạm thời không hỗ trợ trên mobile.");
  };

  // Web-only watermark similar to app/camera.tsx
  const watermarkWeb = async (uri: string): Promise<string> => {
    return new Promise((resolve) => {
      const img = new window.Image();
      img.crossOrigin = 'Anonymous';
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        if (!ctx) return resolve(uri);
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);
        const watermarkText = format(new Date(), 'yyyy-MM-dd HH:mm:ss');
        ctx.font = `${img.width / 25}px Arial`;
        ctx.textAlign = 'left';
        ctx.textBaseline = 'top';
        const padding = 8;
        const metrics = ctx.measureText(watermarkText);
        const textHeight = parseInt(ctx.font, 10);
        ctx.fillStyle = 'rgba(0,0,0,0.4)';
        ctx.fillRect(5, 5, metrics.width + 2 * padding, textHeight + 2 * padding);
        ctx.fillStyle = 'white';
        ctx.fillText(watermarkText, 5 + padding, 5 + padding);
        const dataUrl = canvas.toDataURL('image/jpeg');
        resolve(dataUrl);
      };
      img.src = uri;
    });
  };

  const handleTakePhoto = async () => {
    console.log("📷 handleTakePhoto called, Platform:", Platform.OS);

    // Web: Use file input with camera capture
    if (Platform.OS === "web") {
      if (fileInputRef.current) {
        fileInputRef.current.setAttribute("capture", "environment");
        fileInputRef.current.click();
      }
      return;
    }

    // Native: Open inline expo-camera overlay
    const hasPermission = await requestCameraPermission();
    if (!hasPermission) return;

    if (value.length >= maxPhotos) {
      Alert.alert("Đã đạt giới hạn", `Bạn chỉ có thể chọn tối đa ${maxPhotos} ảnh.`);
      return;
    }

    setShowCamera(true);
  };

  const handleRemoveImage = async (index: number) => {
    try {
      const imageUri = value[index];

      // Remove from stored images if questionId is provided
      if (questionId) {
        const storedImage = storedImages.find((img) => img.uri === imageUri);
        if (storedImage) {
          await ImageStorageManager.removeQuestionImage(
            questionId,
            storedImage.id
          );
          setStoredImages((prev) =>
            prev.filter((img) => img.id !== storedImage.id)
          );
        }
      }

      // Update parent component
      const updatedPhotos = value.filter((_, i) => i !== index);
      onChange?.(updatedPhotos);

      console.log("Image removed successfully");
    } catch (error) {
      console.error("Error removing image:", error);
      Alert.alert("Lỗi", "Không thể xóa ảnh. Vui lòng thử lại.");
    }
  };

  const handleRemovePhoto = (index: number) => {
    const updatedPhotos = value.filter((_, i) => i !== index);
    onChange?.(updatedPhotos);
  };

  const handleShowOptions = () => {
    console.log("🎯 handleShowOptions called, Platform:", Platform.OS);
    console.log("🎯 Current questionId:", questionId);
    console.log("🎯 Current value length:", value.length);
    console.log("🎯 Max photos:", maxPhotos);

    // Web: Directly open file picker (no need for options dialog)
    if (Platform.OS === "web") {
      console.log("🌐 Web: Opening file picker directly");
      console.log("🌐 File input ref exists:", !!fileInputRef.current);
      handlePickImage();
      return;
    }

    // Mobile: Show options dialog
    Alert.alert("Chọn ảnh", "Bạn muốn chụp ảnh mới hay chọn từ thư viện?", [
      {
        text: "Chụp ảnh",
        onPress: handleTakePhoto,
      },
      {
        text: "Chọn từ thư viện",
        onPress: handlePickImage,
      },
      {
        text: "Hủy",
        style: "cancel",
      },
    ]);
  };

  return (
    <View className="space-y-4">
      {/* Hidden file input for web */}
      {Platform.OS === "web" && (
        <input
          ref={fileInputRef as any}
          type="file"
          accept="image/*"
          multiple
          style={{ display: "none" }}
          onChange={handleWebFileInputChange as any}
        />
      )}

      {/* Processing Indicator */}
      {isProcessing && (
        <View className="flex-row items-center justify-center p-3 bg-blue-50 rounded-lg">
          <ActivityIndicator size="small" color="#3B82F6" />
          <Text className="ml-2 text-sm text-blue-600">
            {enableCompression ? "Đang nén và lưu ảnh..." : "Đang lưu ảnh..."}
          </Text>
        </View>
      )}

      {/* Selected Photos */}
      {value.length > 0 && showPreview && (
        <View className="mb-4">
          <Text className="text-sm text-gray-600 mb-2">
            Ảnh đã chọn ({value.length}/{maxPhotos}):
            {questionId && storedImages.length > 0 && (
              <Text className="text-xs text-green-600 ml-1">
                ({storedImages.filter((img) => !img.isUploaded).length} chưa tải
                lên)
              </Text>
            )}
          </Text>
          <View className="flex-row flex-wrap gap-2">
            {value.map((photo, index) => {
              const storedImage = storedImages.find((img) => img.uri === photo);
              return (
                <View key={index} className="relative">
                  <Image
                    source={{ uri: photo }}
                    className="w-20 h-20 rounded-lg"
                  />

                  {/* Upload status indicator */}
                  {storedImage && (
                    <View
                      className={`absolute top-1 left-1 w-3 h-3 rounded-full ${
                        storedImage.isUploaded
                          ? "bg-green-500"
                          : "bg-yellow-500"
                      }`}
                    />
                  )}

                  {/* Remove button */}
                  <Pressable
                    onPress={() => handleRemoveImage(index)}
                    className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full items-center justify-center"
                    disabled={isProcessing}
                  >
                    <X size={16} color="white" />
                  </Pressable>

                  {/* Compression indicator */}
                  {enableCompression && storedImage && (
                    <View className="absolute bottom-0 left-0 right-0 bg-black/50 rounded-b-lg px-1">
                      <Text className="text-xs text-white text-center">
                        {storedImage.size
                          ? `${(storedImage.size / 1024).toFixed(0)}KB`
                          : "WebP"}
                      </Text>
                    </View>
                  )}
                </View>
              );
            })}
          </View>
        </View>
      )}

      {/* Add Photo Button */}
      {value.length < maxPhotos && (
        <TouchableOpacity
          onPress={handleShowOptions}
          disabled={isProcessing}
          className={`border-2 border-dashed rounded-lg p-8 items-center ${
            isProcessing ? "border-gray-200 bg-gray-50" : "border-gray-300"
          }`}
        >
          <View className="flex-row items-center space-x-2">
            <CameraIcon
              size={24}
              color={isProcessing ? "#d1d5db" : "#6b7280"}
            />
            <ImageIcon size={24} color={isProcessing ? "#d1d5db" : "#6b7280"} />
            {enableCompression && !isProcessing && (
              <Upload size={20} color="#3B82F6" />
            )}
          </View>
          <Text
            className={`mt-2 text-center ${
              isProcessing ? "text-gray-400" : "text-gray-600"
            }`}
          >
            {isProcessing
              ? "Đang xử lý..."
              : Platform.OS === "web"
              ? "Nhấn để chọn ảnh từ máy tính"
              : placeholder}
          </Text>
          <Text className="text-gray-400 text-sm mt-1">
            {value.length}/{maxPhotos} ảnh
            {enableCompression && !isProcessing && (
              <Text className="text-blue-500"> • WebP nén tự động</Text>
            )}
          </Text>
        </TouchableOpacity>
      )}

      <Modal visible={showCamera} animationType="fade" presentationStyle="fullScreen">
        {previewUri ? (
          <View style={{ flex: 1, backgroundColor: 'black' }}>
            <Image
              source={{ uri: previewUri }}
              style={{ width: '100%', height: undefined, aspectRatio: previewSize ? previewSize.width / previewSize.height : 3/4 }}
              resizeMode="contain"
            />
            <View style={{ padding: 10 }}>
              <SafeAreaView edges={['bottom']}>
                <Button onPress={async () => {
                  console.log("🔥 SAVE BUTTON: About to save image with URI:", previewUri);
                  console.log("🔥 SAVE BUTTON: Preview size:", previewSize);

                  if (previewUri) {
                    // Skip compression for camera-captured images to avoid double processing
                    await processAndStoreImages([previewUri], true); // Pass skipCompression flag
                  }

                  setPreviewUri(undefined);
                  setPreviewSize(undefined);
                  setShowCamera(false);
                }} className='text-white'>
                  <Text>Save</Text>
                </Button>
              </SafeAreaView>
            </View>
            <MaterialIcons onPress={() => { setPreviewUri(undefined); setPreviewSize(undefined); }} name='close' size={35} color='white' style={{ position: 'absolute', top: 50, left: 20 }} />
          </View>
        ) : (
          <View style={{ flex: 1, backgroundColor: 'black' }}>
            <CameraView
              mode='picture'
              style={{ width: '100%', flex: 1 }}
              facing={facing}
              ref={cameraRef}
            >
              <View style={{ marginTop: 'auto', padding: 20, paddingBottom: 50, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', backgroundColor: '#00000099' }}>
                <View />
                <Pressable
                  style={{ width: 54, height: 54, borderRadius: 27, backgroundColor: 'white' }}
                  onPress={async () => {
                    try {
                      const pic = await cameraRef.current?.takePictureAsync({ quality: 1, skipProcessing: Platform.OS === 'android' });
                      let uri = pic?.uri as string | undefined;
                      let w = (pic as any)?.width as number | undefined;
                      let h = (pic as any)?.height as number | undefined;

                      if (uri && Platform.OS === 'web') {
                        uri = await watermarkWeb(uri);
                        // For web, determine dimensions after watermark
                        await new Promise<void>((resolve) => {
                          const img = new window.Image();
                          img.onload = () => { setPreviewSize({ width: img.naturalWidth, height: img.naturalHeight }); resolve(); };
                          img.src = uri as string;
                        });
                      } else if (uri) {
                        if (w && h) {
                          setPreviewSize({ width: w, height: h });
                        } else {
                          Image.getSize(uri, (width, height) => setPreviewSize({ width, height }), () => setPreviewSize(undefined));
                        }
                      }

                      if (uri) setPreviewUri(uri);
                    } catch (e) {
                      Alert.alert('Lỗi', 'Không thể chụp ảnh.');
                    }
                  }}
                />
                <MaterialIcons name='flip-camera-ios' size={24} color='white' onPress={() => setFacing(prev => (prev === 'back' ? 'front' : 'back'))} />
              </View>
            </CameraView>
            <MaterialIcons name='close' color='white' style={{ position: 'absolute', top: 50, left: 20 }} size={30} onPress={() => { setPreviewUri(undefined); setPreviewSize(undefined); setShowCamera(false); }} />
          </View>
        )}
      </Modal>

      {value.length >= maxPhotos && (
        <View className="border-2 border-gray-200 rounded-lg p-4 items-center">
          <Text className="text-gray-500 text-center">
            Đã đạt giới hạn {maxPhotos} ảnh
          </Text>
        </View>
      )}
    </View>
  );
}
