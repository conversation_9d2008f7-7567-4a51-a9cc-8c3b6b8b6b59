import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Pressable,
  Alert,
  Platform,
  ActivityIndicator,
} from "react-native";
import { Audio } from "expo-av";
import { MaterialIcons } from "@expo/vector-icons";
import { Text } from "~/components/ui/text";
import { AudioStorageManager, StoredRecording } from "~/src/utils/audioStorage";

interface AudioRecorderProps {
  questionId: string;
  onRecordingChange?: (recordings: StoredRecording[]) => void;
  maxDuration?: number; // in seconds, default 300 (5 minutes)
  disabled?: boolean;
}

export function AudioRecorder({
  questionId,
  onRecordingChange,
  maxDuration = 300,
  disabled = false,
}: AudioRecorderProps) {
  const [recording, setRecording] = useState<Audio.Recording | null>(null);
  const [isRecording, setIsRecording] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [sound, setSound] = useState<Audio.Sound | null>(null);
  const [storedRecordings, setStoredRecordings] = useState<StoredRecording[]>([]);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [playbackPosition, setPlaybackPosition] = useState(0);
  const [playbackDuration, setPlaybackDuration] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  const durationInterval = useRef<NodeJS.Timeout | null>(null);

  // Load stored recordings on mount
  useEffect(() => {
    loadStoredRecordings();
  }, [questionId]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (sound) {
        sound.unloadAsync();
      }
      if (recording) {
        recording.stopAndUnloadAsync();
      }
      if (durationInterval.current) {
        clearInterval(durationInterval.current);
      }
    };
  }, []);

  const loadStoredRecordings = async () => {
    try {
      const recordings = await AudioStorageManager.getQuestionRecordings(questionId);
      setStoredRecordings(recordings);
      onRecordingChange?.(recordings);
    } catch (error) {
      console.error("Error loading stored recordings:", error);
    }
  };

  const requestPermissions = async () => {
    try {
      const { status } = await Audio.requestPermissionsAsync();
      if (status !== "granted") {
        Alert.alert(
          "Cần quyền ghi âm",
          "Ứng dụng cần quyền truy cập microphone để ghi âm."
        );
        return false;
      }
      return true;
    } catch (error) {
      console.error("Error requesting audio permissions:", error);
      return false;
    }
  };

  const startRecording = async () => {
    try {
      const hasPermission = await requestPermissions();
      if (!hasPermission) return;

      setIsLoading(true);

      // Configure audio mode
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
      });

      // Create recording
      const { recording: newRecording } = await Audio.Recording.createAsync(
        Audio.RecordingOptionsPresets.HIGH_QUALITY
      );

      setRecording(newRecording);
      setIsRecording(true);
      setRecordingDuration(0);
      setIsLoading(false);

      // Start duration timer
      durationInterval.current = setInterval(() => {
        setRecordingDuration((prev) => {
          const newDuration = prev + 1;
          if (newDuration >= maxDuration) {
            stopRecording();
            return maxDuration;
          }
          return newDuration;
        });
      }, 1000);

      console.log("Recording started");
    } catch (error) {
      console.error("Failed to start recording:", error);
      Alert.alert("Lỗi", "Không thể bắt đầu ghi âm. Vui lòng thử lại.");
      setIsLoading(false);
    }
  };

  const stopRecording = async () => {
    try {
      if (!recording) return;

      setIsLoading(true);

      // Clear duration timer
      if (durationInterval.current) {
        clearInterval(durationInterval.current);
        durationInterval.current = null;
      }

      await recording.stopAndUnloadAsync();
      const uri = recording.getURI();

      if (uri) {
        // Get recording info
        const info = await recording.getStatusAsync();
        const duration = info.durationMillis || recordingDuration * 1000;

        // Get file size (approximate)
        let size = 0;
        try {
          if (Platform.OS !== "web") {
            const { FileSystem } = require("expo-file-system");
            const fileInfo = await FileSystem.getInfoAsync(uri);
            size = fileInfo.size || 0;
          }
        } catch (error) {
          console.warn("Could not get file size:", error);
        }

        // Store recording
        const storedRecording = await AudioStorageManager.storeQuestionRecording(
          questionId,
          {
            uri,
            name: `recording_${Date.now()}.m4a`,
            duration,
            size,
          }
        );

        console.log("Recording saved:", storedRecording);

        // Reload stored recordings
        await loadStoredRecordings();
      }

      setRecording(null);
      setIsRecording(false);
      setRecordingDuration(0);
      setIsLoading(false);
    } catch (error) {
      console.error("Failed to stop recording:", error);
      Alert.alert("Lỗi", "Không thể dừng ghi âm. Vui lòng thử lại.");
      setIsLoading(false);
    }
  };

  const playRecording = async (recordingUri: string) => {
    try {
      if (sound) {
        await sound.unloadAsync();
      }

      const { sound: newSound } = await Audio.Sound.createAsync(
        { uri: recordingUri },
        { shouldPlay: true }
      );

      setSound(newSound);
      setIsPlaying(true);

      // Set up playback status update
      newSound.setOnPlaybackStatusUpdate((status) => {
        if (status.isLoaded) {
          setPlaybackPosition(status.positionMillis || 0);
          setPlaybackDuration(status.durationMillis || 0);

          if (status.didJustFinish) {
            setIsPlaying(false);
            setPlaybackPosition(0);
          }
        }
      });
    } catch (error) {
      console.error("Error playing recording:", error);
      Alert.alert("Lỗi", "Không thể phát ghi âm.");
    }
  };

  const stopPlayback = async () => {
    try {
      if (sound) {
        await sound.stopAsync();
        setIsPlaying(false);
        setPlaybackPosition(0);
      }
    } catch (error) {
      console.error("Error stopping playback:", error);
    }
  };

  const deleteRecording = async (recordingId: string) => {
    try {
      Alert.alert(
        "Xóa ghi âm",
        "Bạn có chắc chắn muốn xóa ghi âm này?",
        [
          { text: "Hủy", style: "cancel" },
          {
            text: "Xóa",
            style: "destructive",
            onPress: async () => {
              await AudioStorageManager.removeQuestionRecording(questionId, recordingId);
              await loadStoredRecordings();
            },
          },
        ]
      );
    } catch (error) {
      console.error("Error deleting recording:", error);
      Alert.alert("Lỗi", "Không thể xóa ghi âm.");
    }
  };

  const formatDuration = (milliseconds: number) => {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  return (
    <View className="space-y-4">
      {/* Recording Controls */}
      <View className="flex-row items-center justify-center space-x-4 p-4 bg-gray-50 rounded-lg">
        {!isRecording ? (
          <Pressable
            onPress={startRecording}
            disabled={disabled || isLoading}
            className={`w-16 h-16 rounded-full items-center justify-center ${
              disabled || isLoading ? "bg-gray-300" : "bg-red-500"
            }`}
          >
            {isLoading ? (
              <ActivityIndicator color="white" />
            ) : (
              <MaterialIcons name="mic" size={32} color="white" />
            )}
          </Pressable>
        ) : (
          <Pressable
            onPress={stopRecording}
            disabled={isLoading}
            className="w-16 h-16 rounded-full bg-red-600 items-center justify-center"
          >
            {isLoading ? (
              <ActivityIndicator color="white" />
            ) : (
              <MaterialIcons name="stop" size={32} color="white" />
            )}
          </Pressable>
        )}

        <View className="flex-1">
          {isRecording ? (
            <View>
              <Text className="text-center text-red-600 font-medium">
                Đang ghi âm...
              </Text>
              <Text className="text-center text-gray-600">
                {formatDuration(recordingDuration * 1000)} / {formatDuration(maxDuration * 1000)}
              </Text>
            </View>
          ) : (
            <Text className="text-center text-gray-600">
              Nhấn để bắt đầu ghi âm
            </Text>
          )}
        </View>
      </View>

      {/* Stored Recordings */}
      {storedRecordings.map((recording) => (
        <View
          key={recording.id}
          className="flex-row items-center space-x-3 p-3 bg-white border border-gray-200 rounded-lg"
        >
          <Pressable
            onPress={() =>
              isPlaying ? stopPlayback() : playRecording(recording.uri)
            }
            className="w-10 h-10 rounded-full bg-blue-500 items-center justify-center"
          >
            <MaterialIcons
              name={isPlaying ? "pause" : "play-arrow"}
              size={24}
              color="white"
            />
          </Pressable>

          <View className="flex-1">
            <Text className="font-medium">{recording.name}</Text>
            <Text className="text-sm text-gray-600">
              {formatDuration(recording.duration)}
              {recording.size > 0 && ` • ${Math.round(recording.size / 1024)}KB`}
            </Text>
            {isPlaying && playbackDuration > 0 && (
              <View className="mt-1">
                <View className="h-1 bg-gray-200 rounded-full">
                  <View
                    className="h-1 bg-blue-500 rounded-full"
                    style={{
                      width: `${(playbackPosition / playbackDuration) * 100}%`,
                    }}
                  />
                </View>
              </View>
            )}
          </View>

          <Pressable
            onPress={() => deleteRecording(recording.id)}
            className="w-8 h-8 items-center justify-center"
          >
            <MaterialIcons name="delete" size={20} color="#ef4444" />
          </Pressable>
        </View>
      ))}

      {storedRecordings.length === 0 && !isRecording && (
        <Text className="text-center text-gray-500 py-4">
          Chưa có ghi âm nào
        </Text>
      )}
    </View>
  );
}
