import React from 'react';
import { View, Image, Modal, TouchableOpacity, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Text } from '~/components/ui/text';

interface ImagePreviewProps {
  visible: boolean;
  onClose: () => void;
  images: string[];
  initialIndex?: number;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

export function ImagePreview({
  visible,
  onClose,
  images,
  initialIndex = 0,
}: ImagePreviewProps) {
  const [currentIndex, setCurrentIndex] = React.useState(initialIndex);

  React.useEffect(() => {
    setCurrentIndex(initialIndex);
  }, [initialIndex]);

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  const handleNext = () => {
    if (currentIndex < images.length - 1) {
      setCurrentIndex(currentIndex + 1);
    }
  };

  if (!visible || images.length === 0) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View className="flex-1 bg-black">
        {/* Header */}
        <View className="flex-row justify-between items-center p-4 pt-12">
          <TouchableOpacity onPress={onClose}>
            <Ionicons name="close" size={24} color="white" />
          </TouchableOpacity>
          <Text className="text-white font-medium">
            {currentIndex + 1} / {images.length}
          </Text>
          <View className="w-6" />
        </View>

        {/* Image */}
        <View className="flex-1 justify-center items-center">
          <Image
            source={{ uri: images[currentIndex] }}
            className="w-full h-full"
            resizeMode="contain"
          />
        </View>

        {/* Navigation */}
        {images.length > 1 && (
          <View className="flex-row justify-between items-center p-4">
            <TouchableOpacity
              onPress={handlePrevious}
              disabled={currentIndex === 0}
              className={`p-2 rounded-full ${
                currentIndex === 0 ? 'opacity-50' : 'bg-white bg-opacity-20'
              }`}
            >
              <Ionicons name="chevron-back" size={24} color="white" />
            </TouchableOpacity>

            <TouchableOpacity
              onPress={handleNext}
              disabled={currentIndex === images.length - 1}
              className={`p-2 rounded-full ${
                currentIndex === images.length - 1
                  ? 'opacity-50'
                  : 'bg-white bg-opacity-20'
              }`}
            >
              <Ionicons name="chevron-forward" size={24} color="white" />
            </TouchableOpacity>
          </View>
        )}
      </View>
    </Modal>
  );
} 