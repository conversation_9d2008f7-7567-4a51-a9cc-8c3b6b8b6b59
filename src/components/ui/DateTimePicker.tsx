import React, { useState, useEffect, useRef } from "react";
import { View, Platform, TouchableOpacity, TextInput } from "react-native";
import { Text } from "~/components/ui/text";
import { Calendar } from "lucide-react-native";
import { format } from "date-fns";
import { vi } from "date-fns/locale";

// Conditionally import the native DateTimePicker only for mobile platforms
let RNDateTimePicker: any = null;
if (Platform.OS !== "web") {
  RNDateTimePicker = require("@react-native-community/datetimepicker").default;
}

interface DateTimePickerProps {
  date: Date | undefined;
  setDate: (date: Date | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  defaultToToday?: boolean; // New prop to set default to today
}

export function DateTimePicker({
  date,
  setDate,
  placeholder = "Chọn ngày",
  disabled = false,
  defaultToToday = true,
}: DateTimePickerProps) {
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [tempDate, setTempDate] = useState<Date>(date || new Date());
  const dateInputRef = useRef<any>(null);

  // Calculate date range: 30 days in the past and 30 days in the future
  const today = new Date();
  const minDate = new Date(today);
  minDate.setDate(today.getDate() - 30);
  const maxDate = new Date(today);
  maxDate.setDate(today.getDate() + 30);

  // Set default value to today if no date is provided and defaultToToday is true
  useEffect(() => {
    if (!date && defaultToToday) {
      setDate(new Date());
    }
  }, [date, defaultToToday, setDate]);

  // Update tempDate when date prop changes
  useEffect(() => {
    if (date) {
      setTempDate(date);
    }
  }, [date]);

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);

    if (selectedDate && event.type !== "dismissed") {
      // Set time to noon (12:00) since we're only showing date
      const newDate = new Date(selectedDate);
      newDate.setHours(12);
      newDate.setMinutes(0);
      newDate.setSeconds(0);
      newDate.setMilliseconds(0);

      // Check if date is within allowed range (additional validation)
      if (newDate >= minDate && newDate <= maxDate) {
        setTempDate(newDate);
        setDate(newDate);
      }
      // If out of range, the native picker should already prevent selection
      // due to minimumDate and maximumDate props
    }
  };

  const formatDisplayDate = (dateToFormat: Date) => {
    try {
      return format(dateToFormat, "dd/MM/yyyy HH:mm", { locale: vi });
    } catch {
      return (
        dateToFormat.toLocaleDateString("vi-VN") +
        " " +
        dateToFormat.toLocaleTimeString("vi-VN", {
          hour: "2-digit",
          minute: "2-digit",
        })
      );
    }
  };

  const clearDate = () => {
    setDate(undefined);
    setTempDate(new Date());
  };

  // Web-specific handlers
  const handleWebDateChange = (event: any) => {
    const dateValue = event.target.value;
    if (dateValue) {
      const newDate = new Date(dateValue + "T12:00:00"); // Set time to noon

      // Check if date is within allowed range
      if (newDate >= minDate && newDate <= maxDate) {
        setTempDate(newDate);
        setDate(newDate);
      } else {
        // Reset to previous valid date or today if out of range
        event.target.value = date
          ? format(date, "yyyy-MM-dd")
          : format(new Date(), "yyyy-MM-dd");
      }
    }
  };

  // Render web version
  if (Platform.OS === "web") {
    return (
      <View className="space-y-3">
        {/* Date Selection - Web */}
        <View className="space-y-2">
          <Text className="text-sm font-medium text-gray-700">Chọn ngày</Text>
          <Text className="text-xs text-gray-500">
            Có thể chọn từ {format(minDate, "dd/MM/yyyy", { locale: vi })} đến{" "}
            {format(maxDate, "dd/MM/yyyy", { locale: vi })}
          </Text>
          <View className="relative">
            <input
              ref={dateInputRef}
              type="date"
              value={date ? format(date, "yyyy-MM-dd") : ""}
              min={format(minDate, "yyyy-MM-dd")}
              max={format(maxDate, "yyyy-MM-dd")}
              onChange={handleWebDateChange}
              disabled={disabled}
              placeholder="Chọn ngày"
              style={{
                width: "100%",
                padding: "12px",
                border: "1px solid #d1d5db",
                borderRadius: "8px",
                backgroundColor: "white",
                fontSize: "16px",
                outline: "none",
                cursor: disabled ? "not-allowed" : "pointer",
                opacity: disabled ? 0.5 : 1,
                colorScheme: "light",
              }}
            />
          </View>
        </View>

        {/* Selected Date Display */}
        {date && (
          <View className="p-3 bg-blue-50 rounded-lg border border-blue-200">
            <View className="flex-row items-center justify-between">
              <Text className="text-sm font-medium text-blue-900">
                Đã chọn: {format(date, "dd/MM/yyyy", { locale: vi })}
              </Text>
              {!disabled && (
                <TouchableOpacity onPress={clearDate}>
                  <Text className="text-sm text-red-600 font-medium">Xóa</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        )}
      </View>
    );
  }

  // Render mobile version
  return (
    <View className="space-y-3">
      {/* Date Selection - Mobile */}
      <View className="space-y-2">
        <Text className="text-sm font-medium text-gray-700">Chọn ngày</Text>
        <Text className="text-xs text-gray-500">
          Có thể chọn từ {format(minDate, "dd/MM/yyyy", { locale: vi })} đến{" "}
          {format(maxDate, "dd/MM/yyyy", { locale: vi })}
        </Text>
        <TouchableOpacity
          onPress={() => !disabled && setShowDatePicker(true)}
          disabled={disabled}
          className={`flex-row items-center justify-between p-3 border border-gray-300 rounded-lg bg-white ${
            disabled ? "opacity-50" : ""
          }`}
        >
          <View className="flex-row items-center flex-1">
            <Calendar size={16} color="#6b7280" className="mr-2" />
            <Text className={`${date ? "text-gray-900" : "text-gray-500"}`}>
              {date ? format(date, "dd/MM/yyyy", { locale: vi }) : "Chọn ngày"}
            </Text>
          </View>
        </TouchableOpacity>
      </View>

      {/* Selected Date Display - Mobile */}
      {date && (
        <View className="p-3 bg-blue-50 rounded-lg border border-blue-200">
          <View className="flex-row items-center justify-between">
            <Text className="text-sm font-medium text-blue-900">
              Đã chọn: {format(date, "dd/MM/yyyy", { locale: vi })}
            </Text>
            {!disabled && (
              <TouchableOpacity onPress={clearDate}>
                <Text className="text-sm text-red-600 font-medium">Xóa</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>
      )}

      {/* Date Picker Modal - Mobile Only */}
      {showDatePicker && RNDateTimePicker && (
        <RNDateTimePicker
          value={tempDate}
          mode="date"
          display={Platform.OS === "ios" ? "spinner" : "default"}
          onChange={handleDateChange}
          locale="vi-VN"
          minimumDate={minDate}
          maximumDate={maxDate}
        />
      )}
    </View>
  );
}
