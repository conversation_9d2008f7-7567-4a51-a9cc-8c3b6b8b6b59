import React from "react";
import { View, Text, TouchableOpacity, Alert } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import * as DocumentPicker from "expo-document-picker";
import { FILE_UPLOAD } from "~/src/types/constants";

interface FileUploadProps {
  value: string[];
  onChange: (files: string[]) => void;
  maxFiles?: number;
  placeholder?: string;
  allowedTypes?: string[];
}

export function FileUpload({
  value = [],
  onChange,
  maxFiles = FILE_UPLOAD.MAX_IMAGES_DEFAULT,
  placeholder = "Chọn file",
  allowedTypes = ["*/*"],
}: FileUploadProps) {
  const handlePickDocument = async () => {
    if (value.length >= maxFiles) {
      Alert.alert("Giới hạn", `Bạn chỉ có thể chọn tối đa ${maxFiles} file.`);
      return;
    }

    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: allowedTypes,
        copyToCacheDirectory: true,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const newFiles = result.assets.map((asset: any) => asset.uri);
        onChange([...value, ...newFiles]);
      }
    } catch (error) {
      console.error("Error picking document:", error);
      Alert.alert("Lỗi", "Không thể chọn file. Vui lòng thử lại.");
    }
  };

  const handleRemoveFile = (index: number) => {
    const newFiles = value.filter((_, i) => i !== index);
    onChange(newFiles);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getFileName = (uri: string) => {
    return uri.split("/").pop() || "Unknown file";
  };

  return (
    <View className="space-y-3">
      {/* Upload Button */}
      <TouchableOpacity
        onPress={handlePickDocument}
        className="border-2 border-dashed border-gray-300 rounded-lg p-4 items-center"
      >
        <Ionicons name="document" size={24} color="#6B7280" />
        <Text className="text-gray-600 mt-2 text-center">{placeholder}</Text>
        <Text className="text-gray-400 text-xs mt-1">
          Tối đa {maxFiles} file
        </Text>
      </TouchableOpacity>

      {/* File List */}
      {value.length > 0 && (
        <View className="space-y-2">
          <Text className="text-sm font-medium text-gray-700">
            File đã chọn ({value.length}/{maxFiles})
          </Text>
          {value.map((fileUri, index) => (
            <View
              key={index}
              className="flex-row items-center justify-between bg-gray-50 p-3 rounded-lg"
            >
              <View className="flex-1">
                <Text
                  className="text-sm font-medium text-gray-800"
                  numberOfLines={1}
                >
                  {getFileName(fileUri)}
                </Text>
                <Text className="text-xs text-gray-500">{fileUri}</Text>
              </View>
              <TouchableOpacity
                onPress={() => handleRemoveFile(index)}
                className="ml-2 p-1"
              >
                <Ionicons name="close-circle" size={20} color="#EF4444" />
              </TouchableOpacity>
            </View>
          ))}
        </View>
      )}
    </View>
  );
}
