import React from "react";
import { View, Pressable } from "react-native";
import { Circle } from "lucide-react-native";

interface RadioGroupProps {
  value?: string;
  onValueChange: (value: string) => void;
  children: React.ReactNode;
  className?: string;
}

export function RadioGroup({
  value,
  onValueChange,
  children,
  className,
}: RadioGroupProps) {
  return <View className={className}>{children}</View>;
}

interface RadioGroupItemProps {
  value: string;
  children: React.ReactNode;
  className?: string;
}

export function RadioGroupItem({
  value,
  children,
  className,
}: RadioGroupItemProps) {
  return <Pressable className={className}>{children}</Pressable>;
}
