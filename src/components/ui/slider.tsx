import React from "react";
import { View } from "react-native";

interface SliderProps {
  value: number;
  onValueChange: (value: number) => void;
  minimumValue?: number;
  maximumValue?: number;
  step?: number;
  className?: string;
}

export function Slider({
  value,
  onValueChange,
  minimumValue = 0,
  maximumValue = 100,
  step = 1,
  className,
}: SliderProps) {
  return (
    <View className={`h-2 bg-gray-200 rounded-full ${className || ""}`}>
      <View
        className="h-2 bg-blue-500 rounded-full"
        style={{
          width: `${
            ((value - minimumValue) / (maximumValue - minimumValue)) * 100
          }%`,
        }}
      />
    </View>
  );
}
