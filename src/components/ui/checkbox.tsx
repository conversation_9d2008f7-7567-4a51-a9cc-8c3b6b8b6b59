import React from "react";
import { Pressable, View } from "react-native";
import { Check } from "lucide-react-native";

interface CheckboxProps {
  checked: boolean;
  onCheckedChange: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
}

export function Checkbox({
  checked,
  onCheckedChange,
  disabled,
  className,
}: CheckboxProps) {
  return (
    <Pressable
      onPress={() => !disabled && onCheckedChange(!checked)}
      className={`w-5 h-5 rounded border-2 items-center justify-center ${
        checked ? "border-blue-500 bg-blue-500" : "border-gray-300"
      } ${disabled ? "opacity-50" : ""} ${className || ""}`}
    >
      {checked && <Check size={12} color="white" />}
    </Pressable>
  );
}
