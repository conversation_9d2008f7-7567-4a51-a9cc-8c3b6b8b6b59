import React from 'react';
import { View, Modal, Alert } from 'react-native';
import { Text } from '~/components/ui/text';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { LocalFile } from '~/src/hooks/useFileUpload';

interface UploadConfirmModalProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: () => void;
  localImages: LocalFile[];
  isUploading: boolean;
}

export function UploadConfirmModal({
  visible,
  onClose,
  onConfirm,
  localImages,
  isUploading,
}: UploadConfirmModalProps) {
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View className="flex-1 bg-black bg-opacity-50 justify-center items-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle className="text-lg">Xác nhận upload ảnh</CardTitle>
          </CardHeader>
          <CardContent>
            <Text className="text-gray-600 mb-4">
              Bạn có {localImages.length} ảnh chưa được upload. Ảnh sẽ được upload lên Strapi khi gửi báo cáo.
            </Text>
            
            {/* Image list */}
            <View className="mb-4">
              <Text className="font-semibold mb-2">Danh sách ảnh:</Text>
              {localImages.map((image, index) => (
                <View key={image.id} className="flex-row items-center justify-between p-2 bg-gray-50 rounded mb-1">
                  <Text className="flex-1 text-sm">{image.name}</Text>
                  <Text className="text-xs text-gray-500">{formatFileSize(image.size)}</Text>
                </View>
              ))}
            </View>

            {/* Action buttons */}
            <View className="flex-row space-x-2">
              <Button
                onPress={onClose}
                variant="outline"
                disabled={isUploading}
                className="flex-1"
              >
                <Text>Hủy</Text>
              </Button>
              
              <Button
                onPress={onConfirm}
                disabled={isUploading}
                className="flex-1"
              >
                <Text className="text-white">
                  {isUploading ? "Đang upload..." : "Gửi báo cáo"}
                </Text>
              </Button>
            </View>
          </CardContent>
        </Card>
      </View>
    </Modal>
  );
} 