import React from "react";
import {
  View,
  Text,
  Modal,
  Pressable,
  ScrollView,
} from "react-native";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import {
  CheckCircle,
  FileText,
  Eye,
  Home,
  X,
} from "lucide-react-native";

interface AuditSuccessModalProps {
  visible: boolean;
  onClose: () => void;
  onViewAnswers: () => void;
  onBackToHome: () => void;
  answerCount: number;
  totalQuestions: number;
  planTitle?: string;
  storeName?: string;
}

export function AuditSuccessModal({
  visible,
  onClose,
  onViewAnswers,
  onBackToHome,
  answerCount,
  totalQuestions,
  planTitle,
  storeName,
}: AuditSuccessModalProps) {
  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View className="flex-1 bg-black/50 justify-center items-center px-4">
        <Card className="w-full max-w-md bg-white">
          <CardContent className="p-6">
            {/* Close button */}
            <Pressable
              onPress={onClose}
              className="absolute top-4 right-4 z-10 p-2"
            >
              <X size={20} color="#6b7280" />
            </Pressable>

            {/* Success icon and title */}
            <View className="items-center mb-6">
              <View className="bg-green-100 p-4 rounded-full mb-4">
                <CheckCircle size={48} color="#10b981" />
              </View>
              <Text className="text-xl font-bold text-gray-900 text-center mb-2">
                Gửi báo cáo thành công!
              </Text>
              <Text className="text-gray-600 text-center">
                Báo cáo kiểm toán đã được lưu và gửi thành công
              </Text>
            </View>

            {/* Summary information */}
            <View className="mb-6">
              {planTitle && (
                <View className="flex-row items-center mb-3">
                  <FileText size={16} color="#6b7280" />
                  <Text className="ml-2 text-gray-700 font-medium">
                    {planTitle}
                  </Text>
                </View>
              )}
              
              {storeName && (
                <View className="flex-row items-center mb-3">
                  <Home size={16} color="#6b7280" />
                  <Text className="ml-2 text-gray-700">
                    {storeName}
                  </Text>
                </View>
              )}

              <View className="bg-gray-50 p-4 rounded-lg">
                <View className="flex-row justify-between items-center mb-2">
                  <Text className="text-gray-600">Câu hỏi đã trả lời:</Text>
                  <Badge variant="secondary">
                    <Text className="text-sm font-medium">
                      {answerCount}/{totalQuestions}
                    </Text>
                  </Badge>
                </View>
                
                <View className="flex-row justify-between items-center">
                  <Text className="text-gray-600">Tỷ lệ hoàn thành:</Text>
                  <Text className="text-green-600 font-semibold">
                    {totalQuestions > 0 
                      ? Math.round((answerCount / totalQuestions) * 100)
                      : 0}%
                  </Text>
                </View>
              </View>
            </View>

            {/* Action buttons */}
            <View className="space-y-3">
              <Button
                onPress={onViewAnswers}
                variant="outline"
                className="w-full py-3"
              >
                <View className="flex-row items-center justify-center">
                  <Eye size={18} color="#3b82f6" />
                  <Text className="ml-2 text-blue-600 font-medium">
                    Xem câu trả lời đã gửi
                  </Text>
                </View>
              </Button>

              <Button
                onPress={onBackToHome}
                className="w-full py-3"
              >
                <View className="flex-row items-center justify-center">
                  <Home size={18} color="#ffffff" />
                  <Text className="ml-2 text-white font-medium">
                    Về trang chủ
                  </Text>
                </View>
              </Button>
            </View>

            {/* Additional info */}
            <View className="mt-4 pt-4 border-t border-gray-200">
              <Text className="text-xs text-gray-500 text-center">
                Báo cáo đã được lưu vào hệ thống và có thể xem lại bất cứ lúc nào
              </Text>
            </View>
          </CardContent>
        </Card>
      </View>
    </Modal>
  );
}

export default AuditSuccessModal;
