import axios, {
  AxiosInstance,
  InternalAxiosRequestConfig,
  AxiosResponse,
} from "axios";
import AsyncStorage from "@react-native-async-storage/async-storage";

const API_BASE_URL =
  process.env.EXPO_PUBLIC_API_URL || "https://cpsstrapi.appmkt.vn/api";

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 1000 * 60 * 60 * 24 * 365,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor
api.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    // Add auth token if available
    try {
      const token = await AsyncStorage.getItem("auth_token");
      if (token && config.headers) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      // Token not available, continue without auth
    }

    // Handle Content-Type for FormData requests
    if (config.data instanceof FormData) {
      // Check if Content-Type is explicitly set to multipart/form-data
      const explicitContentType =
        config.headers?.["Content-Type"] || config.headers?.["content-type"];

      if (explicitContentType === "multipart/form-data") {
        // Keep the explicit multipart/form-data header for React Native
        console.log(
          "🔧 FormData detected - keeping explicit multipart/form-data Content-Type for React Native"
        );
      } else {
        // Remove any other Content-Type headers for FormData
        delete config.headers?.["Content-Type"];
        delete config.headers?.["content-type"];
        console.log(
          "🔧 FormData detected - Content-Type headers removed for automatic boundary handling"
        );
      }
    }

    // Log request for debugging
    console.log("API Request:", config.method?.toUpperCase(), config.url);
    if (config.params) {
      console.log("Request params:", config.params);
    }

    // Debug headers for FormData requests
    if (config.data instanceof FormData) {
      console.log("🔧 Final headers for FormData request:", config.headers);
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response.data;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Token manager
export const tokenManager = {
  async setToken(token: string): Promise<void> {
    try {
      await AsyncStorage.setItem("auth_token", token);
    } catch (error) {
      // Handle storage error
    }
  },

  async getToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem("auth_token");
    } catch (error) {
      return null;
    }
  },

  async removeToken(): Promise<void> {
    try {
      await AsyncStorage.removeItem("auth_token");
    } catch (error) {
      // Handle storage error
    }
  },
};

// Custom error class
export class ApiError extends Error {
  constructor(message: string, public status: number, public data?: any) {
    super(message);
    this.name = "ApiError";
  }
}

export { api };
