import { api, ApiError } from "./client";
import { API_ENDPOINTS, buildEndpoint } from "./endpoints";
import { StrapiApiResponse, StrapiEntityData } from "../types/api";
import {
  UserAnswer,
  FlatUserAnswer,
  MultiChoiceValue,
  YesNoMultiValue,
  StrapiRelation,
} from "../types/entities";
import { UploadedFile } from "./fileUpload";

// User Answer API Service
export class UserAnswerService {
  /**
   * Save user answer to the server with proper JSON structure
   */
  static async saveUserAnswer(
    answerData: CreateUserAnswerData
  ): Promise<FlatUserAnswer> {
    try {
      console.log(
        "🚨🚨🚨 REAL-TIME MONITORING: UserAnswerService.saveUserAnswer CALLED 🚨🚨🚨"
      );
      console.log("🔥 CRITICAL DEBUG - Raw answerData received:");
      console.log(
        "- merchandiser:",
        answerData.merchandiser,
        "type:",
        typeof answerData.merchandiser
      );
      console.log("- plan:", answerData.plan, "type:", typeof answerData.plan);
      console.log(
        "- question:",
        answerData.question,
        "type:",
        typeof answerData.question
      );

      // CRITICAL: Enhanced photo_value logging
      console.log("📊 ENHANCED Answer field analysis:");
      console.log(
        "- text_value:",
        answerData.text_value,
        "(present:",
        answerData.hasOwnProperty("text_value"),
        ")"
      );
      console.log(
        "- boolean_value:",
        answerData.boolean_value,
        "(present:",
        answerData.hasOwnProperty("boolean_value"),
        ")"
      );
      console.log("🚨 PHOTO_VALUE DETAILED ANALYSIS:");
      console.log(
        "- photo_value:",
        answerData.photo_value,
        "(present:",
        answerData.hasOwnProperty("photo_value"),
        ")"
      );
      console.log("- photo_value type:", typeof answerData.photo_value);
      console.log(
        "- photo_value isArray:",
        Array.isArray(answerData.photo_value)
      );
      console.log(
        "- photo_value length:",
        Array.isArray(answerData.photo_value)
          ? answerData.photo_value.length
          : "N/A"
      );
      console.log(
        "- photo_value content:",
        Array.isArray(answerData.photo_value)
          ? answerData.photo_value
          : "Not an array"
      );
      console.log("🔧 PHOTO_VALUE FIX STATUS:", {
        hasProperty: answerData.hasOwnProperty("photo_value"),
        isArray: Array.isArray(answerData.photo_value),
        length: Array.isArray(answerData.photo_value)
          ? answerData.photo_value.length
          : 0,
        firstItem:
          Array.isArray(answerData.photo_value) &&
          answerData.photo_value.length > 0
            ? answerData.photo_value[0]
            : null,
      });

      console.log(
        "- multi_choice_values:",
        answerData.multi_choice_values,
        "(present:",
        answerData.hasOwnProperty("multi_choice_values"),
        ")"
      );
      console.log(
        "- yes_no_multi:",
        answerData.yes_no_multi,
        "(present:",
        answerData.hasOwnProperty("yes_no_multi"),
        ")"
      );

      // Validate answer data before processing
      if (!this.validateAnswerData(answerData)) {
        throw new ApiError("Dữ liệu câu trả lời không hợp lệ", 400);
      }

      // Validate and structure the data according to schema requirements
      const structuredData = this.structureAnswerData(answerData);

      console.log(
        "Saving user answer with data:",
        JSON.stringify(structuredData, null, 2)
      );

      // CRITICAL DEBUG: Log the exact API request payload
      const requestPayload = { data: structuredData };
      console.log("🚀 SENDING TO STRAPI API:");
      console.log(
        "📋 Full request payload:",
        JSON.stringify(requestPayload, null, 2)
      );
      console.log(
        "🔍 Specific photo_value field in payload:",
        JSON.stringify(requestPayload.data.photo_value, null, 2)
      );
      console.log(
        "🔍 Specific yes_no_multi field in payload:",
        JSON.stringify(requestPayload.data.yes_no_multi, null, 2)
      );

      const response = await api.post<StrapiApiResponse<StrapiUserAnswer>>(
        API_ENDPOINTS.USER_ANSWERS,
        requestPayload
      );

      console.log(
        "User answer save response:",
        JSON.stringify(response.data, null, 2)
      );

      // Transform and log the final result
      const transformedResult = transformStrapiUserAnswer(response.data);
      console.log("🎯 Final transformed result:");
      console.log("- ID:", transformedResult.id);
      console.log("- text_value:", transformedResult.text_value);
      console.log("- boolean_value:", transformedResult.boolean_value);
      console.log("- photo_value:", transformedResult.photo_value);
      console.log(
        "- multi_choice_values:",
        transformedResult.multi_choice_values
      );
      console.log(
        "- yes_no_multi:",
        JSON.stringify(transformedResult.yes_no_multi, null, 2)
      );
      console.log("- Question ID:", transformedResult.question?.id);
      console.log("- Plan ID:", transformedResult.plan?.id);
      console.log("- User ID:", transformedResult.merchandiser?.id);

      // CRITICAL: Check if yes_no_multi data was actually saved to database
      if (transformedResult.yes_no_multi) {
        console.log("✅ YES_NO_MULTI DATA SUCCESSFULLY SAVED TO DATABASE");
        console.log(
          "📊 Hierarchical structure preserved:",
          JSON.stringify(transformedResult.yes_no_multi, null, 2)
        );
      } else {
        console.log("❌ YES_NO_MULTI DATA NOT SAVED - FIELD IS NULL/EMPTY");
        console.log(
          "🔍 Raw response yes_no_multi:",
          response.data.attributes?.yes_no_multi
        );
      }

      return transformedResult;
    } catch (error: any) {
      console.error("Failed to save user answer:", error);
      console.error("Error response:", error?.response?.data);

      // CRITICAL: Log detailed error information for photo_value issues
      if (error.response?.data) {
        console.error("🚨 STRAPI API ERROR DETAILS:");
        console.error("- Status:", error.response.status);
        console.error(
          "- Error data:",
          JSON.stringify(error.response.data, null, 2)
        );

        // Check if error is related to photo_value
        const errorMessage = JSON.stringify(error.response.data).toLowerCase();
        if (
          errorMessage.includes("photo_value") ||
          errorMessage.includes("media") ||
          errorMessage.includes("file")
        ) {
          console.error("🚨 PHOTO_VALUE RELATED ERROR DETECTED!");
          console.error(
            "- This error is likely related to invalid file IDs or missing media files"
          );
          console.error(
            "- Check if the uploaded file IDs exist in Strapi media library"
          );
        }
      }

      if (error instanceof ApiError) {
        throw error;
      }

      if (error?.response) {
        const status = error.response.status;
        const errorData = error.response.data;
        const message =
          errorData?.error?.message ||
          errorData?.message ||
          "Không thể lưu câu trả lời";

        throw new ApiError(message, status, errorData);
      }

      throw new ApiError("Không thể lưu câu trả lời. Vui lòng thử lại.", 500);
    }
  }

  /**
   * Structure answer data according to Strapi v5 schema requirements with proper relation format
   */
  private static structureAnswerData(answerData: CreateUserAnswerData): any {
    console.log("Structuring answer data for Strapi v5:", answerData);

    // For Strapi v5, ALL entities use documentId for relations
    const structuredData: any = {
      merchandiser: {
        connect: [{ documentId: answerData.merchandiser }], // User documentId
      },
      plan: {
        connect: [{ documentId: answerData.plan }], // Plan documentId
      },
      question: {
        connect: [{ documentId: answerData.question }], // Question documentId
      },
    };

    console.log("Structured relations for Strapi v5:", {
      merchandiser: structuredData.merchandiser,
      plan: structuredData.plan,
      question: structuredData.question,
    });

    console.log(
      "Full structured data payload:",
      JSON.stringify(structuredData, null, 2)
    );

    // Debug: Log the actual documentId values being sent
    console.log("DEBUG - DocumentId values being sent:");
    console.log("- Merchandiser documentId:", answerData.merchandiser, "→", {
      documentId: answerData.merchandiser,
    });
    console.log("- Plan documentId:", answerData.plan, "→", {
      documentId: answerData.plan,
    });
    console.log("- Question documentId:", answerData.question, "→", {
      documentId: answerData.question,
    });

    // Add appropriate field based on answer type - now handles null values for unanswered questions
    if (answerData.hasOwnProperty("text_value")) {
      structuredData.text_value = answerData.text_value; // Can be null for unanswered questions
      console.log("📝 Adding text_value:", answerData.text_value);
    }

    if (answerData.hasOwnProperty("boolean_value")) {
      structuredData.boolean_value = answerData.boolean_value; // Can be null for unanswered questions
      console.log("✅ Adding boolean_value:", answerData.boolean_value);
    }

    if (answerData.hasOwnProperty("photo_value")) {
      console.log("🚨 PROCESSING PHOTO_VALUE in structureAnswerData:");
      console.log("- Raw photo_value:", answerData.photo_value);
      console.log("- Type:", typeof answerData.photo_value);
      console.log("- Is Array:", Array.isArray(answerData.photo_value));
      console.log(
        "- Length:",
        Array.isArray(answerData.photo_value)
          ? answerData.photo_value.length
          : "N/A"
      );

      if (
        answerData.photo_value &&
        Array.isArray(answerData.photo_value) &&
        answerData.photo_value.length > 0
      ) {
        // Validate that all file IDs are valid numbers
        const validFileIds = answerData.photo_value.filter((fileId: any) => {
          const isValid = typeof fileId === "number" && fileId > 0;
          if (!isValid) {
            console.error(
              `❌ Invalid file ID detected: ${fileId} (type: ${typeof fileId})`
            );
          }
          return isValid;
        });

        if (validFileIds.length > 0) {
          // CORRECTED: For Strapi v5 media fields, use direct array of IDs (not connect format)
          structuredData.photo_value = validFileIds;

          console.log("🚨 PHOTO_VALUE STRAPI v5 CONVERSION (CORRECTED):");
          console.log("- Input IDs:", answerData.photo_value);
          console.log("- Valid IDs:", validFileIds);
          console.log(
            "- Final photo_value (direct array):",
            structuredData.photo_value
          );
        } else {
          structuredData.photo_value = [];
          console.error("❌ No valid file IDs found in photo_value array");
        }
      } else {
        structuredData.photo_value = []; // No images for this question - use empty array
        console.log("📸 Adding photo_value: [] (no images)");
        console.log(
          "- Reason: photo_value is",
          !answerData.photo_value
            ? "falsy"
            : !Array.isArray(answerData.photo_value)
            ? "not array"
            : "empty array"
        );
      }
    } else {
      console.log("⚠️ photo_value property not present in answerData");
      // Set empty array as default for consistency
      structuredData.photo_value = [];
    }

    // Structure multi_choice_values as consistent JSON array - handles null for unanswered questions
    if (answerData.hasOwnProperty("multi_choice_values")) {
      if (
        answerData.multi_choice_values !== null &&
        answerData.multi_choice_values.length > 0
      ) {
        structuredData.multi_choice_values = answerData.multi_choice_values.map(
          (choice) => ({
            id: choice.id?.toString() || "",
            label: choice.label || "",
            value: choice.value || choice.label || "",
            selected: true,
            timestamp: new Date().toISOString(),
          })
        );
        console.log(
          "🔘 Adding multi_choice_values:",
          structuredData.multi_choice_values
        );
      } else {
        structuredData.multi_choice_values = null; // Explicitly set null for unanswered questions
        console.log("🔘 Adding multi_choice_values: null (unanswered)");
      }
    }

    // Structure yes_no_multi as consistent JSON object - handles null for unanswered questions
    if (answerData.hasOwnProperty("yes_no_multi")) {
      if (answerData.yes_no_multi !== null) {
        // CRITICAL FIX: Preserve the entire yes_no_multi structure from transformation
        // Don't reconstruct it here - just pass it through to maintain hierarchical data
        structuredData.yes_no_multi = answerData.yes_no_multi;
        console.log(
          "❓ Adding yes_no_multi (FULL STRUCTURE):",
          JSON.stringify(structuredData.yes_no_multi, null, 2)
        );
      } else {
        structuredData.yes_no_multi = null; // Explicitly set null for unanswered questions
        console.log("❓ Adding yes_no_multi: null (unanswered)");
      }
    }

    return structuredData;
  }

  // Removed old helper methods - now using direct documentId values

  /**
   * Validate answer data before saving - now allows null values for unanswered questions
   */
  private static validateAnswerData(answerData: CreateUserAnswerData): boolean {
    // Required fields validation
    if (!answerData.merchandiser || !answerData.plan || !answerData.question) {
      return false;
    }

    // For complete audit trail, we now allow records with all null answer values
    // This represents unanswered questions and is valid for audit completeness

    // Check if at least one answer field is defined (can be null for unanswered questions)
    const hasAnswerField = !!(
      answerData.hasOwnProperty("text_value") ||
      answerData.hasOwnProperty("boolean_value") ||
      answerData.hasOwnProperty("photo_value") ||
      answerData.hasOwnProperty("multi_choice_values") ||
      answerData.hasOwnProperty("yes_no_multi")
    );

    return hasAnswerField;
  }

  /**
   * Get user answers by plan documentId (Strapi v5 compatible)
   */
  static async getUserAnswersByPlan(
    planDocumentId: string
  ): Promise<FlatUserAnswer[]> {
    try {
      console.log(
        "Getting user answers for plan documentId:",
        planDocumentId,
        "type:",
        typeof planDocumentId
      );

      const response = await api.get<StrapiApiResponse<StrapiUserAnswer[]>>(
        API_ENDPOINTS.USER_ANSWERS,
        {
          params: {
            filters: {
              plan: {
                documentId: {
                  $eq: planDocumentId,
                },
              },
            },
            populate: {
              merchandiser: {
                fields: ["id", "username", "email"],
              },
              plan: {
                fields: ["id", "documentId", "plan_title", "plan_status"],
                populate: {
                  store: {
                    fields: ["id", "documentId", "store_name", "store_code"],
                  },
                },
              },
              question: {
                fields: [
                  "id",
                  "documentId",
                  "question_text",
                  "type",
                  "is_required",
                  "options",
                ],
              },
              photo_value: true,
            },
          },
        }
      );

      console.log(
        "User answers API response:",
        JSON.stringify(response.data, null, 2)
      );

      // CRITICAL DEBUG: Log photo_value data specifically
      response.data.forEach((answer: any, index: number) => {
        console.log(`🖼️ Answer ${index + 1} photo_value analysis:`, {
          questionType: answer.question?.type,
          photoValueRaw: answer.photo_value,
          photoValueType: typeof answer.photo_value,
          photoValueIsArray: Array.isArray(answer.photo_value),
          photoValueLength: Array.isArray(answer.photo_value)
            ? answer.photo_value.length
            : "N/A",
          photoValueSample:
            Array.isArray(answer.photo_value) && answer.photo_value.length > 0
              ? answer.photo_value[0]
              : null,
        });
      });

      const transformedAnswers = response.data.map(transformStrapiUserAnswer);
      console.log("Transformed user answers:", transformedAnswers);

      return transformedAnswers;
    } catch (error: any) {
      console.error("Failed to get user answers:", error);
      console.error("Error response:", error?.response?.data);

      if (error instanceof ApiError) {
        throw error;
      }

      throw new ApiError("Không thể tải câu trả lời.", 500);
    }
  }

  /**
   * Update existing user answer
   */
  static async updateUserAnswer(
    id: number,
    answerData: Partial<CreateUserAnswerData>
  ): Promise<FlatUserAnswer> {
    try {
      const response = await api.put<StrapiApiResponse<StrapiUserAnswer>>(
        buildEndpoint(API_ENDPOINTS.USER_ANSWERS, id),
        {
          data: answerData,
        }
      );

      return transformStrapiUserAnswer(response.data);
    } catch (error: any) {
      console.error("Failed to update user answer:", error);

      if (error instanceof ApiError) {
        throw error;
      }

      throw new ApiError("Không thể cập nhật câu trả lời.", 500);
    }
  }
}

// Types for API requests
export interface CreateUserAnswerData {
  text_value?: string;
  boolean_value?: boolean;
  photo_value?: number[]; // Array of media IDs
  multi_choice_values?: MultiChoiceValue[];
  yes_no_multi?: YesNoMultiValue;
  merchandiser: string; // User documentId for Strapi v5
  plan: string; // Plan documentId for Strapi v5
  question: string; // Question documentId for Strapi v5
}

// Strapi API response types
export interface StrapiUserAnswerAttributes {
  text_value?: string;
  boolean_value?: boolean;
  photo_value?: StrapiRelation<any[]>;
  multi_choice_values?: MultiChoiceValue[];
  yes_no_multi?: YesNoMultiValue;
  merchandiser: StrapiRelation<any>;
  plan: StrapiRelation<any>;
  question: StrapiRelation<any>;
  createdAt: string;
  updatedAt: string;
}

export interface StrapiUserAnswer
  extends StrapiEntityData<StrapiUserAnswerAttributes> {}

// Transform function to convert Strapi response to flat structure
function transformStrapiUserAnswer(strapiUserAnswer: any): FlatUserAnswer {
  // Handle both Strapi v4 (with attributes) and v5 (direct fields) formats
  const isV5Format = !strapiUserAnswer.attributes;

  console.log(`🔄 Transforming user answer ${strapiUserAnswer.id}:`, {
    isV5Format,
    rawPhotoValue: strapiUserAnswer.photo_value,
    photoValueType: typeof strapiUserAnswer.photo_value,
    questionType: strapiUserAnswer.question?.type,
  });

  if (isV5Format) {
    // Strapi v5 format - fields are directly on the object
    const transformedPhotoValue = Array.isArray(strapiUserAnswer.photo_value)
      ? strapiUserAnswer.photo_value
      : strapiUserAnswer.photo_value?.data || [];

    console.log(`📸 V5 photo_value transformation:`, {
      input: strapiUserAnswer.photo_value,
      output: transformedPhotoValue,
      outputLength: transformedPhotoValue.length,
    });

    return {
      id: strapiUserAnswer.id,
      text_value: strapiUserAnswer.text_value,
      boolean_value: strapiUserAnswer.boolean_value,
      photo_value: transformedPhotoValue,
      multi_choice_values: strapiUserAnswer.multi_choice_values,
      yes_no_multi: strapiUserAnswer.yes_no_multi,
      merchandiser: strapiUserAnswer.merchandiser
        ? {
            id: strapiUserAnswer.merchandiser.id,
            username: strapiUserAnswer.merchandiser.username,
            email: strapiUserAnswer.merchandiser.email,
            createdAt: strapiUserAnswer.merchandiser.createdAt || "",
            updatedAt: strapiUserAnswer.merchandiser.updatedAt || "",
          }
        : undefined,
      plan: strapiUserAnswer.plan
        ? {
            id: strapiUserAnswer.plan.id,
            documentId: strapiUserAnswer.plan.documentId, // Strapi v5 documentId
            plan_title: strapiUserAnswer.plan.plan_title,
            plan_status: strapiUserAnswer.plan.plan_status,
            assigned_date: strapiUserAnswer.plan.assigned_date,
            start_date: strapiUserAnswer.plan.start_date,
            end_date: strapiUserAnswer.plan.end_date,
            visits_target: strapiUserAnswer.plan.visits_target,
            store: strapiUserAnswer.plan.store
              ? {
                  id: strapiUserAnswer.plan.store.id,
                  documentId: strapiUserAnswer.plan.store.documentId, // Strapi v5 documentId
                  store_name: strapiUserAnswer.plan.store.store_name,
                  store_code: strapiUserAnswer.plan.store.store_code,
                  address: strapiUserAnswer.plan.store.address,
                  is_active: strapiUserAnswer.plan.store.is_active,
                  createdAt: strapiUserAnswer.plan.store.createdAt || "",
                  updatedAt: strapiUserAnswer.plan.store.updatedAt || "",
                }
              : undefined,
            createdAt: strapiUserAnswer.plan.createdAt || "",
            updatedAt: strapiUserAnswer.plan.updatedAt || "",
          }
        : undefined,
      question: strapiUserAnswer.question
        ? {
            id: strapiUserAnswer.question.id,
            documentId: strapiUserAnswer.question.documentId, // Strapi v5 documentId
            question_text: strapiUserAnswer.question.question_text || "",
            type: strapiUserAnswer.question.type || "text",
            is_required: strapiUserAnswer.question.is_required || false,
            options: strapiUserAnswer.question.options,
            createdAt: strapiUserAnswer.question.createdAt || "",
            updatedAt: strapiUserAnswer.question.updatedAt || "",
          }
        : undefined,
      createdAt: strapiUserAnswer.createdAt,
      updatedAt: strapiUserAnswer.updatedAt,
    };
  } else {
    // Strapi v4 format - fields are in attributes
    const { attributes } = strapiUserAnswer;

    return {
      id: strapiUserAnswer.id,
      text_value: attributes.text_value,
      boolean_value: attributes.boolean_value,
      photo_value: attributes.photo_value?.data || [],
      multi_choice_values: attributes.multi_choice_values,
      yes_no_multi: attributes.yes_no_multi,
      merchandiser: attributes.merchandiser?.data
        ? {
            id: attributes.merchandiser.data.id,
            username: attributes.merchandiser.data.attributes?.username,
            email: attributes.merchandiser.data.attributes?.email,
            createdAt: attributes.merchandiser.data.attributes?.createdAt || "",
            updatedAt: attributes.merchandiser.data.attributes?.updatedAt || "",
          }
        : undefined,
      plan: attributes.plan?.data
        ? {
            id: attributes.plan.data.id,
            plan_title: attributes.plan.data.attributes?.plan_title,
            plan_status: attributes.plan.data.attributes?.plan_status,
            assigned_date: attributes.plan.data.attributes?.assigned_date,
            start_date: attributes.plan.data.attributes?.start_date,
            end_date: attributes.plan.data.attributes?.end_date,
            visits_target: attributes.plan.data.attributes?.visits_target,
            createdAt: attributes.plan.data.attributes?.createdAt || "",
            updatedAt: attributes.plan.data.attributes?.updatedAt || "",
          }
        : undefined,
      question: attributes.question?.data
        ? {
            id: attributes.question.data.id,
            documentId: attributes.question.data.documentId, // May exist in v4 as well
            question_text:
              attributes.question.data.attributes?.question_text || "",
            type: attributes.question.data.attributes?.type || "text",
            is_required:
              attributes.question.data.attributes?.is_required || false,
            options: attributes.question.data.attributes?.options,
            createdAt: attributes.question.data.attributes?.createdAt || "",
            updatedAt: attributes.question.data.attributes?.updatedAt || "",
          }
        : undefined,
      createdAt: attributes.createdAt,
      updatedAt: attributes.updatedAt,
    };
  }
}

// Data transformation utilities
export class UserAnswerTransformer {
  /**
   * Transform mobile app answer format to user answer format
   */
  static transformAnswerToUserAnswer(
    questionId: string,
    value: any,
    questionType: string,
    merchandiserDocumentId: string,
    planDocumentId: string
  ): CreateUserAnswerData {
    const baseData: CreateUserAnswerData = {
      merchandiser: merchandiserDocumentId, // User's documentId
      plan: planDocumentId, // Plan's documentId
      question: questionId, // Question's documentId
    };

    switch (questionType) {
      case "text":
        return {
          ...baseData,
          text_value: value, // null for unanswered questions
        };

      case "number":
        return {
          ...baseData,
          text_value: value !== null ? value.toString() : null, // null for unanswered questions
        };

      case "photo":
        return {
          ...baseData,
          photo_value:
            value !== null ? (Array.isArray(value) ? value : [value]) : null, // null for unanswered questions
        };

      case "single_choice":
        return {
          ...baseData,
          text_value: value, // null for unanswered questions
        };

      case "multi_choice":
        return {
          ...baseData,
          multi_choice_values:
            value !== null
              ? Array.isArray(value)
                ? value.map((choice, index) => ({
                    id: choice.id || index,
                    label: choice.label || choice,
                    value: choice.value || choice.label || choice,
                  }))
                : []
              : null, // null for unanswered questions
        };

      case "yes_no":
        console.log(
          "🔍 YES_NO transformation - Input value:",
          JSON.stringify(value, null, 2)
        );
        const yesNoResult = {
          ...baseData,
          yes_no_multi:
            value !== null
              ? {
                  // If value is already a structured yes_no_multi object, use its answer field
                  // Otherwise, treat value as the direct hierarchical answer structure
                  answer: value.answer !== undefined ? value.answer : value,
                  question_id: questionId,
                  timestamp: new Date().toISOString(),
                  // Preserve any additional options from the original structure
                  options: value.options || null,
                }
              : null, // null for unanswered questions
        };
        console.log(
          "🎯 YES_NO transformation - Output:",
          JSON.stringify(yesNoResult.yes_no_multi, null, 2)
        );
        return yesNoResult;

      case "rating":
        return {
          ...baseData,
          text_value: value !== null ? value.toString() : null, // null for unanswered questions
        };

      default:
        return {
          ...baseData,
          text_value: value !== null ? value.toString() : null, // null for unanswered questions
        };
    }
  }

  /**
   * Batch transform multiple answers - processes ALL questions, not just answered ones
   */
  static transformAnswersToUserAnswers(
    answers: Record<string, any>,
    merchandiserDocumentId: string,
    planDocumentId: string,
    questions: Array<{ id: string; type: string }>
  ): CreateUserAnswerData[] {
    console.log("Transforming answers:", JSON.stringify(answers, null, 2));
    console.log("Questions:", JSON.stringify(questions, null, 2));
    console.log(
      "MerchandiserDocumentId:",
      merchandiserDocumentId,
      "PlanDocumentId:",
      planDocumentId
    );

    // Process ALL questions from the audit form, not just answered ones
    return questions.map((question) => {
      const questionId = question.id;
      const questionType = question.type;
      const answerData = answers[questionId]; // May be undefined for unanswered questions

      console.log(
        `Processing question ${questionId} (type: ${questionType}):`,
        answerData || "UNANSWERED"
      );

      // Handle the mobile app answer structure correctly
      let value: any = null; // Default to null for unanswered questions

      if (answerData) {
        if (
          answerData &&
          typeof answerData === "object" &&
          answerData.question_id
        ) {
          // This is the mobile app answer object structure
          if (answerData.multi_choice_values !== undefined) {
            value = answerData.multi_choice_values;
          } else if (answerData.yes_no_multi !== undefined) {
            // For yes_no questions, preserve the entire hierarchical structure
            // Don't extract just the 'answer' field - keep the full nested object
            value = answerData.yes_no_multi;
            console.log(
              "🔍 YES_NO_MULTI: Preserving full hierarchical structure:",
              value
            );
          } else if (answerData.text_value !== undefined) {
            value = answerData.text_value;
          } else if (answerData.boolean_value !== undefined) {
            value = answerData.boolean_value;
          } else if (answerData.photo_value !== undefined) {
            value = answerData.photo_value;
          } else {
            value = answerData;
          }
        } else {
          // Direct value
          value = answerData;
        }
      }

      console.log(
        `Extracted value for question ${questionId}:`,
        value || "NULL (unanswered)"
      );

      const transformed = this.transformAnswerToUserAnswer(
        questionId,
        value,
        questionType,
        merchandiserDocumentId,
        planDocumentId
      );

      console.log(
        `Transformed answer for question ${questionId}:`,
        transformed
      );

      return transformed;
    });
  }
}

/**
 * Submit multiple user answers (batch submission)
 * Used by FormSubmissionHandler for comprehensive form submission with images
 */
export const submitUserAnswers = async (data: {
  planId: string;
  storeId: string;
  userId: string;
  answers: Array<{
    question: string;
    answer_value: any;
    question_type: string;
    photo_value: UploadedFile[] | number[];
    plan: string;
    store: string;
    user: string;
  }>;
}): Promise<{ id: string; success: boolean }> => {
  try {
    console.log("🚨🚨🚨 REAL-TIME MONITORING: submitUserAnswers CALLED 🚨🚨🚨");
    console.log("🚀 BATCH SUBMITTING USER ANSWERS:", {
      planId: data.planId,
      storeId: data.storeId,
      userId: data.userId,
      answersCount: data.answers.length,
    });

    // CRITICAL: Log all incoming answers with photo data
    console.log("🔍 INCOMING ANSWERS ANALYSIS:");
    data.answers.forEach((answer, index) => {
      console.log(`📋 Incoming Answer ${index + 1}:`, {
        questionId: answer.question,
        questionType: answer.question_type,
        answerValue: answer.answer_value,
        photoValueType: Array.isArray(answer.photo_value)
          ? "array"
          : typeof answer.photo_value,
        photoValueLength: Array.isArray(answer.photo_value)
          ? answer.photo_value.length
          : 0,
        photoValueSample:
          Array.isArray(answer.photo_value) && answer.photo_value.length > 0
            ? typeof answer.photo_value[0] === "object"
              ? {
                  id: (answer.photo_value[0] as UploadedFile).id,
                  name: (answer.photo_value[0] as UploadedFile).name,
                }
              : answer.photo_value[0]
            : null,
      });
    });

    const results = [];

    // Process each answer
    for (const [index, answer] of data.answers.entries()) {
      console.log(
        `🔄 Processing answer ${index + 1}/${
          data.answers.length
        } for question:`,
        answer.question
      );

      // Transform the answer data to match CreateUserAnswerData format
      const answerData: CreateUserAnswerData = {
        merchandiser: data.userId,
        plan: data.planId,
        question: answer.question,
        // CRITICAL FIX: Always initialize photo_value to ensure it's present in answerData
        photo_value: [], // Will be populated later if images exist
      };

      // Set the appropriate field based on question type and answer value
      switch (answer.question_type) {
        case "text":
        case "number":
        case "single_choice":
        case "rating":
          answerData.text_value = answer.answer_value?.toString() || null;
          break;

        case "yes_no":
          // Handle yes_no questions properly based on answer structure
          if (typeof answer.answer_value === "boolean") {
            // Simple boolean answer - use boolean_value field
            answerData.boolean_value = answer.answer_value;
          } else if (
            answer.answer_value !== null &&
            typeof answer.answer_value === "object"
          ) {
            // Hierarchical structure - use yes_no_multi field
            answerData.yes_no_multi = {
              answer: answer.answer_value,
              question_id: answer.question,
              timestamp: new Date().toISOString(),
            };
          } else {
            // Null or other values
            answerData.boolean_value = null;
          }
          break;

        case "multi_choice":
          answerData.multi_choice_values = Array.isArray(answer.answer_value)
            ? answer.answer_value.map((choice, index) => ({
                id: choice.id || index,
                label: choice.label || choice,
                value: choice.value || choice.label || choice,
              }))
            : null;
          break;

        case "photo":
          // Handle photo values - convert UploadedFile[] to number[]
          if (Array.isArray(answer.photo_value)) {
            if (
              answer.photo_value.length > 0 &&
              typeof answer.photo_value[0] === "object"
            ) {
              // UploadedFile[] format
              answerData.photo_value = (
                answer.photo_value as UploadedFile[]
              ).map((file) => file.id);
            } else {
              // Already number[] format
              answerData.photo_value = answer.photo_value as number[];
            }
          } else {
            answerData.photo_value = null;
          }
          break;

        default:
          answerData.text_value = answer.answer_value?.toString() || null;
      }

      // CRITICAL: Add photo_value for any question type that has images
      console.log(`🔍 Checking photo_value for question ${answer.question}:`, {
        hasPhotoValue: !!answer.photo_value,
        isArray: Array.isArray(answer.photo_value),
        length: Array.isArray(answer.photo_value)
          ? answer.photo_value.length
          : 0,
        firstItemType:
          Array.isArray(answer.photo_value) && answer.photo_value.length > 0
            ? typeof answer.photo_value[0]
            : "none",
      });

      if (
        answer.photo_value &&
        Array.isArray(answer.photo_value) &&
        answer.photo_value.length > 0
      ) {
        if (typeof answer.photo_value[0] === "object") {
          // UploadedFile[] format
          const fileIds = (answer.photo_value as UploadedFile[]).map(
            (file) => file.id
          );
          answerData.photo_value = fileIds;

          console.log(
            `🚨 CONVERTING UploadedFile[] to IDs for question ${answer.question}:`,
            {
              originalFiles: (answer.photo_value as UploadedFile[]).map(
                (f) => ({ id: f.id, name: f.name })
              ),
              convertedIds: fileIds,
            }
          );
        } else {
          // Already number[] format
          answerData.photo_value = answer.photo_value as number[];

          console.log(
            `🚨 Using existing number[] for question ${answer.question}:`,
            {
              ids: answerData.photo_value,
            }
          );
        }

        console.log(
          `📸 FINAL: Adding ${answerData.photo_value.length} images to question ${answer.question}`,
          { imageIds: answerData.photo_value }
        );
      } else {
        console.log(`⚠️ NO IMAGES for question ${answer.question}`);
        // CRITICAL FIX: Explicitly set photo_value to empty array instead of leaving undefined
        answerData.photo_value = [];
      }

      // CRITICAL: Log final answer data before saving
      console.log(`🚨 FINAL ANSWER DATA for question ${answer.question}:`, {
        merchandiser: answerData.merchandiser,
        plan: answerData.plan,
        question: answerData.question,
        text_value: answerData.text_value,
        boolean_value: answerData.boolean_value,
        photo_value: answerData.photo_value,
        photo_value_length: Array.isArray(answerData.photo_value)
          ? answerData.photo_value.length
          : "not array",
        photo_value_type: typeof answerData.photo_value,
        multi_choice_values: answerData.multi_choice_values,
        yes_no_multi: answerData.yes_no_multi,
      });

      // Save the user answer
      console.log(
        `🚨 CALLING UserAnswerService.saveUserAnswer for question ${answer.question}`
      );
      const result = await UserAnswerService.saveUserAnswer(answerData);
      results.push(result);

      console.log(
        `✅ SAVED answer for question ${answer.question}:`,
        result.id
      );
    }

    console.log("🎉 All user answers submitted successfully:", results.length);

    return {
      id: `batch_${Date.now()}`,
      success: true,
    };
  } catch (error: any) {
    console.error("❌ Failed to submit user answers:", error);
    throw new Error(`Failed to submit answers: ${error.message}`);
  }
};

// Export the service as default and named export
export default UserAnswerService;
