import { api, tokenManager, ApiError } from "./client";
import { API_ENDPOINTS } from "./endpoints";
import {
  LoginCredentials,
  RegisterData,
  AuthResponse,
  FlatUser,
} from "../types";

// Auth API Service
export class AuthService {
  /**
   * Login user with credentials
   */
  static async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      const response = await api.post<AuthResponse>(
        API_ENDPOINTS.AUTH.LOGIN,
        credentials
      );

      // Store JWT token
      if (response.jwt) {
        await tokenManager.setToken(response.jwt);
      }

      return response;
    } catch (error: any) {
      // Re-throw ApiError as-is
      if (error?.name === "ApiError") {
        throw error;
      }

      // Handle axios errors
      if (error?.response) {
        const status = error.response.status;
        const errorData = error.response.data;
        const message =
          errorData?.error?.message ||
          errorData?.message ||
          "Đăng nhập thất bại";

        throw new ApiError(message, status, errorData);
      }

      // Handle network/timeout errors
      if (
        error?.code === "NETWORK_ERROR" ||
        error?.message?.includes("Network")
      ) {
        throw new ApiError(
          "Kết nối mạng thất bại. Vui lòng kiểm tra kết nối internet của bạn.",
          0
        );
      }

      if (
        error?.code === "ECONNABORTED" ||
        error?.message?.includes("timeout")
      ) {
        throw new ApiError("Yêu cầu hết thời gian chờ. Vui lòng thử lại.", 0);
      }

      // Default error
      throw new ApiError(
        error?.message ||
          "Đăng nhập thất bại. Vui lòng kiểm tra thông tin đăng nhập.",
        401
      );
    }
  }

  /**
   * Register new user
   */
  static async register(data: RegisterData): Promise<AuthResponse> {
    try {
      const response = await api.post<AuthResponse>(
        API_ENDPOINTS.AUTH.REGISTER,
        data
      );

      // Store JWT token
      if (response.jwt) {
        await tokenManager.setToken(response.jwt);
      }

      return response;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      throw new ApiError("Đăng ký thất bại. Vui lòng thử lại.", 400);
    }
  }

  /**
   * Get current user profile
   */
  static async getProfile(): Promise<FlatUser> {
    try {
      const response = await api.get<any>(API_ENDPOINTS.AUTH.ME);

      console.log("Raw user profile response:", response);

      // Transform the response to extract documentId
      const transformedUser: FlatUser = {
        id: response.id,
        documentId: response.documentId, // Extract documentId from Strapi v5 response
        username: response.username,
        email: response.email,
        confirmed: response.confirmed,
        blocked: response.blocked,
        role: response.role?.name || response.role,
        createdAt: response.createdAt,
        updatedAt: response.updatedAt,
      };

      console.log("Transformed user profile:", transformedUser);
      console.log("User documentId extracted:", transformedUser.documentId);

      return transformedUser;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      throw new ApiError("Không thể tải thông tin người dùng.", 500);
    }
  }

  /**
   * Logout user
   */
  static async logout(): Promise<void> {
    try {
      // Remove token from storage
      await tokenManager.removeToken();

      // Optional: Call logout endpoint if your API has one
      // await api.post('/auth/logout');
    } catch (error) {
      // Don't throw error for logout, just log it
    }
  }

  /**
   * Forgot password
   */
  static async forgotPassword(email: string): Promise<{ message: string }> {
    try {
      const response = await api.post<{ message: string }>(
        API_ENDPOINTS.AUTH.FORGOT_PASSWORD,
        { email }
      );

      return response;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      throw new ApiError(
        "Không thể gửi email đặt lại mật khẩu. Vui lòng thử lại.",
        400
      );
    }
  }

  /**
   * Reset password
   */
  static async resetPassword(
    code: string,
    password: string,
    passwordConfirmation: string
  ): Promise<AuthResponse> {
    try {
      const response = await api.post<AuthResponse>(
        API_ENDPOINTS.AUTH.RESET_PASSWORD,
        {
          code,
          password,
          passwordConfirmation,
        }
      );

      // Store JWT token
      if (response.jwt) {
        await tokenManager.setToken(response.jwt);
      }

      return response;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      throw new ApiError("Không thể đặt lại mật khẩu. Vui lòng thử lại.", 400);
    }
  }

  /**
   * Check if user is authenticated
   */
  static async isAuthenticated(): Promise<boolean> {
    try {
      const token = await tokenManager.getToken();
      if (!token) {
        return false;
      }

      // Verify token by getting user profile
      await AuthService.getProfile();
      return true;
    } catch (error) {
      // If profile fetch fails, user is not authenticated
      await tokenManager.removeToken();
      return false;
    }
  }

  /**
   * Update user profile
   */
  static async updateProfile(userData: Partial<FlatUser>): Promise<FlatUser> {
    try {
      const response = await api.put<FlatUser>(API_ENDPOINTS.AUTH.ME, userData);

      return response;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      throw new ApiError(
        "Không thể cập nhật thông tin cá nhân. Vui lòng thử lại.",
        400
      );
    }
  }
}

// Export convenience functions
export const authAPI = {
  login: AuthService.login,
  register: AuthService.register,
  logout: AuthService.logout,
  getProfile: AuthService.getProfile,
  forgotPassword: AuthService.forgotPassword,
  resetPassword: AuthService.resetPassword,
  isAuthenticated: AuthService.isAuthenticated,
  updateProfile: AuthService.updateProfile,
};
