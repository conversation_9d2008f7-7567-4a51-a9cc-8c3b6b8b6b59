import { api, ApiError } from "./client";
import { API_ENDPOINTS, buildEndpoint } from "./endpoints";
import { StrapiApiResponse, StrapiEntityData } from "../types/api";
import { MockQuestionService } from "../mockdata/questions";

// Question types from schema
export interface Question {
  id: number;
  documentId?: string; // Strapi v5 documentId
  question_text: string;
  type:
    | "text"
    | "number"
    | "photo"
    | "single_choice"
    | "multi_choice"
    | "yes_no"
    | "rating";
  is_required: boolean;
  is_image_answer_require?: boolean; // <-- thêm field này
  sort_order?: number; // <-- thêm field sort_order
  options?: {
    choices?: Array<{
      id: string | number;
      label: string;
    }>;
    multiple?: boolean;
    min?: number;
    max?: number;
    labels?: string[];
  };
  audit_forms?: {
    data: StrapiEntityData<any>[];
  };
}

// Strapi API response types for questions
export interface StrapiQuestionAttributes {
  question_text: string;
  type: string;
  is_required: boolean;
  sort_order?: number;
  options?: any;
  audit_forms?: {
    data: StrapiEntityData<any>[];
  };
}

export interface StrapiQuestion
  extends StrapiEntityData<StrapiQuestionAttributes> {}

export interface QuestionsApiResponse
  extends StrapiApiResponse<StrapiQuestion[]> {}

// Query parameters for filtering questions
export interface QuestionQueryParams {
  pagination?: {
    page?: number;
    pageSize?: number;
  };
  sort?: string[];
  filters?: {
    audit_forms?: {
      id?: {
        $eq?: number;
      };
    };
    type?: {
      $eq?: string;
      $in?: string[];
    };
  };
  populate?: string | string[] | Record<string, any>;
}

// Transform Strapi question data to flat structure
const transformStrapiQuestion = (strapiQuestion: any): Question => {
  // Handle both old Strapi v4 format (with attributes) and new Strapi v5 format (direct fields)
  const isV5Format = !strapiQuestion.attributes;

  if (isV5Format) {
    // Strapi v5 format - fields are directly on the object
    return {
      id: strapiQuestion.id,
      documentId: strapiQuestion.documentId, // Strapi v5 documentId
      question_text: strapiQuestion.question_text,
      type: strapiQuestion.type,
      is_required: strapiQuestion.is_required,
      is_image_answer_require: strapiQuestion.is_image_answer_require, // <-- map field này
      sort_order: strapiQuestion.sort_order, // <-- map sort_order field
      options: strapiQuestion.options,
      audit_forms: strapiQuestion.audit_forms,
    };
  } else {
    // Strapi v4 format - fields are in attributes
    const { id, attributes } = strapiQuestion;

    return {
      id,
      documentId: strapiQuestion.documentId, // May exist in v4 as well
      question_text: attributes.question_text,
      type: attributes.type,
      is_required: attributes.is_required,
      is_image_answer_require: attributes.is_image_answer_require, // <-- map field này
      sort_order: attributes.sort_order, // <-- map sort_order field
      options: attributes.options,
      audit_forms: attributes.audit_forms,
    };
  }
};

// Question API Service
export class QuestionService {
  /**
   * Get questions with optional filtering
   */
  static async getQuestions(params?: QuestionQueryParams): Promise<{
    data: Question[];
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  }> {
    try {
      const queryParams: Record<string, any> = {};

      // Add pagination
      if (params?.pagination) {
        queryParams.pagination = params.pagination;
      }

      // Add sorting
      if (params?.sort) {
        queryParams.sort = params.sort;
      }

      // Add filters
      if (params?.filters) {
        queryParams.filters = params.filters;
      }

      // Add population
      queryParams.populate = params?.populate || {
        audit_forms: true,
      };

      console.log("Calling questions API with params:", queryParams);

      const response = (await api.get(API_ENDPOINTS.QUESTIONS, {
        params: queryParams,
        headers: {
          // Tạm thời không gửi auth header để test
          Authorization: undefined,
        },
      })) as QuestionsApiResponse;
      console.log("Questions API response:", response);

      return {
        data: response.data.map(transformStrapiQuestion),
        pagination: response.meta?.pagination,
      };
    } catch (error: any) {
      if (error instanceof ApiError) {
        throw error;
      }

      if (error?.response) {
        const status = error.response.status;
        const errorData = error.response.data;
        const message =
          errorData?.error?.message ||
          errorData?.message ||
          "Không thể tải danh sách câu hỏi";

        throw new ApiError(message, status, errorData);
      }

      throw new ApiError("Lỗi kết nối. Vui lòng thử lại.", 0);
    }
  }

  /**
   * Get questions by audit form ID (client-side filtering)
   */
  static async getQuestionsByAuditForm(
    auditFormId: number
  ): Promise<Question[]> {
    try {
      console.log("🔍 Getting questions for audit form ID:", auditFormId);

      // Lấy tất cả questions và populate audit_forms
      const allQuestionsResponse = await this.getQuestions({
        populate: { audit_forms: true },
        pagination: { page: 1, pageSize: 1000 },
      });

      console.log("📋 All questions from API:", allQuestionsResponse.data);
      console.log(
        "📊 Total questions found:",
        allQuestionsResponse.data.length
      );

      // Lọc client-side theo audit_form.id
      const filteredQuestions = allQuestionsResponse.data.filter((question) => {
        console.log(
          "🔍 Question ID:",
          question.id,
          "audit_forms:",
          question.audit_forms
        );

        // Kiểm tra xem question có audit_forms không
        if (!question.audit_forms || !Array.isArray(question.audit_forms)) {
          console.log(
            "❌ Question",
            question.id,
            "không có audit_forms hoặc không phải array"
          );
          return false;
        }

        // Tìm audit_form có id khớp
        const hasMatchingAuditForm = question.audit_forms.some((auditForm) => {
          console.log(
            "🔍 Checking audit_form:",
            auditForm,
            "against target:",
            auditFormId
          );
          console.log(
            "🔍 auditForm.id:",
            auditForm.id,
            "type:",
            typeof auditForm.id
          );
          console.log(
            "🔍 auditFormId:",
            auditFormId,
            "type:",
            typeof auditFormId
          );

          // Chuyển về number để so sánh chính xác
          const auditFormIdNum = Number(auditForm.id);
          const targetIdNum = Number(auditFormId);

          const matches = auditFormIdNum === targetIdNum;
          console.log(
            "✅ Match result:",
            matches,
            `(${auditFormIdNum} === ${targetIdNum})`
          );
          return matches;
        });

        console.log(
          "✅ Question",
          question.id,
          "matches audit form",
          auditFormId,
          ":",
          hasMatchingAuditForm
        );
        return hasMatchingAuditForm;
      });

      console.log(
        "🎯 Filtered questions for audit form",
        auditFormId,
        ":",
        filteredQuestions
      );
      console.log(
        "📈 Found",
        filteredQuestions.length,
        "questions for audit form",
        auditFormId
      );

      // Sort questions by sort_order (ascending), with null/undefined values at the end
      const sortedQuestions = filteredQuestions.sort((a, b) => {
        const aOrder = a.sort_order ?? Number.MAX_SAFE_INTEGER;
        const bOrder = b.sort_order ?? Number.MAX_SAFE_INTEGER;
        return aOrder - bOrder;
      });

      console.log(
        "🔄 Sorted questions by sort_order:",
        sortedQuestions.map((q) => ({
          id: q.id,
          question_text: q.question_text,
          sort_order: q.sort_order,
        }))
      );

      return sortedQuestions;
    } catch (error: any) {
      console.warn(
        "❌ API Questions failed, falling back to mock data:",
        error
      );
      // fallback mock nếu cần
      return MockQuestionService.getQuestionsByAuditForm(auditFormId);
    }
  }

  /**
   * Get a single question by ID
   */
  static async getQuestion(id: number | string): Promise<Question> {
    try {
      const response = (await api.get(
        buildEndpoint(API_ENDPOINTS.QUESTIONS, id),
        {
          params: {
            populate: {
              audit_forms: true,
            },
          },
        }
      )) as StrapiApiResponse<StrapiQuestion>;

      return transformStrapiQuestion(response.data);
    } catch (error: any) {
      console.warn("API Question failed, falling back to mock data:", error);

      // Fallback to mock data when API fails
      return MockQuestionService.getQuestion(Number(id));
    }
  }
}

// Export the service as default and named export
export default QuestionService;
