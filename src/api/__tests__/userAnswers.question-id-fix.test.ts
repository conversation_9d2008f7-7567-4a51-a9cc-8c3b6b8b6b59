import { UserAnswerTransformer } from '../userAnswers';

describe('UserAnswerTransformer - Question ID Mapping Fix', () => {
  const mockMerchandiserDocumentId = 'user_doc_123';
  const mockPlanDocumentId = 'plan_doc_456';

  describe('Question ID Mapping', () => {
    it('should correctly map answers using documentId instead of numeric id', () => {
      // This simulates the real scenario where:
      // - Answers are stored using question documentIds (from AuditDetail.tsx)
      // - Questions array uses documentIds for lookup (from submission process)
      
      const mockQuestions = [
        { id: 'c9bc5k6kddkdiv9paxf2qeos', type: 'text' },
        { id: 'x8ac4j5jccjciv8oaxe1pdns', type: 'yes_no' },
        { id: 'z7bd3i4ibbibiv7naxd0ocmr', type: 'multi_choice' },
      ];

      // Answers stored using documentIds (the fix)
      const answersWithDocumentIds = {
        'c9bc5k6kddkdiv9paxf2qeos': {
          question_id: 'c9bc5k6kddkdiv9paxf2qeos',
          text_value: 'Text answer'
        },
        'x8ac4j5jccjciv8oaxe1pdns': {
          question_id: 'x8ac4j5jccjciv8oaxe1pdns',
          yes_no_multi: {
            answer: {
              "Samsung": {
                "Lỗi POSM": {
                  "POSM": true
                },
                "Lỗi trưng bày": {
                  "Vệ sinh": false,
                  "Vật dụng thừa": true
                }
              }
            },
            options: null
          }
        },
        // z7bd3i4ibbibiv7naxd0ocmr is unanswered
      };

      const results = UserAnswerTransformer.transformAnswersToUserAnswers(
        answersWithDocumentIds,
        mockMerchandiserDocumentId,
        mockPlanDocumentId,
        mockQuestions
      );

      expect(results).toHaveLength(3);

      // Check text question - should be answered
      expect(results[0].question).toBe('c9bc5k6kddkdiv9paxf2qeos');
      expect(results[0].text_value).toBe('Text answer');

      // Check yes_no question - should have hierarchical structure
      expect(results[1].question).toBe('x8ac4j5jccjciv8oaxe1pdns');
      expect(results[1].yes_no_multi).not.toBeNull();
      expect(results[1].yes_no_multi.answer).toEqual({
        "Samsung": {
          "Lỗi POSM": {
            "POSM": true
          },
          "Lỗi trưng bày": {
            "Vệ sinh": false,
            "Vật dụng thừa": true
          }
        }
      });

      // Check multi_choice question - should be unanswered (null)
      expect(results[2].question).toBe('z7bd3i4ibbibiv7naxd0ocmr');
      expect(results[2].multi_choice_values).toBeNull();
    });

    it('should handle the old problematic scenario (numeric IDs)', () => {
      // This simulates the old broken scenario where:
      // - Answers were stored using numeric IDs
      // - Questions array used documentIds for lookup
      // - Result: All questions appeared as "unanswered"
      
      const mockQuestions = [
        { id: 'c9bc5k6kddkdiv9paxf2qeos', type: 'text' },
        { id: 'x8ac4j5jccjciv8oaxe1pdns', type: 'yes_no' },
      ];

      // Answers stored using numeric IDs (the old broken way)
      const answersWithNumericIds = {
        '9': {
          "Samsung": {
            "Lỗi POSM": {
              "POSM": true
            },
            "Lỗi trưng bày": {
              "Vệ sinh": false,
              "Vật dụng thừa": true
            }
          }
        },
        '10': 'Text answer'
      };

      const results = UserAnswerTransformer.transformAnswersToUserAnswers(
        answersWithNumericIds,
        mockMerchandiserDocumentId,
        mockPlanDocumentId,
        mockQuestions
      );

      expect(results).toHaveLength(2);

      // Both questions should be unanswered because of ID mismatch
      expect(results[0].text_value).toBeNull();
      expect(results[1].yes_no_multi).toBeNull();
    });

    it('should demonstrate the fix working with real hierarchical data', () => {
      const mockQuestions = [
        { id: 'hierarchical_question_doc_id', type: 'yes_no' },
      ];

      // Real hierarchical data structure from YesNoQuestion component
      const realHierarchicalAnswers = {
        'hierarchical_question_doc_id': {
          question_id: 'hierarchical_question_doc_id',
          yes_no_multi: {
            answer: {
              "Samsung": {
                "Lỗi POSM": {
                  "POSM": true
                },
                "Lỗi trưng bày": {
                  "Vệ sinh": false,
                  "Vật dụng thừa": true,
                  "Form cấu hình đầy đủ": null,
                  "Demo hoạt động: pin, alarm, wifi,...": null,
                  "Trưng bày đúng số lượng demo theo quy định": true
                },
                "Bảng giá điện tử": {
                  "BGĐT đúng loại, vị trí": null
                }
              },
              "Audio": {
                "Lỗi POSM": {
                  "POSM": true
                },
                "Lỗi trưng bày": {
                  "Vệ sinh": true,
                  "Vật dụng thừa": false,
                  "Form cấu hình đầy đủ": null,
                  "Demo hoạt động: pin, alarm, wifi,...": null,
                  "Trưng bày đúng số lượng demo theo quy định": null
                }
              }
            },
            options: null
          }
        }
      };

      const results = UserAnswerTransformer.transformAnswersToUserAnswers(
        realHierarchicalAnswers,
        mockMerchandiserDocumentId,
        mockPlanDocumentId,
        mockQuestions
      );

      expect(results).toHaveLength(1);
      
      const result = results[0];
      expect(result.question).toBe('hierarchical_question_doc_id');
      expect(result.yes_no_multi).not.toBeNull();
      
      // Verify the complete hierarchical structure is preserved
      const savedAnswer = result.yes_no_multi.answer;
      
      // Samsung section
      expect(savedAnswer.Samsung["Lỗi POSM"].POSM).toBe(true);
      expect(savedAnswer.Samsung["Lỗi trưng bày"]["Vệ sinh"]).toBe(false);
      expect(savedAnswer.Samsung["Lỗi trưng bày"]["Vật dụng thừa"]).toBe(true);
      expect(savedAnswer.Samsung["Lỗi trưng bày"]["Form cấu hình đầy đủ"]).toBeNull();
      expect(savedAnswer.Samsung["Lỗi trưng bày"]["Demo hoạt động: pin, alarm, wifi,..."]).toBeNull();
      expect(savedAnswer.Samsung["Lỗi trưng bày"]["Trưng bày đúng số lượng demo theo quy định"]).toBe(true);
      expect(savedAnswer.Samsung["Bảng giá điện tử"]["BGĐT đúng loại, vị trí"]).toBeNull();
      
      // Audio section
      expect(savedAnswer.Audio["Lỗi POSM"].POSM).toBe(true);
      expect(savedAnswer.Audio["Lỗi trưng bày"]["Vệ sinh"]).toBe(true);
      expect(savedAnswer.Audio["Lỗi trưng bày"]["Vật dụng thừa"]).toBe(false);
      expect(savedAnswer.Audio["Lỗi trưng bày"]["Form cấu hình đầy đủ"]).toBeNull();
      expect(savedAnswer.Audio["Lỗi trưng bày"]["Demo hoạt động: pin, alarm, wifi,..."]).toBeNull();
      expect(savedAnswer.Audio["Lỗi trưng bày"]["Trưng bày đúng số lượng demo theo quy định"]).toBeNull();
    });
  });

  describe('Data Flow Verification', () => {
    it('should verify the complete data flow from YesNoQuestion to database', () => {
      // This test simulates the complete flow:
      // 1. YesNoQuestion component creates hierarchical data
      // 2. AuditDetail stores it using documentId
      // 3. Submission process finds it using documentId
      // 4. Database receives complete hierarchical structure

      const questionDocumentId = 'test_question_doc_id';
      
      // Step 1: YesNoQuestion creates hierarchical data
      const hierarchicalData = {
        "Category1": {
          "Subcategory1": {
            "Item1": true,
            "Item2": false,
            "Item3": null
          }
        }
      };

      // Step 2: AuditDetail wraps and stores using documentId
      const auditDetailAnswer = {
        [questionDocumentId]: {
          question_id: questionDocumentId,
          yes_no_multi: {
            answer: hierarchicalData,
            options: null
          }
        }
      };

      // Step 3: Submission process uses documentId for lookup
      const questions = [
        { id: questionDocumentId, type: 'yes_no' }
      ];

      // Step 4: Transform and verify database structure
      const results = UserAnswerTransformer.transformAnswersToUserAnswers(
        auditDetailAnswer,
        mockMerchandiserDocumentId,
        mockPlanDocumentId,
        questions
      );

      expect(results).toHaveLength(1);
      
      const databaseRecord = results[0];
      
      // Verify the complete structure that will be saved to database
      expect(databaseRecord.question).toBe(questionDocumentId);
      expect(databaseRecord.merchandiser).toBe(mockMerchandiserDocumentId);
      expect(databaseRecord.plan).toBe(mockPlanDocumentId);
      expect(databaseRecord.yes_no_multi).not.toBeNull();
      expect(databaseRecord.yes_no_multi.answer).toEqual(hierarchicalData);
      expect(databaseRecord.yes_no_multi.question_id).toBe(questionDocumentId);
      expect(databaseRecord.yes_no_multi.timestamp).toBeDefined();
    });
  });
});
