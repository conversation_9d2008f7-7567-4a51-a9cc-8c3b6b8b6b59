import { UserAnswerTransformer } from '../userAnswers';

describe('UserAnswerTransformer - Hierarchical Yes/No Structure', () => {
  const mockMerchandiserDocumentId = 'user_doc_123';
  const mockPlanDocumentId = 'plan_doc_456';

  describe('Hierarchical Yes/No Data Preservation', () => {
    it('should preserve hierarchical structure from YesNoQuestion component', () => {
      // This is the structure that comes from YesNoQuestion component
      const hierarchicalAnswer = {
        "Samsung": {
          "Lỗi POSM": {
            "POSM": true
          },
          "Lỗi trưng bày": {
            "Vệ sinh": false,
            "Vật dụng thừa": true,
            "Form cấu hình đầy đủ": null,
            "Demo hoạt động: pin, alarm, wifi,...": null,
            "Trưng bày đúng số lượng demo theo quy định": true
          },
          "Bảng giá điện tử": {
            "BGĐT đúng loại, vị trí": null
          }
        }
      };

      // This is how AuditFormScreen wraps it
      const wrappedAnswer = {
        answer: hierarchicalAnswer,
        options: null
      };

      const result = UserAnswerTransformer.transformAnswerToUserAnswer(
        'q1_doc',
        wrappedAnswer,
        'yes_no',
        mockMerchandiserDocumentId,
        mockPlanDocumentId
      );

      expect(result).toHaveProperty('yes_no_multi');
      expect(result.yes_no_multi).not.toBeNull();
      expect(result.yes_no_multi.answer).toEqual(hierarchicalAnswer);
      expect(result.yes_no_multi.question_id).toBe('q1_doc');
      expect(result.yes_no_multi.timestamp).toBeDefined();
    });

    it('should handle direct hierarchical structure (without wrapper)', () => {
      // Sometimes the hierarchical structure might come directly
      const directHierarchicalAnswer = {
        "Oppo": {
          "Lỗi POSM": {
            "POSM": false
          },
          "Lỗi trưng bày": {
            "Vệ sinh": true,
            "Vật dụng thừa": false
          }
        }
      };

      const result = UserAnswerTransformer.transformAnswerToUserAnswer(
        'q2_doc',
        directHierarchicalAnswer,
        'yes_no',
        mockMerchandiserDocumentId,
        mockPlanDocumentId
      );

      expect(result).toHaveProperty('yes_no_multi');
      expect(result.yes_no_multi).not.toBeNull();
      expect(result.yes_no_multi.answer).toEqual(directHierarchicalAnswer);
      expect(result.yes_no_multi.question_id).toBe('q2_doc');
    });

    it('should handle complete audit form submission with hierarchical yes_no', () => {
      const mockQuestions = [
        { id: 'q1_doc', type: 'text' },
        { id: 'q2_doc', type: 'yes_no' },
        { id: 'q3_doc', type: 'yes_no' },
      ];

      // Simulate answers from audit form submission
      const auditFormAnswers = {
        'q1_doc': {
          question_id: 'q1_doc',
          text_value: 'Text answer'
        },
        'q2_doc': {
          question_id: 'q2_doc',
          yes_no_multi: {
            answer: {
              "Samsung": {
                "Lỗi POSM": {
                  "POSM": true
                },
                "Lỗi trưng bày": {
                  "Vệ sinh": false,
                  "Vật dụng thừa": true
                }
              }
            },
            options: null
          }
        },
        // q3_doc is unanswered
      };

      const results = UserAnswerTransformer.transformAnswersToUserAnswers(
        auditFormAnswers,
        mockMerchandiserDocumentId,
        mockPlanDocumentId,
        mockQuestions
      );

      expect(results).toHaveLength(3);

      // Check text question
      expect(results[0].text_value).toBe('Text answer');

      // Check hierarchical yes_no question
      expect(results[1].yes_no_multi).not.toBeNull();
      expect(results[1].yes_no_multi.answer).toEqual({
        "Samsung": {
          "Lỗi POSM": {
            "POSM": true
          },
          "Lỗi trưng bày": {
            "Vệ sinh": false,
            "Vật dụng thừa": true
          }
        }
      });

      // Check unanswered yes_no question
      expect(results[2].yes_no_multi).toBeNull();
    });

    it('should preserve nested boolean values correctly', () => {
      const complexHierarchy = {
        "Audio": {
          "Lỗi POSM": {
            "POSM": true
          },
          "Lỗi trưng bày": {
            "Trưng bày đúng số lượng demo theo quy định": false,
            "Demo hoạt động: pin, alarm, wifi,...": null,
            "Form cấu hình đầy đủ": null,
            "Vật dụng thừa": true,
            "Vệ sinh": true
          }
        }
      };

      const wrappedAnswer = {
        answer: complexHierarchy,
        options: null
      };

      const result = UserAnswerTransformer.transformAnswerToUserAnswer(
        'q3_doc',
        wrappedAnswer,
        'yes_no',
        mockMerchandiserDocumentId,
        mockPlanDocumentId
      );

      const savedAnswer = result.yes_no_multi.answer;
      
      // Verify specific boolean values are preserved
      expect(savedAnswer.Audio["Lỗi POSM"].POSM).toBe(true);
      expect(savedAnswer.Audio["Lỗi trưng bày"]["Trưng bày đúng số lượng demo theo quy định"]).toBe(false);
      expect(savedAnswer.Audio["Lỗi trưng bày"]["Demo hoạt động: pin, alarm, wifi,..."]).toBeNull();
      expect(savedAnswer.Audio["Lỗi trưng bày"]["Vật dụng thừa"]).toBe(true);
      expect(savedAnswer.Audio["Lỗi trưng bày"]["Vệ sinh"]).toBe(true);
    });

    it('should handle null values for unanswered hierarchical questions', () => {
      const result = UserAnswerTransformer.transformAnswerToUserAnswer(
        'q4_doc',
        null,
        'yes_no',
        mockMerchandiserDocumentId,
        mockPlanDocumentId
      );

      expect(result).toHaveProperty('yes_no_multi', null);
      expect(result.hasOwnProperty('yes_no_multi')).toBe(true);
    });
  });

  describe('Data Structure Validation', () => {
    it('should create valid structure for database storage', () => {
      const hierarchicalData = {
        "Category1": {
          "Subcategory1": {
            "Item1": true,
            "Item2": false,
            "Item3": null
          }
        }
      };

      const wrappedAnswer = {
        answer: hierarchicalData,
        options: null
      };

      const result = UserAnswerTransformer.transformAnswerToUserAnswer(
        'q5_doc',
        wrappedAnswer,
        'yes_no',
        mockMerchandiserDocumentId,
        mockPlanDocumentId
      );

      // Verify the structure is suitable for JSON storage
      expect(() => JSON.stringify(result.yes_no_multi)).not.toThrow();
      
      // Verify the structure can be parsed back
      const jsonString = JSON.stringify(result.yes_no_multi);
      const parsedBack = JSON.parse(jsonString);
      
      expect(parsedBack.answer).toEqual(hierarchicalData);
      expect(parsedBack.question_id).toBe('q5_doc');
      expect(parsedBack.timestamp).toBeDefined();
    });
  });
});
