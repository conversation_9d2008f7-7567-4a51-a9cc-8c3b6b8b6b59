import { UserAnswerTransformer } from '../userAnswers';

describe('UserAnswerTransformer - Complete Audit Trail', () => {
  const mockMerchandiserDocumentId = 'user_doc_123';
  const mockPlanDocumentId = 'plan_doc_456';
  
  const mockQuestions = [
    { id: 'q1_doc', type: 'text' },
    { id: 'q2_doc', type: 'yes_no' },
    { id: 'q3_doc', type: 'multi_choice' },
    { id: 'q4_doc', type: 'photo' },
    { id: 'q5_doc', type: 'rating' },
  ];

  describe('transformAnswersToUserAnswers - Complete Coverage', () => {
    it('should process ALL questions including unanswered ones', () => {
      // Simulate partial answers - only some questions answered
      const partialAnswers = {
        'q1_doc': 'This is a text answer',
        'q2_doc': {
          question_id: 'q2_doc',
          yes_no_multi: {
            answer: true
          }
        },
        // q3_doc, q4_doc, q5_doc are intentionally unanswered
      };

      const result = UserAnswerTransformer.transformAnswersToUserAnswers(
        partialAnswers,
        mockMerchandiserDocumentId,
        mockPlanDocumentId,
        mockQuestions
      );

      // Should return entries for ALL questions, not just answered ones
      expect(result).toHaveLength(5);
      
      // Check answered questions
      expect(result[0]).toEqual({
        merchandiser: mockMerchandiserDocumentId,
        plan: mockPlanDocumentId,
        question: 'q1_doc',
        text_value: 'This is a text answer',
      });

      expect(result[1]).toEqual({
        merchandiser: mockMerchandiserDocumentId,
        plan: mockPlanDocumentId,
        question: 'q2_doc',
        yes_no_multi: {
          answer: true,
          question_id: 'q2_doc',
          timestamp: expect.any(String),
        },
      });

      // Check unanswered questions have null values
      expect(result[2]).toEqual({
        merchandiser: mockMerchandiserDocumentId,
        plan: mockPlanDocumentId,
        question: 'q3_doc',
        multi_choice_values: null,
      });

      expect(result[3]).toEqual({
        merchandiser: mockMerchandiserDocumentId,
        plan: mockPlanDocumentId,
        question: 'q4_doc',
        photo_value: null,
      });

      expect(result[4]).toEqual({
        merchandiser: mockMerchandiserDocumentId,
        plan: mockPlanDocumentId,
        question: 'q5_doc',
        text_value: null,
      });
    });

    it('should handle completely unanswered audit form', () => {
      const emptyAnswers = {};

      const result = UserAnswerTransformer.transformAnswersToUserAnswers(
        emptyAnswers,
        mockMerchandiserDocumentId,
        mockPlanDocumentId,
        mockQuestions
      );

      // Should still return entries for ALL questions with null values
      expect(result).toHaveLength(5);
      
      result.forEach((answer, index) => {
        expect(answer.merchandiser).toBe(mockMerchandiserDocumentId);
        expect(answer.plan).toBe(mockPlanDocumentId);
        expect(answer.question).toBe(mockQuestions[index].id);
        
        // All should have null values for their respective answer fields
        const questionType = mockQuestions[index].type;
        switch (questionType) {
          case 'text':
          case 'rating':
            expect(answer.text_value).toBeNull();
            break;
          case 'yes_no':
            expect(answer.yes_no_multi).toBeNull();
            break;
          case 'multi_choice':
            expect(answer.multi_choice_values).toBeNull();
            break;
          case 'photo':
            expect(answer.photo_value).toBeNull();
            break;
        }
      });
    });

    it('should handle fully answered audit form', () => {
      const fullAnswers = {
        'q1_doc': 'Complete text answer',
        'q2_doc': {
          question_id: 'q2_doc',
          yes_no_multi: {
            answer: false
          }
        },
        'q3_doc': {
          question_id: 'q3_doc',
          multi_choice_values: [
            { id: 1, label: 'Option 1', value: 'opt1' },
            { id: 2, label: 'Option 2', value: 'opt2' }
          ]
        },
        'q4_doc': {
          question_id: 'q4_doc',
          photo_value: [1, 2, 3]
        },
        'q5_doc': 4
      };

      const result = UserAnswerTransformer.transformAnswersToUserAnswers(
        fullAnswers,
        mockMerchandiserDocumentId,
        mockPlanDocumentId,
        mockQuestions
      );

      // Should return entries for ALL questions with actual values
      expect(result).toHaveLength(5);
      
      // All questions should have non-null values
      expect(result[0].text_value).toBe('Complete text answer');
      expect(result[1].yes_no_multi).toEqual({
        answer: false,
        question_id: 'q2_doc',
        timestamp: expect.any(String),
      });
      expect(result[2].multi_choice_values).toHaveLength(2);
      expect(result[3].photo_value).toEqual([1, 2, 3]);
      expect(result[4].text_value).toBe('4');
    });
  });

  describe('Audit Trail Benefits', () => {
    it('should provide complete audit trail showing presented vs answered questions', () => {
      const mixedAnswers = {
        'q1_doc': 'Answered',
        'q3_doc': {
          question_id: 'q3_doc',
          multi_choice_values: [{ id: 1, label: 'Selected', value: 'sel' }]
        }
        // q2_doc, q4_doc, q5_doc intentionally skipped
      };

      const result = UserAnswerTransformer.transformAnswersToUserAnswers(
        mixedAnswers,
        mockMerchandiserDocumentId,
        mockPlanDocumentId,
        mockQuestions
      );

      // Verify we can distinguish between answered and unanswered
      const answeredQuestions = result.filter(answer => {
        return answer.text_value !== null || 
               answer.yes_no_multi !== null || 
               answer.multi_choice_values !== null || 
               answer.photo_value !== null;
      });

      const unansweredQuestions = result.filter(answer => {
        return answer.text_value === null && 
               answer.yes_no_multi === null && 
               answer.multi_choice_values === null && 
               answer.photo_value === null;
      });

      expect(answeredQuestions).toHaveLength(2); // q1_doc and q3_doc
      expect(unansweredQuestions).toHaveLength(3); // q2_doc, q4_doc, q5_doc
      expect(result).toHaveLength(5); // Total questions presented
    });
  });
});
