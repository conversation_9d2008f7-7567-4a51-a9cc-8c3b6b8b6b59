import { UserAnswerTransformer } from '../userAnswers';

describe('UserAnswerTransformer - Data Structure Fix', () => {
  const mockMerchandiserDocumentId = 'user_doc_123';
  const mockPlanDocumentId = 'plan_doc_456';

  describe('transformAnswerToUserAnswer - Field Presence', () => {
    it('should include text_value field for text questions (answered)', () => {
      const result = UserAnswerTransformer.transformAnswerToUserAnswer(
        'q1_doc',
        'This is an answer',
        'text',
        mockMerchandiserDocumentId,
        mockPlanDocumentId
      );

      expect(result).toHaveProperty('text_value', 'This is an answer');
      expect(result.hasOwnProperty('text_value')).toBe(true);
    });

    it('should include text_value field for text questions (unanswered)', () => {
      const result = UserAnswerTransformer.transformAnswerToUserAnswer(
        'q1_doc',
        null,
        'text',
        mockMerchandiserDocumentId,
        mockPlanDocumentId
      );

      expect(result).toHaveProperty('text_value', null);
      expect(result.hasOwnProperty('text_value')).toBe(true);
    });

    it('should include yes_no_multi field for yes_no questions (answered)', () => {
      const result = UserAnswerTransformer.transformAnswerToUserAnswer(
        'q2_doc',
        true,
        'yes_no',
        mockMerchandiserDocumentId,
        mockPlanDocumentId
      );

      expect(result).toHaveProperty('yes_no_multi');
      expect(result.yes_no_multi).toEqual({
        answer: true,
        question_id: 'q2_doc',
        timestamp: expect.any(String),
      });
      expect(result.hasOwnProperty('yes_no_multi')).toBe(true);
    });

    it('should include yes_no_multi field for yes_no questions (unanswered)', () => {
      const result = UserAnswerTransformer.transformAnswerToUserAnswer(
        'q2_doc',
        null,
        'yes_no',
        mockMerchandiserDocumentId,
        mockPlanDocumentId
      );

      expect(result).toHaveProperty('yes_no_multi', null);
      expect(result.hasOwnProperty('yes_no_multi')).toBe(true);
    });

    it('should include multi_choice_values field for multi_choice questions (answered)', () => {
      const choices = [
        { id: 1, label: 'Option 1', value: 'opt1' },
        { id: 2, label: 'Option 2', value: 'opt2' }
      ];

      const result = UserAnswerTransformer.transformAnswerToUserAnswer(
        'q3_doc',
        choices,
        'multi_choice',
        mockMerchandiserDocumentId,
        mockPlanDocumentId
      );

      expect(result).toHaveProperty('multi_choice_values');
      expect(result.multi_choice_values).toHaveLength(2);
      expect(result.hasOwnProperty('multi_choice_values')).toBe(true);
    });

    it('should include multi_choice_values field for multi_choice questions (unanswered)', () => {
      const result = UserAnswerTransformer.transformAnswerToUserAnswer(
        'q3_doc',
        null,
        'multi_choice',
        mockMerchandiserDocumentId,
        mockPlanDocumentId
      );

      expect(result).toHaveProperty('multi_choice_values', null);
      expect(result.hasOwnProperty('multi_choice_values')).toBe(true);
    });

    it('should include photo_value field for photo questions (answered)', () => {
      const result = UserAnswerTransformer.transformAnswerToUserAnswer(
        'q4_doc',
        [1, 2, 3],
        'photo',
        mockMerchandiserDocumentId,
        mockPlanDocumentId
      );

      expect(result).toHaveProperty('photo_value', [1, 2, 3]);
      expect(result.hasOwnProperty('photo_value')).toBe(true);
    });

    it('should include photo_value field for photo questions (unanswered)', () => {
      const result = UserAnswerTransformer.transformAnswerToUserAnswer(
        'q4_doc',
        null,
        'photo',
        mockMerchandiserDocumentId,
        mockPlanDocumentId
      );

      expect(result).toHaveProperty('photo_value', null);
      expect(result.hasOwnProperty('photo_value')).toBe(true);
    });
  });

  describe('transformAnswersToUserAnswers - Complete Data Structure', () => {
    it('should create records with proper field presence for mixed answers', () => {
      const mockQuestions = [
        { id: 'q1_doc', type: 'text' },
        { id: 'q2_doc', type: 'yes_no' },
        { id: 'q3_doc', type: 'multi_choice' },
        { id: 'q4_doc', type: 'photo' },
      ];

      const mixedAnswers = {
        'q1_doc': 'Text answer',
        'q2_doc': {
          question_id: 'q2_doc',
          yes_no_multi: {
            answer: true
          }
        },
        // q3_doc and q4_doc are unanswered (null)
      };

      const results = UserAnswerTransformer.transformAnswersToUserAnswers(
        mixedAnswers,
        mockMerchandiserDocumentId,
        mockPlanDocumentId,
        mockQuestions
      );

      expect(results).toHaveLength(4);

      // Check answered questions have proper values
      expect(results[0].hasOwnProperty('text_value')).toBe(true);
      expect(results[0].text_value).toBe('Text answer');

      expect(results[1].hasOwnProperty('yes_no_multi')).toBe(true);
      expect(results[1].yes_no_multi).toEqual({
        answer: true,
        question_id: 'q2_doc',
        timestamp: expect.any(String),
      });

      // Check unanswered questions have null values but fields are present
      expect(results[2].hasOwnProperty('multi_choice_values')).toBe(true);
      expect(results[2].multi_choice_values).toBeNull();

      expect(results[3].hasOwnProperty('photo_value')).toBe(true);
      expect(results[3].photo_value).toBeNull();
    });
  });

  describe('Data Structure Validation', () => {
    it('should pass validation for answered questions', () => {
      const answeredData = {
        merchandiser: mockMerchandiserDocumentId,
        plan: mockPlanDocumentId,
        question: 'q1_doc',
        text_value: 'Answer'
      };

      // This should not throw an error
      expect(() => {
        // Simulate the validation that happens in saveUserAnswer
        const hasAnswerField = !!(
          answeredData.hasOwnProperty('text_value') ||
          answeredData.hasOwnProperty('boolean_value') ||
          answeredData.hasOwnProperty('photo_value') ||
          answeredData.hasOwnProperty('multi_choice_values') ||
          answeredData.hasOwnProperty('yes_no_multi')
        );
        expect(hasAnswerField).toBe(true);
      }).not.toThrow();
    });

    it('should pass validation for unanswered questions with null values', () => {
      const unansweredData = {
        merchandiser: mockMerchandiserDocumentId,
        plan: mockPlanDocumentId,
        question: 'q1_doc',
        text_value: null
      };

      // This should not throw an error
      expect(() => {
        // Simulate the validation that happens in saveUserAnswer
        const hasAnswerField = !!(
          unansweredData.hasOwnProperty('text_value') ||
          unansweredData.hasOwnProperty('boolean_value') ||
          unansweredData.hasOwnProperty('photo_value') ||
          unansweredData.hasOwnProperty('multi_choice_values') ||
          unansweredData.hasOwnProperty('yes_no_multi')
        );
        expect(hasAnswerField).toBe(true);
      }).not.toThrow();
    });
  });
});
