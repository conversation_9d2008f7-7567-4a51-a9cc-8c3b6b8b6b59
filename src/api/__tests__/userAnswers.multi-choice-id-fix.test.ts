import { UserAnswerTransformer } from '../userAnswers';

describe('UserAnswerTransformer - Multi-Choice Question ID Mapping Fix', () => {
  const mockMerchandiserDocumentId = 'user_doc_123';
  const mockPlanDocumentId = 'plan_doc_456';

  describe('Multi-Choice Question ID Mapping', () => {
    it('should correctly map multi-choice answers using documentId instead of numeric id', () => {
      // This simulates the real scenario where:
      // - Answers are stored using question documentIds (from AuditDetail.tsx and AuditFormScreen.tsx)
      // - Questions array uses documentIds for lookup (from submission process)
      
      const mockQuestions = [
        { id: 'multi_choice_doc_id_1', type: 'multi_choice' },
        { id: 'multi_choice_doc_id_2', type: 'multi_choice' },
        { id: 'text_doc_id_3', type: 'text' },
      ];

      // Answers stored using documentIds (the fix)
      const answersWithDocumentIds = {
        'multi_choice_doc_id_1': {
          question_id: 'multi_choice_doc_id_1',
          multi_choice_values: ['option1', 'option3', 'option5']
        },
        'multi_choice_doc_id_2': {
          question_id: 'multi_choice_doc_id_2',
          multi_choice_values: ['choice_a', 'choice_c']
        },
        'text_doc_id_3': {
          question_id: 'text_doc_id_3',
          text_value: 'Sample text answer'
        }
      };

      const results = UserAnswerTransformer.transformAnswersToUserAnswers(
        answersWithDocumentIds,
        mockMerchandiserDocumentId,
        mockPlanDocumentId,
        mockQuestions
      );

      expect(results).toHaveLength(3);

      // Check first multi_choice question - should be answered
      expect(results[0].question).toBe('multi_choice_doc_id_1');
      expect(results[0].multi_choice_values).toEqual(['option1', 'option3', 'option5']);
      expect(results[0].text_value).toBeNull();
      expect(results[0].yes_no_multi).toBeNull();

      // Check second multi_choice question - should be answered
      expect(results[1].question).toBe('multi_choice_doc_id_2');
      expect(results[1].multi_choice_values).toEqual(['choice_a', 'choice_c']);
      expect(results[1].text_value).toBeNull();
      expect(results[1].yes_no_multi).toBeNull();

      // Check text question - should be answered
      expect(results[2].question).toBe('text_doc_id_3');
      expect(results[2].text_value).toBe('Sample text answer');
      expect(results[2].multi_choice_values).toBeNull();
      expect(results[2].yes_no_multi).toBeNull();
    });

    it('should handle the old problematic scenario (numeric IDs) for multi_choice', () => {
      // This simulates the old broken scenario where:
      // - Answers were stored using numeric IDs
      // - Questions array used documentIds for lookup
      // - Result: All questions appeared as "unanswered"
      
      const mockQuestions = [
        { id: 'multi_choice_doc_id_1', type: 'multi_choice' },
        { id: 'multi_choice_doc_id_2', type: 'multi_choice' },
      ];

      // Answers stored using numeric IDs (the old broken way)
      const answersWithNumericIds = {
        '15': ['option1', 'option3', 'option5'],
        '16': ['choice_a', 'choice_c']
      };

      const results = UserAnswerTransformer.transformAnswersToUserAnswers(
        answersWithNumericIds,
        mockMerchandiserDocumentId,
        mockPlanDocumentId,
        mockQuestions
      );

      expect(results).toHaveLength(2);

      // Both questions should be unanswered because of ID mismatch
      expect(results[0].multi_choice_values).toBeNull();
      expect(results[1].multi_choice_values).toBeNull();
    });

    it('should demonstrate the fix working with real multi_choice data', () => {
      const mockQuestions = [
        { id: 'brand_preference_doc_id', type: 'multi_choice' },
        { id: 'store_features_doc_id', type: 'multi_choice' },
      ];

      // Real multi_choice data structure from MultiChoiceQuestion component
      const realMultiChoiceAnswers = {
        'brand_preference_doc_id': {
          question_id: 'brand_preference_doc_id',
          multi_choice_values: ['Samsung', 'Apple', 'Oppo', 'Xiaomi']
        },
        'store_features_doc_id': {
          question_id: 'store_features_doc_id',
          multi_choice_values: ['wifi', 'parking', 'air_conditioning', 'security_camera']
        }
      };

      const results = UserAnswerTransformer.transformAnswersToUserAnswers(
        realMultiChoiceAnswers,
        mockMerchandiserDocumentId,
        mockPlanDocumentId,
        mockQuestions
      );

      expect(results).toHaveLength(2);
      
      // Verify first multi_choice question
      const brandResult = results[0];
      expect(brandResult.question).toBe('brand_preference_doc_id');
      expect(brandResult.multi_choice_values).toEqual(['Samsung', 'Apple', 'Oppo', 'Xiaomi']);
      expect(brandResult.merchandiser).toBe(mockMerchandiserDocumentId);
      expect(brandResult.plan).toBe(mockPlanDocumentId);
      
      // Verify second multi_choice question
      const featuresResult = results[1];
      expect(featuresResult.question).toBe('store_features_doc_id');
      expect(featuresResult.multi_choice_values).toEqual(['wifi', 'parking', 'air_conditioning', 'security_camera']);
      expect(featuresResult.merchandiser).toBe(mockMerchandiserDocumentId);
      expect(featuresResult.plan).toBe(mockPlanDocumentId);
    });
  });

  describe('Data Flow Verification for Multi-Choice', () => {
    it('should verify the complete data flow from MultiChoiceQuestion to database', () => {
      // This test simulates the complete flow:
      // 1. MultiChoiceQuestion component creates array of selected values
      // 2. AuditFormScreen stores it using documentId
      // 3. Submission process finds it using documentId
      // 4. Database receives complete multi_choice_values array

      const questionDocumentId = 'test_multi_choice_doc_id';
      
      // Step 1: MultiChoiceQuestion creates array of selected values
      const selectedValues = ['choice_1', 'choice_3', 'choice_5'];

      // Step 2: AuditFormScreen wraps and stores using documentId
      const auditFormAnswer = {
        [questionDocumentId]: {
          question_id: questionDocumentId,
          multi_choice_values: selectedValues
        }
      };

      // Step 3: Submission process uses documentId for lookup
      const questions = [
        { id: questionDocumentId, type: 'multi_choice' }
      ];

      // Step 4: Transform and verify database structure
      const results = UserAnswerTransformer.transformAnswersToUserAnswers(
        auditFormAnswer,
        mockMerchandiserDocumentId,
        mockPlanDocumentId,
        questions
      );

      expect(results).toHaveLength(1);
      
      const databaseRecord = results[0];
      
      // Verify the complete structure that will be saved to database
      expect(databaseRecord.question).toBe(questionDocumentId);
      expect(databaseRecord.merchandiser).toBe(mockMerchandiserDocumentId);
      expect(databaseRecord.plan).toBe(mockPlanDocumentId);
      expect(databaseRecord.multi_choice_values).toEqual(selectedValues);
      expect(databaseRecord.text_value).toBeNull();
      expect(databaseRecord.yes_no_multi).toBeNull();
    });

    it('should handle mixed question types with consistent ID mapping', () => {
      // Test that multi_choice, yes_no, and text questions all use consistent ID mapping
      const questions = [
        { id: 'multi_choice_doc_1', type: 'multi_choice' },
        { id: 'yes_no_doc_2', type: 'yes_no' },
        { id: 'text_doc_3', type: 'text' },
      ];

      const mixedAnswers = {
        'multi_choice_doc_1': {
          question_id: 'multi_choice_doc_1',
          multi_choice_values: ['option_a', 'option_c']
        },
        'yes_no_doc_2': {
          question_id: 'yes_no_doc_2',
          yes_no_multi: {
            answer: {
              "Category1": {
                "Subcategory1": {
                  "Item1": true,
                  "Item2": false
                }
              }
            },
            options: null
          }
        },
        'text_doc_3': {
          question_id: 'text_doc_3',
          text_value: 'Sample text response'
        }
      };

      const results = UserAnswerTransformer.transformAnswersToUserAnswers(
        mixedAnswers,
        mockMerchandiserDocumentId,
        mockPlanDocumentId,
        questions
      );

      expect(results).toHaveLength(3);

      // Verify multi_choice question
      expect(results[0].question).toBe('multi_choice_doc_1');
      expect(results[0].multi_choice_values).toEqual(['option_a', 'option_c']);
      expect(results[0].yes_no_multi).toBeNull();
      expect(results[0].text_value).toBeNull();

      // Verify yes_no question
      expect(results[1].question).toBe('yes_no_doc_2');
      expect(results[1].yes_no_multi).not.toBeNull();
      expect(results[1].yes_no_multi.answer.Category1.Subcategory1.Item1).toBe(true);
      expect(results[1].yes_no_multi.answer.Category1.Subcategory1.Item2).toBe(false);
      expect(results[1].multi_choice_values).toBeNull();
      expect(results[1].text_value).toBeNull();

      // Verify text question
      expect(results[2].question).toBe('text_doc_3');
      expect(results[2].text_value).toBe('Sample text response');
      expect(results[2].multi_choice_values).toBeNull();
      expect(results[2].yes_no_multi).toBeNull();
    });
  });
});
