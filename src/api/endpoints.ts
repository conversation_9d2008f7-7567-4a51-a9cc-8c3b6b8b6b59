// API Endpoints configuration
export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: "/auth/local",
    REGISTER: "/auth/local/register",
    FORGOT_PASSWORD: "/auth/forgot-password",
    RESET_PASSWORD: "/auth/reset-password",
    ME: "/users/me",
  },

  // Core entities
  STORES: "/stores",
  PLANS: "/plans",
  AUDIT_FORMS: "/audit-forms",
  QUESTIONS: "/questions",
  VISITS: "/visits",
  ANSWERS: "/answers",
  USER_ANSWERS: "/user-answers",

  // File upload
  UPLOAD: "/upload",

  // User management
  USERS: "/users",
  ROLES: "/users-permissions/roles",
} as const;

// Helper function to build endpoint with ID
export const buildEndpoint = (
  baseEndpoint: string,
  id?: number | string
): string => {
  return id ? `${baseEndpoint}/${id}` : baseEndpoint;
};

// Helper function to build endpoint with query parameters
export const buildQueryEndpoint = (
  baseEndpoint: string,
  params?: Record<string, string | number | boolean>
): string => {
  if (!params || Object.keys(params).length === 0) {
    return baseEndpoint;
  }

  const searchParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    searchParams.append(key, String(value));
  });

  return `${baseEndpoint}?${searchParams.toString()}`;
};
