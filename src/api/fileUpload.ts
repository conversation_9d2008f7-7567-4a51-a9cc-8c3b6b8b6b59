import { api } from "./client";
import { API_ENDPOINTS } from "./endpoints";
import { CompressedImage } from "../utils/imageUtils";

// File upload types
export interface UploadedFile {
  id: number;
  documentId: string;
  name: string;
  alternativeText?: string;
  caption?: string;
  width?: number;
  height?: number;
  formats?: any;
  hash: string;
  ext: string;
  mime: string;
  size: number;
  url: string;
  previewUrl?: string;
  provider: string;
  provider_metadata?: any;
  createdAt: string;
  updatedAt: string;
}

export interface FileUploadResponse {
  data: UploadedFile[];
}

export interface FileUploadProgress {
  loaded: number;
  total: number;
  percentage: number;
}

/**
 * Upload single file to Strapi v5
 */
export const uploadFile = async (
  file: CompressedImage,
  onProgress?: (progress: FileUploadProgress) => void
): Promise<UploadedFile> => {
  try {
    console.log("🔄 uploadFile: Starting file upload to Strapi v5");
    console.log("📤 File details:", {
      name: file.name,
      size: file.size,
      type: file.type,
      uri: file.uri.substring(0, 50) + "...",
    });

    // Create FormData for file upload
    console.log("🔄 Creating FormData for Strapi v5 upload...");

    // Verify file exists and is accessible
    try {
      // Check if file URI is accessible (React Native specific)
      console.log("🔍 Verifying file accessibility...");
      console.log("🔍 File URI to verify:", file.uri);

      // For React Native, validate URI format - accept both file:// and data: URIs
      if (!file.uri) {
        throw new Error("File URI is missing");
      }

      const isFileUri = file.uri.startsWith("file://");
      const isDataUri = file.uri.startsWith("data:");

      if (!isFileUri && !isDataUri) {
        throw new Error(
          `Invalid file URI format. Expected file:// or data: URI, got: ${file.uri.substring(
            0,
            50
          )}...`
        );
      }

      console.log(`🔍 File URI type: ${isFileUri ? "File URI" : "Data URI"}`);

      // For data URIs, we need to handle them differently
      if (isDataUri) {
        console.log("🔄 Processing data URI for upload...");
        // Data URIs are already in the correct format for React Native FormData
        // The FormData will handle the conversion automatically
      }

      if (!file.type || !file.name) {
        throw new Error(
          `Missing file metadata - type: ${file.type}, name: ${file.name}`
        );
      }

      console.log("✅ File validation passed");
    } catch (validationError) {
      console.error("❌ File validation failed:", validationError);
      throw new Error(`File validation failed: ${validationError.message}`);
    }

    const formData = new FormData();

    // Add file to FormData - React Native format
    console.log("📤 Adding file to FormData with React Native format");
    console.log("📤 File URI:", file.uri.substring(0, 50) + "...");
    console.log("📤 File type:", file.type);
    console.log("📤 File name:", file.name);

    // Environment-specific FormData handling
    // Browser environment requires different approach than React Native mobile
    console.log("📤 Detecting environment for FormData handling");

    // Check if we're in browser environment (React Native Web)
    const isBrowser = typeof window !== "undefined" && window.document;
    console.log(
      "🌐 Environment detected:",
      isBrowser ? "Browser (React Native Web)" : "React Native Mobile"
    );

    if (isBrowser) {
      // Browser environment - use standard web FormData with Blob
      console.log("🌐 Using browser FormData approach for React Native Web");

      try {
        // Convert data URI to blob for browser FormData
        const response = await fetch(file.uri);
        const blob = await response.blob();

        console.log("📤 Browser blob created:", {
          name: file.name,
          type: file.type,
          size: blob.size,
          blobType: blob.type,
        });

        // Use standard web FormData approach - Strapi expects 'files' parameter
        formData.append("files", blob, file.name);
        console.log("✅ Browser blob appended to FormData successfully");
      } catch (blobError) {
        console.error("❌ Failed to create blob in browser:", blobError);
        throw new Error(
          `Failed to process file for browser upload: ${blobError.message}`
        );
      }
    } else {
      // React Native mobile environment - use file object format
      console.log("📱 Using React Native mobile FormData approach");

      const fileObject = {
        uri: file.uri,
        type: file.type,
        name: file.name,
      };

      console.log("📤 Mobile file object for FormData:", {
        name: fileObject.name,
        type: fileObject.type,
        uriType: file.uri.startsWith("data:") ? "Data URI" : "File URI",
        uriLength: file.uri.length,
      });

      // Append to FormData using React Native's native file object support
      formData.append("files", fileObject as any);
      console.log("✅ Mobile file object appended to FormData successfully");
    }

    // Add file info for Strapi v5
    const fileInfo = {
      name: file.name,
      alternativeText: `Audit image - ${file.name}`,
      caption: `Uploaded on ${new Date().toISOString()}`,
    };
    console.log("📤 Adding fileInfo to FormData:", fileInfo);
    formData.append("fileInfo", JSON.stringify(fileInfo));

    console.log("✅ FormData prepared for Strapi v5 upload");

    // Upload with progress tracking
    console.log(
      "🚀 Starting upload to Strapi v5 endpoint:",
      API_ENDPOINTS.UPLOAD
    );
    console.log(
      "🔧 Using explicit multipart/form-data Content-Type for React Native"
    );
    console.log("⏱️ Upload timeout set to 120 seconds for server processing");

    // For React Native, we need to explicitly set the Content-Type to multipart/form-data
    // to prevent XMLHttpRequest from defaulting to application/x-www-form-urlencoded
    const response = await api.post<UploadedFile[]>(
      API_ENDPOINTS.UPLOAD,
      formData,
      {
        timeout: 120000, // 120 seconds timeout for file uploads (server processing time)
        headers: {
          "Content-Type": "multipart/form-data",
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress && progressEvent.total) {
            const progress: FileUploadProgress = {
              loaded: progressEvent.loaded,
              total: progressEvent.total,
              percentage: Math.round(
                (progressEvent.loaded * 100) / progressEvent.total
              ),
            };
            console.log(
              `📤 Upload progress: ${progress.percentage}% (${progress.loaded}/${progress.total})`
            );
            onProgress(progress);
          }
        },
      }
    );

    console.log("✅ File uploaded successfully to Strapi v5");
    console.log("📤 Upload response:", {
      status: response.status,
      statusText: response.statusText,
      dataLength: response.data?.length || 0,
      data: response.data,
    });

    // Strapi v5 returns array of uploaded files
    if (Array.isArray(response) && response.length > 0) {
      const uploadedFile = response[0];

      // Ensure we have the required fields for Strapi v5
      if (!uploadedFile.id || !uploadedFile.url) {
        console.error("Invalid upload response:", uploadedFile);
        throw new Error("Invalid file upload response from server");
      }

      console.log("✅ File upload successful:", {
        id: uploadedFile.id,
        name: uploadedFile.name,
        url: uploadedFile.url,
        size: uploadedFile.size,
      });

      return uploadedFile;
    } else {
      console.error("No files returned from upload:", response);
      throw new Error("No file returned from upload");
    }
  } catch (error: any) {
    console.error("❌ Error uploading file to Strapi v5:", error);

    // Enhanced error handling for different error types
    if (error.code === "ECONNABORTED") {
      console.error(
        "⏱️ Upload timeout - server took too long to respond (120s limit)"
      );
      console.error(
        "💡 This suggests server-side processing issues or resource constraints"
      );
      throw new Error(
        "Upload timeout - server processing took too long (120s). Check server resources."
      );
    } else if (error.response) {
      console.error("🚨 Server error response:", error.response.data);
      console.error("📊 Response status:", error.response.status);
      console.error("📋 Response headers:", error.response.headers);
      const errorMessage =
        error.response.data?.error?.message ||
        error.response.data?.message ||
        "Unknown upload error";
      throw new Error(`Upload failed: ${errorMessage}`);
    } else if (error.request) {
      console.error("🌐 Network error during upload:", error.request);
      console.error("🔍 Request details:", {
        readyState: error.request.readyState,
        status: error.request.status,
        timeout: error.request.timeout,
        response: error.request._response,
      });
      throw new Error(
        "Network error during upload - check server connectivity and network connection"
      );
    } else {
      console.error("⚙️ Upload configuration error:", error.message);
      throw new Error(`Upload failed: ${error.message}`);
    }
  }
};

/**
 * Upload multiple files to Strapi
 */
export const uploadFiles = async (
  files: CompressedImage[],
  onProgress?: (fileIndex: number, progress: FileUploadProgress) => void,
  onFileComplete?: (fileIndex: number, uploadedFile: UploadedFile) => void
): Promise<UploadedFile[]> => {
  try {
    console.log("Uploading multiple files:", files.length);

    const uploadedFiles: UploadedFile[] = [];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      try {
        console.log(`Uploading file ${i + 1}/${files.length}: ${file.name}`);

        const uploadedFile = await uploadFile(file, (progress) => {
          onProgress?.(i, progress);
        });

        uploadedFiles.push(uploadedFile);
        onFileComplete?.(i, uploadedFile);

        console.log(`File ${i + 1} uploaded successfully`);
      } catch (error) {
        console.error(`Failed to upload file ${i + 1}:`, error);
        throw error; // Stop on first failure
      }
    }

    console.log("All files uploaded successfully:", uploadedFiles.length);
    return uploadedFiles;
  } catch (error) {
    console.error("Error in batch upload:", error);
    throw error;
  }
};

/**
 * Delete uploaded file from Strapi
 */
export const deleteFile = async (fileId: number): Promise<void> => {
  try {
    console.log("Deleting file:", fileId);

    await api.delete(`${API_ENDPOINTS.UPLOAD}/files/${fileId}`);

    console.log("File deleted successfully");
  } catch (error: any) {
    console.error("Error deleting file:", error);
    throw new Error(`Failed to delete file: ${error.message}`);
  }
};

/**
 * Get file info from Strapi
 */
export const getFileInfo = async (fileId: number): Promise<UploadedFile> => {
  try {
    console.log("Getting file info:", fileId);

    const response = await api.get<UploadedFile>(
      `${API_ENDPOINTS.UPLOAD}/files/${fileId}`
    );

    return response;
  } catch (error: any) {
    console.error("Error getting file info:", error);
    throw new Error(`Failed to get file info: ${error.message}`);
  }
};

/**
 * Upload images for a specific question
 */
export const uploadQuestionImages = async (
  questionId: string,
  images: CompressedImage[],
  onProgress?: (fileIndex: number, progress: FileUploadProgress) => void
): Promise<UploadedFile[]> => {
  try {
    console.log(
      "Uploading images for question:",
      questionId,
      "Count:",
      images.length
    );

    if (images.length === 0) {
      return [];
    }

    // Upload all images
    const uploadedFiles = await uploadFiles(
      images,
      onProgress,
      (fileIndex, uploadedFile) => {
        console.log(
          `Question ${questionId} - File ${fileIndex + 1} uploaded:`,
          uploadedFile.name
        );
      }
    );

    console.log(
      `All images uploaded for question ${questionId}:`,
      uploadedFiles.length
    );
    return uploadedFiles;
  } catch (error) {
    console.error("Error uploading question images:", error);
    throw error;
  }
};

/**
 * Batch upload images for multiple questions
 */
export const uploadMultipleQuestionImages = async (
  questionImages: { questionId: string; images: CompressedImage[] }[],
  onQuestionProgress?: (
    questionIndex: number,
    fileIndex: number,
    progress: FileUploadProgress
  ) => void
): Promise<{ questionId: string; uploadedFiles: UploadedFile[] }[]> => {
  try {
    console.log("Batch uploading images for questions:", questionImages.length);

    const results: { questionId: string; uploadedFiles: UploadedFile[] }[] = [];

    for (let i = 0; i < questionImages.length; i++) {
      const { questionId, images } = questionImages[i];

      if (images.length === 0) {
        results.push({ questionId, uploadedFiles: [] });
        continue;
      }

      try {
        console.log(
          `Uploading images for question ${i + 1}/${
            questionImages.length
          }: ${questionId}`
        );

        const uploadedFiles = await uploadQuestionImages(
          questionId,
          images,
          (fileIndex, progress) => {
            onQuestionProgress?.(i, fileIndex, progress);
          }
        );

        results.push({ questionId, uploadedFiles });

        console.log(`Question ${i + 1} images uploaded successfully`);
      } catch (error) {
        console.error(`Failed to upload images for question ${i + 1}:`, error);
        throw error; // Stop on first failure
      }
    }

    console.log("All question images uploaded successfully");
    return results;
  } catch (error) {
    console.error("Error in batch question image upload:", error);
    throw error;
  }
};
