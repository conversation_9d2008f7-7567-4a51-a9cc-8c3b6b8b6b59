import { api, ApiError } from "./client";
import { API_ENDPOINTS, buildEndpoint } from "./endpoints";
import { StrapiApiResponse, StrapiEntityData } from "../types/api";
import { FlatPlan } from "../types/entities";

// Strapi API response types for plans
export interface StrapiPlanAttributes {
  assigned_date?: string;
  start_date?: string;
  end_date?: string;
  plan_status?: "pending" | "in_progress" | "completed" | "expired";
  visits_target?: number;
  plan_title?: string;
  store?: {
    data: StrapiEntityData<{
      store_name: string;
      store_code: string;
      address?: string;
      is_active: boolean;
    }> | null;
  };
  audit_form?: {
    data: StrapiEntityData<{
      name: string;
      version?: string;
      form_status: "draft" | "active" | "archived";
    }> | null;
  };
  merchandiser?: {
    data: StrapiEntityData<{
      username: string;
      email: string;
      confirmed: boolean;
      blocked: boolean;
    }> | null;
  };
}

export interface StrapiPlan extends StrapiEntityData<StrapiPlanAttributes> {}

export interface PlansApiResponse extends StrapiApiResponse<StrapiPlan[]> {}
export interface PlanApiResponse extends StrapiApiResponse<StrapiPlan> {}

// Query parameters for filtering and pagination
export interface PlanQueryParams {
  pagination?: {
    page?: number;
    pageSize?: number;
  };
  sort?: string[];
  filters?: {
    plan_status?: {
      $eq?: string;
      $in?: string[];
    };
    // Note: merchandiser filtering is done client-side due to Strapi configuration
    assigned_date?: {
      $gte?: string;
      $lte?: string;
    };
    start_date?: {
      $gte?: string;
      $lte?: string;
    };
    end_date?: {
      $gte?: string;
      $lte?: string;
    };
  };
  populate?: string | string[] | Record<string, any>;
}

// Transform Strapi plan data to flat structure
const transformStrapiPlan = (strapiPlan: any): FlatPlan => {
  // Handle both old Strapi v4 format (with attributes) and new Strapi v5 format (direct fields)
  const isV5Format = !strapiPlan.attributes;

  // Log basic plan transformation info
  console.log(
    "Transforming plan ID:",
    strapiPlan.id,
    "- V5 format:",
    isV5Format
  );

  if (isV5Format) {
    // Strapi v5 format - fields are directly on the object
    return {
      id: strapiPlan.id,
      documentId: strapiPlan.documentId, // Strapi v5 documentId
      assigned_date: strapiPlan.assigned_date,
      start_date: strapiPlan.start_date,
      end_date: strapiPlan.end_date,
      plan_status: strapiPlan.plan_status,
      visits_target: strapiPlan.visits_target,
      plan_title: strapiPlan.plan_title,
      store: strapiPlan.store
        ? {
            id: strapiPlan.store.id,
            store_name: strapiPlan.store.store_name,
            store_code: strapiPlan.store.store_code,
            address: strapiPlan.store.address,
            is_active: strapiPlan.store.is_active,
            createdAt: strapiPlan.store.createdAt,
            updatedAt: strapiPlan.store.updatedAt,
          }
        : undefined,
      audit_form: (() => {
        // Handle different possible audit_form structures
        let auditFormData = strapiPlan.audit_form;

        // If audit_form is wrapped in a data object (some Strapi v5 cases)
        if (auditFormData?.data) {
          auditFormData = auditFormData.data;
        }

        console.log("Processing audit_form for plan:", strapiPlan.id);

        if (auditFormData && auditFormData.id) {
          return {
            id: auditFormData.id,
            documentId: auditFormData.documentId, // Include documentId
            name: auditFormData.name,
            version: auditFormData.version,
            form_status: auditFormData.form_status,
            questions: auditFormData.questions || [], // Include questions with documentIds
            createdAt: auditFormData.createdAt,
            updatedAt: auditFormData.updatedAt,
          };
        }

        console.log("No audit_form found for plan:", strapiPlan.id);
        return undefined;
      })(),
      merchandiser: strapiPlan.merchandiser
        ? {
            id: strapiPlan.merchandiser.id,
            username: strapiPlan.merchandiser.username,
            email: strapiPlan.merchandiser.email,
            confirmed: strapiPlan.merchandiser.confirmed,
            blocked: strapiPlan.merchandiser.blocked,
            createdAt: strapiPlan.merchandiser.createdAt,
            updatedAt: strapiPlan.merchandiser.updatedAt,
          }
        : undefined,
      user_answers: strapiPlan.user_answers || [], // Include user_answers array
      createdAt: strapiPlan.createdAt,
      updatedAt: strapiPlan.updatedAt,
    };
  } else {
    // Strapi v4 format - fields are in attributes
    const { id, attributes } = strapiPlan;

    return {
      id,
      documentId: strapiPlan.documentId, // May exist in v4 as well
      assigned_date: attributes.assigned_date,
      start_date: attributes.start_date,
      end_date: attributes.end_date,
      plan_status: attributes.plan_status,
      visits_target: attributes.visits_target,
      plan_title: attributes.plan_title,
      store: attributes.store?.data
        ? {
            id: attributes.store.data.id,
            store_name: attributes.store.data.attributes.store_name,
            store_code: attributes.store.data.attributes.store_code,
            address: attributes.store.data.attributes.address,
            is_active: attributes.store.data.attributes.is_active,
            createdAt: attributes.store.data.attributes.createdAt,
            updatedAt: attributes.store.data.attributes.updatedAt,
          }
        : undefined,
      audit_form: attributes.audit_form?.data
        ? {
            id: attributes.audit_form.data.id,
            documentId: attributes.audit_form.data.documentId, // Include documentId for v4
            name: attributes.audit_form.data.attributes.name,
            version: attributes.audit_form.data.attributes.version,
            form_status: attributes.audit_form.data.attributes.form_status,
            questions: attributes.audit_form.data.attributes.questions || [], // Include questions
            createdAt: attributes.audit_form.data.attributes.createdAt,
            updatedAt: attributes.audit_form.data.attributes.updatedAt,
          }
        : undefined,
      merchandiser: attributes.merchandiser?.data
        ? {
            id: attributes.merchandiser.data.id,
            username: attributes.merchandiser.data.attributes.username,
            email: attributes.merchandiser.data.attributes.email,
            confirmed: attributes.merchandiser.data.attributes.confirmed,
            blocked: attributes.merchandiser.data.attributes.blocked,
            createdAt: attributes.merchandiser.data.attributes.createdAt,
            updatedAt: attributes.merchandiser.data.attributes.updatedAt,
          }
        : undefined,
      user_answers: attributes.user_answers?.data || [], // Include user_answers array for v4
      createdAt: attributes.createdAt,
      updatedAt: attributes.updatedAt,
    };
  }
};

// Plan API Service
export class PlanService {
  /**
   * Get plans with optional filtering and pagination
   */
  static async getPlans(params?: PlanQueryParams): Promise<{
    data: FlatPlan[];
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  }> {
    try {
      const queryParams: Record<string, any> = {};

      // Add pagination
      if (params?.pagination) {
        queryParams.pagination = params.pagination;
      }

      // Add sorting
      if (params?.sort) {
        queryParams.sort = params.sort;
      }

      // Add filters
      if (params?.filters) {
        queryParams.filters = params.filters;
      }

      // Add population - simplified populate for better Strapi v5 compatibility
      queryParams.populate = params?.populate || {
        store: true,
        audit_form: true, // Simplified populate for audit_form
        merchandiser: {
          fields: ["id", "username", "email", "confirmed", "blocked"],
        },
        user_answers: {
          fields: ["id", "documentId", "createdAt"], // Populate user_answers to see count
        },
      };

      const response = (await api.get(API_ENDPOINTS.PLANS, {
        params: queryParams,
      })) as PlansApiResponse;

      // response is already the Strapi response format due to response interceptor
      return {
        data: response.data.map(transformStrapiPlan),
        pagination: response.meta?.pagination,
      };
    } catch (error: any) {
      if (error instanceof ApiError) {
        throw error;
      }

      if (error?.response) {
        const status = error.response.status;
        const errorData = error.response.data;
        const message =
          errorData?.error?.message ||
          errorData?.message ||
          "Không thể tải danh sách kế hoạch";

        throw new ApiError(message, status, errorData);
      }

      throw new ApiError("Lỗi kết nối. Vui lòng thử lại.", 0);
    }
  }

  /**
   * Get plans for the current authenticated user (client-side filtering)
   */
  static async getUserPlans(
    userId: number,
    params?: Omit<PlanQueryParams, "filters"> & {
      filters?: Omit<PlanQueryParams["filters"], "merchandiser">;
    }
  ): Promise<{
    data: FlatPlan[];
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  }> {
    // Get all plans and filter on client side since merchandiser filtering is not supported
    const allPlansParams: PlanQueryParams = {
      ...params,
      filters: params?.filters, // Use other filters but exclude merchandiser
      populate: params?.populate || {
        store: true,
        audit_form: true, // Simplified populate for audit_form
        merchandiser: {
          fields: ["id", "username", "email", "confirmed", "blocked"],
        },
        user_answers: {
          fields: ["id", "documentId", "createdAt"], // Populate user_answers to see count
        },
      },
    };

    const allPlansResponse = await this.getPlans(allPlansParams);

    console.log("All plans response:", allPlansResponse);
    console.log("User ID for filtering:", userId);

    // Filter plans on client side by merchandiser ID
    const userPlans = allPlansResponse.data.filter((plan) => {
      console.log("Plan merchandiser:", plan.merchandiser);
      return plan.merchandiser?.id === userId;
    });

    console.log("Filtered user plans:", userPlans);
    console.log(
      `Filtered ${userPlans.length} plans out of ${allPlansResponse.data.length} total plans`
    );

    return {
      data: userPlans,
      pagination: {
        page: 1,
        pageSize: userPlans.length,
        pageCount: 1,
        total: userPlans.length,
      },
    };
  }

  /**
   * Get a single plan by ID or documentId
   */
  static async getPlan(id: number | string): Promise<FlatPlan> {
    try {
      // Thử tìm theo ID trước
      try {
        const response = (await api.get(
          buildEndpoint(API_ENDPOINTS.PLANS, id),
          {
            params: {
              populate: {
                store: true,
                audit_form: true, // Simplified populate for audit_form
                merchandiser: true,
                user_answers: {
                  fields: ["id", "documentId", "createdAt"], // Populate user_answers to see count
                },
              },
            },
          }
        )) as PlanApiResponse;

        return transformStrapiPlan(response.data);
      } catch (idError: any) {
        // Nếu không tìm được theo ID, thử tìm theo documentId
        console.log("Plan not found by ID, trying documentId:", id);

        const allPlansResponse = await this.getPlans({
          populate: {
            store: true,
            audit_form: true, // Simplified populate for audit_form
            merchandiser: {
              fields: ["id", "username", "email", "confirmed", "blocked"],
            },
            user_answers: {
              fields: ["id", "documentId", "createdAt"], // Populate user_answers to see count
            },
          },
        });

        // Tìm plan theo documentId hoặc id trong danh sách
        const plan = allPlansResponse.data.find(
          (p) =>
            p.documentId === id.toString() || p.id.toString() === id.toString()
        );

        if (!plan) {
          console.error(
            "Plan not found in list. Available plans:",
            allPlansResponse.data.map((p) => ({
              id: p.id,
              documentId: p.documentId,
            }))
          );
          throw new ApiError("Không tìm thấy kế hoạch với ID này", 404);
        }

        console.log("Found plan by searching in list:", plan);
        return plan;
      }
    } catch (error: any) {
      if (error instanceof ApiError) {
        throw error;
      }

      if (error?.response) {
        const status = error.response.status;
        const errorData = error.response.data;
        const message =
          errorData?.error?.message ||
          errorData?.message ||
          "Không thể tải thông tin kế hoạch";

        throw new ApiError(message, status, errorData);
      }

      throw new ApiError("Lỗi kết nối. Vui lòng thử lại.", 0);
    }
  }

  /**
   * Update plan status
   */
  static async updatePlanStatus(
    id: number | string,
    plan_status: "pending" | "in_progress" | "completed" | "expired"
  ): Promise<FlatPlan> {
    try {
      console.log(
        `Updating plan ${id} (type: ${typeof id}) status to ${plan_status}`
      );

      // In Strapi v5, we need to use documentId instead of id for API operations
      // First, get the plan to find its documentId
      let documentId: string;

      try {
        console.log("Step 1: Finding plan to get documentId");
        const existingPlan = await this.getPlan(id);
        console.log("Found plan:", existingPlan);

        // In Strapi v5, the plan should have a documentId field
        // If not available, we'll try using the id as documentId
        documentId = existingPlan.documentId || String(existingPlan.id);
        console.log("Using documentId for update:", documentId);
      } catch (getPlanError: any) {
        console.warn("Could not fetch plan to get documentId:", getPlanError);
        // Fallback: try using the provided id as documentId
        documentId = String(id);
        console.log("Fallback: using provided id as documentId:", documentId);
      }

      // Try different approaches with documentId
      const updateAttempts = [
        // Attempt 1: Use documentId (Strapi v5 way)
        async () => {
          console.log("Attempt 1: Using documentId (Strapi v5):", documentId);
          const endpoint = buildEndpoint(API_ENDPOINTS.PLANS, documentId);
          const payload = { data: { plan_status } };
          console.log("Endpoint:", endpoint, "Payload:", payload);
          return await api.put<PlanApiResponse>(endpoint, payload);
        },

        // Attempt 2: Use original id (Strapi v4 fallback)
        async () => {
          console.log("Attempt 2: Using original id (Strapi v4 fallback):", id);
          const endpoint = buildEndpoint(API_ENDPOINTS.PLANS, id);
          const payload = { data: { plan_status } };
          console.log("Endpoint:", endpoint, "Payload:", payload);
          return await api.put<PlanApiResponse>(endpoint, payload);
        },

        // Attempt 3: Try with string conversion of original id
        async () => {
          console.log("Attempt 3: Using string conversion of id");
          const stringId = String(id);
          const endpoint = buildEndpoint(API_ENDPOINTS.PLANS, stringId);
          const payload = { data: { plan_status } };
          return await api.put<PlanApiResponse>(endpoint, payload);
        },
      ];

      let lastError: any;

      for (let i = 0; i < updateAttempts.length; i++) {
        try {
          console.log(`Trying update attempt ${i + 1}`);
          const response = await updateAttempts[i]();
          console.log(
            "Success! Plan status update response:",
            JSON.stringify(response.data, null, 2)
          );
          return transformStrapiPlan(response.data);
        } catch (attemptError: any) {
          console.warn(
            `Attempt ${i + 1} failed:`,
            attemptError?.response?.data || attemptError.message
          );
          lastError = attemptError;

          // If it's not a 404, don't try other attempts
          if (attemptError?.response?.status !== 404) {
            throw attemptError;
          }
        }
      }

      // If all attempts failed, throw the last error
      throw lastError;
    } catch (error: any) {
      console.error("All plan status update attempts failed:", error);
      console.error("Final error response:", error?.response?.data);

      if (error instanceof ApiError) {
        throw error;
      }

      if (error?.response) {
        const status = error.response.status;
        const errorData = error.response.data;
        const message =
          errorData?.error?.message ||
          errorData?.message ||
          "Không thể cập nhật trạng thái kế hoạch";

        throw new ApiError(message, status, errorData);
      }

      throw new ApiError(
        "Không thể cập nhật trạng thái kế hoạch. Vui lòng thử lại.",
        500
      );
    }
  }

  /**
   * Update plan data
   */
  static async updatePlan(
    id: number | string,
    data: Partial<{
      assigned_date?: string;
      start_date?: string;
      end_date?: string;
      plan_status?: "pending" | "in_progress" | "completed" | "expired";
      visits_target?: number;
      plan_title?: string;
    }>
  ): Promise<FlatPlan> {
    try {
      const response = await api.put<PlanApiResponse>(
        buildEndpoint(API_ENDPOINTS.PLANS, id),
        {
          data,
        }
      );

      return transformStrapiPlan(response.data);
    } catch (error: any) {
      console.error("Failed to update plan:", error);

      if (error instanceof ApiError) {
        throw error;
      }

      if (error?.response) {
        const status = error.response.status;
        const errorData = error.response.data;
        const message =
          errorData?.error?.message ||
          errorData?.message ||
          "Không thể cập nhật kế hoạch";

        throw new ApiError(message, status, errorData);
      }

      throw new ApiError("Không thể cập nhật kế hoạch. Vui lòng thử lại.", 500);
    }
  }
}

// Export the service as default and named export
export default PlanService;
