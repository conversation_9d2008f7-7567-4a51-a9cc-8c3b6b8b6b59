import { api } from './client';

export interface UploadedFile {
  id: number;
  name: string;
  alternativeText?: string;
  caption?: string;
  width?: number;
  height?: number;
  formats?: {
    thumbnail?: { url: string };
    small?: { url: string };
    medium?: { url: string };
    large?: { url: string };
  };
  hash: string;
  ext: string;
  mime: string;
  size: number;
  url: string;
  previewUrl?: string;
  provider: string;
  provider_metadata?: any;
  createdAt: string;
  updatedAt: string;
}

export interface UploadResponse {
  data: UploadedFile[];
}

export const uploadService = {
  /**
   * Upload files to Strapi media library
   */
  async uploadFiles(files: File[]): Promise<UploadResponse> {
    const formData = new FormData();
    
    files.forEach((file) => {
      formData.append('files', file);
    });

    const response = await api.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response;
  },

  /**
   * Upload a single file to Strapi media library
   */
  async uploadFile(file: File): Promise<UploadedFile> {
    const response = await this.uploadFiles([file]);
    return response.data[0];
  },
}; 