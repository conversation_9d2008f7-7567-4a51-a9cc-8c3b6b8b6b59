/**
 * Test utility for image upload functionality
 * This file helps debug and test the image upload integration
 */

import { uploadFile, UploadedFile } from "../api/fileUpload";
import { submitUserAnswers } from "../api/userAnswers";
import { CompressedImage } from "./imageUtils";

// Mock compressed image for testing
const createMockCompressedImage = (name: string): CompressedImage => ({
  uri: `file:///mock/path/${name}`,
  name: name,
  type: "image/webp",
  size: 1024 * 50, // 50KB
  width: 800,
  height: 600,
  quality: 0.8,
  format: "webp",
});

/**
 * Test file upload to Strapi
 */
export const testFileUpload = async (): Promise<void> => {
  try {
    console.log("🧪 Testing file upload to Strapi v5...");

    const mockImage = createMockCompressedImage("test-image.webp");

    console.log("Mock image created:", mockImage);

    // This will fail in actual upload due to mock URI, but will test the API structure
    const uploadedFile = await uploadFile(mockImage, (progress) => {
      console.log("Upload progress:", progress);
    });

    console.log("✅ File upload test successful:", uploadedFile);
  } catch (error) {
    console.log("❌ File upload test failed (expected for mock data):", error);
  }
};

/**
 * Test user answer submission with images
 */
export const testUserAnswerSubmission = async (
  planId: string,
  userId: string,
  questionId: string
): Promise<void> => {
  try {
    console.log("🧪 Testing user answer submission with images...");

    // Mock uploaded files (these would come from actual upload)
    const mockUploadedFiles: UploadedFile[] = [
      {
        id: 123,
        documentId: "mock-doc-id",
        name: "test-image.webp",
        url: "/uploads/test-image.webp",
        hash: "mock-hash",
        ext: ".webp",
        mime: "image/webp",
        size: 51200,
        provider: "local",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];

    const submissionData = {
      planId: planId,
      storeId: "mock-store-id",
      userId: userId,
      answers: [
        {
          question: questionId,
          answer_value: "Test answer with image",
          question_type: "text",
          photo_value: mockUploadedFiles,
          plan: planId,
          store: "mock-store-id",
          user: userId,
        },
      ],
    };

    console.log("Submission data prepared:", submissionData);

    const result = await submitUserAnswers(submissionData);

    console.log("✅ User answer submission test successful:", result);
  } catch (error) {
    console.log("❌ User answer submission test failed:", error);
  }
};

/**
 * Test the complete image upload flow
 */
export const testCompleteImageFlow = async (
  planId: string,
  userId: string,
  questionId: string
): Promise<void> => {
  console.log("🧪 Testing complete image upload flow...");

  // Step 1: Test file upload
  await testFileUpload();

  // Step 2: Test user answer submission
  await testUserAnswerSubmission(planId, userId, questionId);

  console.log("🎉 Complete image flow test completed");
};

/**
 * Debug image upload issues
 */
export const debugImageUpload = (): void => {
  console.log("🔍 Image Upload Debug Information:");
  console.log(
    "- API Base URL:",
    process.env.EXPO_PUBLIC_API_URL || "https://cpsstrapi.appmkt.vn/api"
  );
  console.log("- Upload endpoint: /upload");
  console.log("- User answers endpoint: /user-answers");
  console.log("- Expected Strapi version: v5");
  console.log("- Image format: WebP");
  console.log(
    "- Multimedia field connection format: { connect: [{ id: fileId }] }"
  );
};
