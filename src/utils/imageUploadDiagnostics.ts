import { ImageStorageManager } from "./imageStorage";
import { submitUserAnswers, UserAnswerService } from "../api/userAnswers";
import { uploadFile, UploadedFile } from "../api/fileUpload";
import { CompressedImage } from "./imageUtils";

/**
 * Comprehensive diagnostics for image upload and linking process
 */
export class ImageUploadDiagnostics {
  /**
   * Test the complete image upload and linking flow
   */
  static async runCompleteDiagnostics(
    planId: string,
    userId: string,
    questionId: string
  ): Promise<void> {
    console.log("🔍 STARTING COMPREHENSIVE IMAGE UPLOAD DIAGNOSTICS");
    console.log("=" .repeat(60));

    try {
      // Step 1: Check stored images
      await this.checkStoredImages(questionId);

      // Step 2: Test image upload
      const mockImage = this.createMockImage(questionId);
      const uploadedFile = await this.testImageUpload(mockImage);

      // Step 3: Test user answer creation with image
      await this.testUserAnswerWithImage(planId, userId, questionId, uploadedFile);

      // Step 4: Verify database storage
      await this.verifyDatabaseStorage(planId);

      console.log("✅ ALL DIAGNOSTICS COMPLETED SUCCESSFULLY");
    } catch (error) {
      console.error("❌ DIAGNOSTICS FAILED:", error);
      throw error;
    }
  }

  /**
   * Check stored images for a question
   */
  private static async checkStoredImages(questionId: string): Promise<void> {
    console.log("\n📋 STEP 1: Checking stored images");
    console.log("-".repeat(40));

    try {
      const storedImages = await ImageStorageManager.getQuestionImages(questionId);
      console.log(`📸 Found ${storedImages.length} stored images for question ${questionId}`);

      storedImages.forEach((img, index) => {
        console.log(`📸 Image ${index + 1}:`, {
          id: img.id,
          name: img.name,
          isUploaded: img.isUploaded,
          uploadedFileId: img.uploadedFileId,
          size: img.size,
          timestamp: new Date(img.timestamp).toISOString(),
        });
      });

      const pendingImages = await ImageStorageManager.getPendingImages();
      console.log(`📤 Found ${pendingImages.length} questions with pending images`);

      pendingImages.forEach((questionImages, index) => {
        console.log(`📤 Question ${index + 1}:`, {
          questionId: questionImages.questionId,
          imagesCount: questionImages.images.length,
          pendingCount: questionImages.images.filter(img => !img.isUploaded).length,
        });
      });

    } catch (error) {
      console.error("❌ Error checking stored images:", error);
      throw error;
    }
  }

  /**
   * Test image upload process
   */
  private static async testImageUpload(mockImage: CompressedImage): Promise<UploadedFile> {
    console.log("\n📤 STEP 2: Testing image upload");
    console.log("-".repeat(40));

    try {
      console.log("📤 Uploading mock image:", {
        name: mockImage.name,
        size: mockImage.size,
        type: mockImage.type,
      });

      const uploadedFile = await uploadFile(mockImage, (progress) => {
        console.log(`📤 Upload progress: ${progress.percentage}%`);
      });

      console.log("✅ Image uploaded successfully:", {
        id: uploadedFile.id,
        name: uploadedFile.name,
        url: uploadedFile.url,
        size: uploadedFile.size,
      });

      return uploadedFile;
    } catch (error) {
      console.error("❌ Image upload failed:", error);
      throw error;
    }
  }

  /**
   * Test user answer creation with image
   */
  private static async testUserAnswerWithImage(
    planId: string,
    userId: string,
    questionId: string,
    uploadedFile: UploadedFile
  ): Promise<void> {
    console.log("\n💾 STEP 3: Testing user answer creation with image");
    console.log("-".repeat(40));

    try {
      const submissionData = {
        planId,
        storeId: "diagnostic-store",
        userId,
        answers: [
          {
            question: questionId,
            answer_value: "Diagnostic test answer",
            question_type: "text",
            photo_value: [uploadedFile], // Include uploaded image
            plan: planId,
            store: "diagnostic-store",
            user: userId,
          },
        ],
      };

      console.log("💾 Submitting user answer with image:", {
        questionId,
        imageId: uploadedFile.id,
        imageName: uploadedFile.name,
      });

      const result = await submitUserAnswers(submissionData);

      console.log("✅ User answer submitted successfully:", result);
    } catch (error) {
      console.error("❌ User answer submission failed:", error);
      throw error;
    }
  }

  /**
   * Verify database storage
   */
  private static async verifyDatabaseStorage(planId: string): Promise<void> {
    console.log("\n🔍 STEP 4: Verifying database storage");
    console.log("-".repeat(40));

    try {
      // Note: This would require implementing a getUserAnswers method
      console.log("🔍 Checking database for stored user answers...");
      console.log("📋 Plan ID:", planId);
      
      // For now, just log that we would check the database
      console.log("✅ Database verification would check:");
      console.log("  - User answer record exists");
      console.log("  - photo_value field contains image IDs");
      console.log("  - Image files are accessible via URLs");
      
    } catch (error) {
      console.error("❌ Database verification failed:", error);
      throw error;
    }
  }

  /**
   * Create a mock compressed image for testing
   */
  private static createMockImage(questionId: string): CompressedImage {
    return {
      uri: `data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA`, // Minimal WebP
      name: `diagnostic-test-${questionId}-${Date.now()}.webp`,
      type: "image/webp",
      size: 34, // Size of the minimal WebP above
    };
  }

  /**
   * Quick diagnostic check for common issues
   */
  static async quickDiagnostic(): Promise<void> {
    console.log("🔍 QUICK DIAGNOSTIC CHECK");
    console.log("=" .repeat(40));

    // Check environment
    console.log("🌐 Environment Check:");
    console.log("  - API URL:", process.env.EXPO_PUBLIC_API_URL || "Not set");
    console.log("  - Upload endpoint: /upload");
    console.log("  - User answers endpoint: /user-answers");

    // Check storage
    try {
      const allQuestionImages = await ImageStorageManager.getAllQuestionImages();
      console.log(`📸 Total questions with stored images: ${allQuestionImages.length}`);
      
      const totalImages = allQuestionImages.reduce((sum, q) => sum + q.images.length, 0);
      const uploadedImages = allQuestionImages.reduce(
        (sum, q) => sum + q.images.filter(img => img.isUploaded).length, 
        0
      );
      
      console.log(`📸 Total stored images: ${totalImages}`);
      console.log(`📤 Uploaded images: ${uploadedImages}`);
      console.log(`⏳ Pending images: ${totalImages - uploadedImages}`);
      
    } catch (error) {
      console.error("❌ Storage check failed:", error);
    }

    console.log("✅ Quick diagnostic completed");
  }

  /**
   * Clear all diagnostic data
   */
  static async clearDiagnosticData(): Promise<void> {
    console.log("🧹 Clearing diagnostic data...");
    
    try {
      // Clear all stored images
      const allQuestionImages = await ImageStorageManager.getAllQuestionImages();
      
      for (const questionImages of allQuestionImages) {
        if (questionImages.questionId.includes("diagnostic")) {
          await ImageStorageManager.clearQuestionImages(questionImages.questionId);
          console.log(`🧹 Cleared images for diagnostic question: ${questionImages.questionId}`);
        }
      }
      
      console.log("✅ Diagnostic data cleared");
    } catch (error) {
      console.error("❌ Failed to clear diagnostic data:", error);
    }
  }
}

/**
 * Export convenience functions
 */
export const runImageUploadDiagnostics = ImageUploadDiagnostics.runCompleteDiagnostics;
export const quickImageDiagnostic = ImageUploadDiagnostics.quickDiagnostic;
export const clearImageDiagnosticData = ImageUploadDiagnostics.clearDiagnosticData;
