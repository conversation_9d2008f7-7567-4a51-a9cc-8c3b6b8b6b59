import AsyncStorage from "@react-native-async-storage/async-storage";

// Storage keys
const STORAGE_KEYS = {
  QUESTION_RECORDINGS: "question_recordings",
} as const;

// Types for audio storage
export interface StoredRecording {
  id: string;
  questionId: string;
  uri: string;
  name: string;
  duration: number; // in milliseconds
  size: number; // in bytes
  timestamp: number;
  isUploaded: boolean;
  uploadedFileId?: number;
}

export interface QuestionRecordings {
  questionId: string;
  recordings: StoredRecording[];
}

/**
 * Audio Storage Manager for temporary storage before upload
 */
export class AudioStorageManager {
  /**
   * Store recording for a specific question
   */
  static async storeQuestionRecording(
    questionId: string,
    recording: Omit<StoredRecording, "id" | "questionId" | "timestamp" | "isUploaded">
  ): Promise<StoredRecording> {
    try {
      console.log("Storing recording for question:", questionId);

      const storedRecording: StoredRecording = {
        ...recording,
        id: `${questionId}_${Date.now()}`,
        questionId,
        timestamp: Date.now(),
        isUploaded: false,
      };

      // Get existing recordings
      const existingRecordings = await this.getQuestionRecordings(questionId);

      // Replace existing recording (only one recording per question)
      const allRecordings = [storedRecording];

      // Store updated recordings
      await this.saveQuestionRecordings(questionId, allRecordings);

      console.log("Recording stored successfully for question:", questionId);
      return storedRecording;
    } catch (error) {
      console.error("Error storing question recording:", error);
      throw new Error("Failed to store recording");
    }
  }

  /**
   * Get stored recordings for a specific question
   */
  static async getQuestionRecordings(questionId: string): Promise<StoredRecording[]> {
    try {
      const allQuestionRecordings = await this.getAllQuestionRecordings();
      const questionRecordings = allQuestionRecordings.find(
        (q) => q.questionId === questionId
      );
      return questionRecordings?.recordings || [];
    } catch (error) {
      console.error("Error getting question recordings:", error);
      return [];
    }
  }

  /**
   * Get all stored question recordings
   */
  static async getAllQuestionRecordings(): Promise<QuestionRecordings[]> {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEYS.QUESTION_RECORDINGS);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error("Error getting all question recordings:", error);
      return [];
    }
  }

  /**
   * Save question recordings to storage
   */
  private static async saveQuestionRecordings(
    questionId: string,
    recordings: StoredRecording[]
  ): Promise<void> {
    try {
      const allQuestionRecordings = await this.getAllQuestionRecordings();

      // Update or add question recordings
      const existingIndex = allQuestionRecordings.findIndex(
        (q) => q.questionId === questionId
      );

      if (existingIndex >= 0) {
        allQuestionRecordings[existingIndex] = { questionId, recordings };
      } else {
        allQuestionRecordings.push({ questionId, recordings });
      }

      await AsyncStorage.setItem(
        STORAGE_KEYS.QUESTION_RECORDINGS,
        JSON.stringify(allQuestionRecordings)
      );
    } catch (error) {
      console.error("Error saving question recordings:", error);
      throw error;
    }
  }

  /**
   * Remove recording from question
   */
  static async removeQuestionRecording(
    questionId: string,
    recordingId: string
  ): Promise<void> {
    try {
      console.log("Removing recording:", recordingId, "from question:", questionId);

      const recordings = await this.getQuestionRecordings(questionId);
      const filteredRecordings = recordings.filter((rec) => rec.id !== recordingId);

      await this.saveQuestionRecordings(questionId, filteredRecordings);

      console.log("Recording removed successfully");
    } catch (error) {
      console.error("Error removing question recording:", error);
      throw new Error("Failed to remove recording");
    }
  }

  /**
   * Clear all recordings for a question
   */
  static async clearQuestionRecordings(questionId: string): Promise<void> {
    try {
      await this.saveQuestionRecordings(questionId, []);
      console.log("All recordings cleared for question:", questionId);
    } catch (error) {
      console.error("Error clearing question recordings:", error);
      throw error;
    }
  }

  /**
   * Mark recording as uploaded
   */
  static async markRecordingAsUploaded(
    questionId: string,
    recordingId: string,
    uploadedFileId: number
  ): Promise<void> {
    try {
      console.log("Marking recording as uploaded:", recordingId);

      const recordings = await this.getQuestionRecordings(questionId);

      const updatedRecordings = recordings.map((rec) => {
        if (rec.id === recordingId) {
          return {
            ...rec,
            isUploaded: true,
            uploadedFileId,
          };
        }
        return rec;
      });

      await this.saveQuestionRecordings(questionId, updatedRecordings);

      console.log("Recording marked as uploaded successfully");
    } catch (error) {
      console.error("Error marking recording as uploaded:", error);
      throw error;
    }
  }

  /**
   * Get all pending (not uploaded) recordings
   */
  static async getPendingRecordings(): Promise<
    { questionId: string; recordings: StoredRecording[] }[]
  > {
    try {
      const allQuestionRecordings = await this.getAllQuestionRecordings();

      return allQuestionRecordings
        .map((questionRecordings) => ({
          questionId: questionRecordings.questionId,
          recordings: questionRecordings.recordings.filter((rec) => !rec.isUploaded),
        }))
        .filter((item) => item.recordings.length > 0);
    } catch (error) {
      console.error("Error getting pending recordings:", error);
      return [];
    }
  }

  /**
   * Clear all recordings from cache
   */
  static async clearAllRecordings(): Promise<void> {
    try {
      console.log("🧹 Clearing all recordings from cache...");

      await AsyncStorage.removeItem(STORAGE_KEYS.QUESTION_RECORDINGS);

      console.log("✅ All recordings cleared successfully from cache");
    } catch (error) {
      console.error("❌ Error clearing all recordings:", error);
      throw new Error("Failed to clear recordings");
    }
  }
}
