import AsyncStorage from "@react-native-async-storage/async-storage";
import { CompressedImage } from "./imageUtils";

// Storage keys
const STORAGE_KEYS = {
  TEMP_IMAGES: "temp_images",
  QUESTION_IMAGES: "question_images",
} as const;

// Types for image storage
export interface StoredImage extends CompressedImage {
  id: string;
  questionId: string;
  planId?: string; // Add planId for form isolation
  timestamp: number;
  isUploaded: boolean;
  uploadedFileId?: number;
}

export interface QuestionImages {
  questionId: string;
  planId?: string; // Add planId for form isolation
  images: StoredImage[];
}

/**
 * Image Storage Manager for temporary storage before upload
 */
export class ImageStorageManager {
  /**
   * Store images for a specific question
   */
  static async storeQuestionImages(
    questionId: string,
    images: CompressedImage[],
    planId?: string
  ): Promise<StoredImage[]> {
    try {
      console.log(
        "Storing images for question:",
        questionId,
        "planId:",
        planId,
        "Count:",
        images.length
      );

      // Convert to StoredImage format
      const storedImages: StoredImage[] = images.map((image, index) => ({
        ...image,
        id: `${planId || "no-plan"}_${questionId}_${Date.now()}_${index}`,
        questionId,
        planId,
        timestamp: Date.now(),
        isUploaded: false,
      }));

      // Get existing question images
      const existingImages = await this.getQuestionImages(questionId, planId);

      // Merge with new images
      const allImages = [...existingImages, ...storedImages];

      // Store updated images
      await this.saveQuestionImages(questionId, allImages, planId);

      console.log(
        "Images stored successfully for question:",
        questionId,
        "planId:",
        planId
      );
      return storedImages;
    } catch (error) {
      console.error("Error storing question images:", error);
      throw new Error("Failed to store images");
    }
  }

  /**
   * Get stored images for a specific question
   */
  static async getQuestionImages(
    questionId: string,
    planId?: string
  ): Promise<StoredImage[]> {
    try {
      const allQuestionImages = await this.getAllQuestionImages();
      const questionImages = allQuestionImages.find(
        (q) => q.questionId === questionId && q.planId === planId
      );
      return questionImages?.images || [];
    } catch (error) {
      console.error("Error getting question images:", error);
      return [];
    }
  }

  /**
   * Get all stored question images
   */
  static async getAllQuestionImages(): Promise<QuestionImages[]> {
    try {
      const stored = await AsyncStorage.getItem(STORAGE_KEYS.QUESTION_IMAGES);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error("Error getting all question images:", error);
      return [];
    }
  }

  /**
   * Save question images to storage
   */
  private static async saveQuestionImages(
    questionId: string,
    images: StoredImage[],
    planId?: string
  ): Promise<void> {
    try {
      const allQuestionImages = await this.getAllQuestionImages();

      // Update or add question images
      const existingIndex = allQuestionImages.findIndex(
        (q) => q.questionId === questionId && q.planId === planId
      );

      if (existingIndex >= 0) {
        allQuestionImages[existingIndex] = { questionId, planId, images };
      } else {
        allQuestionImages.push({ questionId, planId, images });
      }

      await AsyncStorage.setItem(
        STORAGE_KEYS.QUESTION_IMAGES,
        JSON.stringify(allQuestionImages)
      );
    } catch (error) {
      console.error("Error saving question images:", error);
      throw error;
    }
  }

  /**
   * Remove specific image from question
   */
  static async removeQuestionImage(
    questionId: string,
    imageId: string,
    planId?: string
  ): Promise<void> {
    try {
      console.log(
        "Removing image:",
        imageId,
        "from question:",
        questionId,
        "planId:",
        planId
      );

      const images = await this.getQuestionImages(questionId, planId);
      const filteredImages = images.filter((img) => img.id !== imageId);

      await this.saveQuestionImages(questionId, filteredImages, planId);

      console.log("Image removed successfully");
    } catch (error) {
      console.error("Error removing question image:", error);
      throw new Error("Failed to remove image");
    }
  }

  /**
   * Clear all images for a specific question
   */
  static async clearQuestionImages(
    questionId: string,
    planId?: string
  ): Promise<void> {
    try {
      console.log(
        "Clearing all images for question:",
        questionId,
        "planId:",
        planId
      );

      await this.saveQuestionImages(questionId, [], planId);

      console.log("Question images cleared successfully");
    } catch (error) {
      console.error("Error clearing question images:", error);
      throw new Error("Failed to clear images");
    }
  }

  /**
   * Mark images as uploaded
   */
  static async markImagesAsUploaded(
    questionId: string,
    imageIds: string[],
    uploadedFileIds: number[]
  ): Promise<void> {
    try {
      console.log("Marking images as uploaded:", imageIds);

      const images = await this.getQuestionImages(questionId);

      const updatedImages = images.map((img) => {
        const index = imageIds.indexOf(img.id);
        if (index >= 0) {
          return {
            ...img,
            isUploaded: true,
            uploadedFileId: uploadedFileIds[index],
          };
        }
        return img;
      });

      await this.saveQuestionImages(questionId, updatedImages);

      console.log("Images marked as uploaded successfully");
    } catch (error) {
      console.error("Error marking images as uploaded:", error);
      throw error;
    }
  }

  /**
   * Get all stored images (flattened list with questionId)
   */
  static async getAllStoredImages(): Promise<
    (StoredImage & { questionId: string })[]
  > {
    try {
      const allQuestionImages = await this.getAllQuestionImages();
      const flattenedImages: (StoredImage & { questionId: string })[] = [];

      allQuestionImages.forEach((questionImages) => {
        questionImages.images.forEach((image) => {
          flattenedImages.push({
            ...image,
            questionId: questionImages.questionId,
          });
        });
      });

      console.log(
        "getAllStoredImages: Found",
        flattenedImages.length,
        "total images"
      );
      return flattenedImages;
    } catch (error) {
      console.error("Error getting all stored images:", error);
      return [];
    }
  }

  /**
   * Get all pending (not uploaded) images
   */
  static async getPendingImages(): Promise<
    { questionId: string; images: StoredImage[] }[]
  > {
    try {
      const allQuestionImages = await this.getAllQuestionImages();

      return allQuestionImages
        .map((questionImages) => ({
          questionId: questionImages.questionId,
          images: questionImages.images.filter((img) => !img.isUploaded),
        }))
        .filter((item) => item.images.length > 0);
    } catch (error) {
      console.error("Error getting pending images:", error);
      return [];
    }
  }

  /**
   * Get total count of stored images
   */
  static async getTotalImageCount(): Promise<number> {
    try {
      const allQuestionImages = await this.getAllQuestionImages();
      return allQuestionImages.reduce((total, questionImages) => {
        return total + questionImages.images.length;
      }, 0);
    } catch (error) {
      console.error("Error getting total image count:", error);
      return 0;
    }
  }

  /**
   * Get storage size estimate
   */
  static async getStorageSize(): Promise<number> {
    try {
      const allQuestionImages = await this.getAllQuestionImages();
      return allQuestionImages.reduce((totalSize, questionImages) => {
        const questionSize = questionImages.images.reduce((size, img) => {
          return size + (img.size || 0);
        }, 0);
        return totalSize + questionSize;
      }, 0);
    } catch (error) {
      console.error("Error getting storage size:", error);
      return 0;
    }
  }

  /**
   * Clear all images for a specific plan (for form isolation)
   */
  static async clearPlanImages(planId: string): Promise<void> {
    try {
      console.log("🧹 Clearing all images for plan:", planId);

      const allQuestionImages = await this.getAllQuestionImages();
      const filteredImages = allQuestionImages.filter(
        (q) => q.planId !== planId
      );

      await AsyncStorage.setItem(
        STORAGE_KEYS.QUESTION_IMAGES,
        JSON.stringify(filteredImages)
      );

      console.log("✅ Plan images cleared successfully for plan:", planId);
    } catch (error) {
      console.error("❌ Error clearing plan images:", error);
      throw new Error("Failed to clear plan images");
    }
  }

  /**
   * Clear all stored images (cleanup)
   */
  static async clearAllImages(): Promise<void> {
    try {
      console.log("🧹 Clearing all stored images from cache...");

      // Get current storage data for logging
      const currentImages = await this.getAllQuestionImages();
      console.log(
        `🧹 Found ${currentImages.length} question groups with images to clear`
      );

      currentImages.forEach((questionImages, index) => {
        console.log(`🧹 Question group ${index + 1}:`, {
          questionId: questionImages.questionId,
          imageCount: questionImages.images.length,
        });
      });

      await AsyncStorage.removeItem(STORAGE_KEYS.QUESTION_IMAGES);
      await AsyncStorage.removeItem(STORAGE_KEYS.TEMP_IMAGES);

      console.log("✅ All images cleared successfully from cache");
    } catch (error) {
      console.error("❌ Error clearing all images:", error);
      throw new Error("Failed to clear images");
    }
  }

  /**
   * Export stored images data (for debugging)
   */
  static async exportStorageData(): Promise<string> {
    try {
      const allQuestionImages = await this.getAllQuestionImages();
      return JSON.stringify(allQuestionImages, null, 2);
    } catch (error) {
      console.error("Error exporting storage data:", error);
      return "[]";
    }
  }
}
