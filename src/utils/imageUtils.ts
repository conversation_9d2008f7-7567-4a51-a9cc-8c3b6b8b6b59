import * as ImageManipulator from "expo-image-manipulator";
import * as FileSystem from "expo-file-system";
import { Alert, Platform } from "react-native";

// Image compression and processing utilities
export interface CompressedImage {
  uri: string;
  name: string;
  size: number;
  type: string;
  width?: number;
  height?: number;
}

export interface ImageCompressionOptions {
  quality?: number; // 0-1, default 0.8
  maxWidth?: number; // default 1920
  maxHeight?: number; // default 1080
  format?: "jpeg" | "png" | "webp"; // default 'webp'
}

// Web-specific image compression utilities
const compressImageWeb = async (
  imageUri: string,
  options: ImageCompressionOptions = {}
): Promise<CompressedImage> => {
  const {
    quality = 0.8,
    maxWidth = 1920,
    maxHeight = 1080,
    format = "webp",
  } = options;

  console.log("🌐 Web: Compressing image:", imageUri, "with options:", options);

  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = "anonymous";

    img.onload = () => {
      try {
        // Create canvas for image processing
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");

        if (!ctx) {
          throw new Error("Could not get canvas context");
        }

        // Calculate new dimensions maintaining aspect ratio
        let { width, height } = img;
        const aspectRatio = width / height;

        if (width > maxWidth) {
          width = maxWidth;
          height = width / aspectRatio;
        }

        if (height > maxHeight) {
          height = maxHeight;
          width = height * aspectRatio;
        }

        // Set canvas dimensions
        canvas.width = width;
        canvas.height = height;

        // Draw and compress image
        ctx.drawImage(img, 0, 0, width, height);

        // Convert to desired format
        const mimeType =
          format === "webp"
            ? "image/webp"
            : format === "png"
            ? "image/png"
            : "image/jpeg";

        const compressedDataUrl = canvas.toDataURL(mimeType, quality);

        // Calculate approximate file size (base64 is ~33% larger than binary)
        const base64Length = compressedDataUrl.split(",")[1].length;
        const approximateSize = Math.round(base64Length * 0.75);

        // Generate filename
        const timestamp = Date.now();
        const extension =
          format === "webp" ? "webp" : format === "png" ? "png" : "jpg";
        const fileName = `compressed_image_${timestamp}.${extension}`;

        const compressedImage: CompressedImage = {
          uri: compressedDataUrl,
          name: fileName,
          size: approximateSize,
          type: mimeType,
          width: Math.round(width),
          height: Math.round(height),
        };

        console.log("🌐 Web: Image compressed successfully:", {
          originalSize: "Unknown (web blob)",
          compressedSize: compressedImage.size,
          dimensions: `${width}x${height}`,
        });

        resolve(compressedImage);
      } catch (error) {
        console.error("🌐 Web: Error in canvas processing:", error);
        reject(error);
      }
    };

    img.onerror = (error) => {
      console.error("🌐 Web: Error loading image:", error);
      reject(new Error("Failed to load image for compression"));
    };

    img.src = imageUri;
  });
};

const getImageInfoWeb = async (imageUri: string) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = "anonymous";

    img.onload = () => {
      resolve({
        uri: imageUri,
        size: 0, // Size not easily available for blob URLs
        width: img.naturalWidth,
        height: img.naturalHeight,
        exists: true,
      });
    };

    img.onerror = (error) => {
      console.error("🌐 Web: Error loading image for info:", error);
      reject(new Error("Failed to load image"));
    };

    img.src = imageUri;
  });
};

/**
 * Compress image to WebP format with specified quality and dimensions
 */
export const compressImage = async (
  imageUri: string,
  options: ImageCompressionOptions = {}
): Promise<CompressedImage> => {
  try {
    console.log("📱 Platform:", Platform.OS, "- Compressing image:", imageUri);

    // Use web-specific compression on web platform
    if (Platform.OS === "web") {
      return await compressImageWeb(imageUri, options);
    }

    // Mobile platform - use expo modules
    const {
      quality = 0.8,
      maxWidth = 1920,
      maxHeight = 1080,
      format = "webp",
    } = options;

    console.log("📱 Mobile: Compressing image with expo modules");

    // Get image info first
    const imageInfo = await FileSystem.getInfoAsync(imageUri);
    if (!imageInfo.exists) {
      throw new Error("Image file does not exist");
    }

    // Get original image dimensions first
    const originalImage = await ImageManipulator.manipulateAsync(imageUri, [], {
      format: ImageManipulator.SaveFormat.JPEG,
    });

    // Calculate new dimensions maintaining aspect ratio
    let newWidth = originalImage.width;
    let newHeight = originalImage.height;
    const aspectRatio = newWidth / newHeight;

    // Only resize if image is larger than max dimensions
    if (newWidth > maxWidth || newHeight > maxHeight) {
      if (newWidth > maxWidth) {
        newWidth = maxWidth;
        newHeight = newWidth / aspectRatio;
      }

      if (newHeight > maxHeight) {
        newHeight = maxHeight;
        newWidth = newHeight * aspectRatio;
      }
    }

    // Manipulate image with compression, only resize if needed
    const manipulateActions = [];
    if (newWidth !== originalImage.width || newHeight !== originalImage.height) {
      manipulateActions.push({
        resize: {
          width: Math.round(newWidth),
          height: Math.round(newHeight),
        },
      });
    }

    const manipulatedImage = await ImageManipulator.manipulateAsync(
      imageUri,
      [
        {
          resize: {
            width: maxWidth,
            height: maxHeight,
          },
        },
      ],
      {
        compress: quality,
        format:
          format === "webp"
            ? ImageManipulator.SaveFormat.WEBP
            : format === "png"
            ? ImageManipulator.SaveFormat.PNG
            : ImageManipulator.SaveFormat.JPEG,
        base64: false,
      }
    );

    // Get compressed file info
    const compressedInfo = await FileSystem.getInfoAsync(manipulatedImage.uri);

    // Generate filename with proper extension
    const timestamp = Date.now();
    const extension =
      format === "webp" ? "webp" : format === "png" ? "png" : "jpg";
    const fileName = `compressed_image_${timestamp}.${extension}`;

    const compressedImage: CompressedImage = {
      uri: manipulatedImage.uri,
      name: fileName,
      size: compressedInfo.size || 0,
      type: `image/${format}`,
      width: manipulatedImage.width,
      height: manipulatedImage.height,
    };

    console.log("📱 Mobile: Image compressed successfully:", {
      originalSize: imageInfo.size,
      compressedSize: compressedImage.size,
      compressionRatio:
        (
          ((imageInfo.size - compressedImage.size) / imageInfo.size) *
          100
        ).toFixed(1) + "%",
    });

    return compressedImage;
  } catch (error) {
    console.error("Error compressing image:", error);
    throw new Error("Failed to compress image");
  }
};

/**
 * Compress multiple images in batch
 */
export const compressImages = async (
  imageUris: string[],
  options: ImageCompressionOptions = {}
): Promise<CompressedImage[]> => {
  try {
    console.log("Compressing batch of images:", imageUris.length);

    const compressedImages: CompressedImage[] = [];

    for (let i = 0; i < imageUris.length; i++) {
      const uri = imageUris[i];
      try {
        const compressed = await compressImage(uri, options);
        compressedImages.push(compressed);
        console.log(`Compressed image ${i + 1}/${imageUris.length}`);
      } catch (error) {
        console.error(`Failed to compress image ${i + 1}:`, error);
        // Continue with other images, don't fail the entire batch
      }
    }

    return compressedImages;
  } catch (error) {
    console.error("Error in batch compression:", error);
    throw new Error("Failed to compress images");
  }
};

/**
 * Get image file info including dimensions
 * Platform-aware: uses web APIs on web, expo modules on mobile
 */
export const getImageInfo = async (imageUri: string) => {
  try {
    console.log("📱 Platform:", Platform.OS, "- Getting image info:", imageUri);

    // Use web-specific info gathering on web platform
    if (Platform.OS === "web") {
      return await getImageInfoWeb(imageUri);
    }

    // Mobile platform - use expo modules
    const fileInfo = await FileSystem.getInfoAsync(imageUri);

    if (!fileInfo.exists) {
      throw new Error("Image file does not exist");
    }

    // Get image dimensions using ImageManipulator
    const imageInfo = await ImageManipulator.manipulateAsync(imageUri, [], {
      format: ImageManipulator.SaveFormat.JPEG,
    });

    return {
      uri: imageUri,
      size: fileInfo.size || 0,
      width: imageInfo.width,
      height: imageInfo.height,
      exists: fileInfo.exists,
    };
  } catch (error) {
    console.error("Error getting image info:", error);
    throw error;
  }
};

/**
 * Validate image file size and type
 */
export const validateImage = (
  image: CompressedImage,
  maxSizeMB: number = 10
): boolean => {
  const maxSizeBytes = maxSizeMB * 1024 * 1024;

  if (image.size > maxSizeBytes) {
    Alert.alert(
      "File quá lớn",
      `Kích thước file không được vượt quá ${maxSizeMB}MB. File hiện tại: ${(
        image.size /
        1024 /
        1024
      ).toFixed(1)}MB`
    );
    return false;
  }

  const allowedTypes = ["image/jpeg", "image/png", "image/webp"];
  if (!allowedTypes.includes(image.type)) {
    Alert.alert(
      "Định dạng không hỗ trợ",
      "Chỉ hỗ trợ các định dạng: JPEG, PNG, WebP"
    );
    return false;
  }

  return true;
};

/**
 * Clean up temporary image files
 * Platform-aware: revokes blob URLs on web, deletes files on mobile
 */
export const cleanupTempImages = async (imageUris: string[]): Promise<void> => {
  try {
    console.log(
      "📱 Platform:",
      Platform.OS,
      "- Cleaning up temp images:",
      imageUris.length
    );

    if (Platform.OS === "web") {
      // Web: Revoke blob URLs to free memory
      for (const uri of imageUris) {
        try {
          if (uri.startsWith("blob:")) {
            URL.revokeObjectURL(uri);
            console.log("🌐 Web: Revoked blob URL:", uri);
          }
        } catch (error) {
          console.warn("🌐 Web: Failed to revoke blob URL:", uri, error);
        }
      }
      return;
    }

    // Mobile: Delete actual files
    for (const uri of imageUris) {
      try {
        const info = await FileSystem.getInfoAsync(uri);
        if (info.exists) {
          await FileSystem.deleteAsync(uri);
          console.log("📱 Mobile: Cleaned up temp image:", uri);
        }
      } catch (error) {
        console.warn("📱 Mobile: Failed to cleanup temp image:", uri, error);
      }
    }
  } catch (error) {
    console.error("Error in cleanup:", error);
  }
};

/**
 * Generate unique filename for image
 */
export const generateImageFileName = (extension: string = "webp"): string => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `audit_image_${timestamp}_${random}.${extension}`;
};

/**
 * Calculate optimal compression settings based on image size
 */
export const getOptimalCompressionSettings = (
  originalSize: number
): ImageCompressionOptions => {
  const sizeMB = originalSize / (1024 * 1024);

  if (sizeMB > 10) {
    // Very large images - aggressive compression
    return {
      quality: 0.6,
      maxWidth: 1280,
      maxHeight: 720,
      format: "webp",
    };
  } else if (sizeMB > 5) {
    // Large images - moderate compression
    return {
      quality: 0.7,
      maxWidth: 1600,
      maxHeight: 900,
      format: "webp",
    };
  } else {
    // Smaller images - light compression
    return {
      quality: 0.8,
      maxWidth: 1920,
      maxHeight: 1080,
      format: "webp",
    };
  }
};

/**
 * Get optimized compression settings for batch uploads
 * More aggressive compression when uploading many images
 */
export const getBatchCompressionSettings = (
  imageCount: number,
  originalSize: number
): ImageCompressionOptions => {
  const sizeMB = originalSize / (1024 * 1024);

  // More aggressive compression for large batches
  if (imageCount > 20) {
    return {
      quality: 0.6,
      maxWidth: 1280,
      maxHeight: 720,
      format: "webp",
    };
  } else if (imageCount > 10) {
    return {
      quality: 0.7,
      maxWidth: 1600,
      maxHeight: 900,
      format: "webp",
    };
  } else {
    // Use standard compression for smaller batches
    return getOptimalCompressionSettings(originalSize);
  }
};
