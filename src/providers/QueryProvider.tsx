import React from "react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

// Create a client with mobile-optimized configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
      retry: (failureCount, error: any) => {
        // Get status from different possible error structures
        const status = error?.status || error?.response?.status || 0;

        // Don't retry on authentication errors
        if (status === 401 || status === 403) {
          return false;
        }

        // Don't retry on 4xx errors except 408, 429
        if (status >= 400 && status < 500) {
          if (status === 408 || status === 429) {
            return failureCount < 2;
          }
          return false;
        }

        // Don't retry if we've already failed too many times
        if (failureCount >= 3) {
          return false;
        }

        // Retry on network errors and 5xx errors
        return true;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
      // Mobile-specific optimizations
      refetchOnWindowFocus: false, // Disable for mobile
      refetchOnReconnect: true, // Enable for mobile network changes
    },
    mutations: {
      retry: 1, // Limited retries for mutations on mobile
      onError: (error: any) => {
        // Global error handling for mutations
        console.error("Mutation error:", error);
      },
    },
  },
});

// Query keys factory for consistent key management
export const queryKeys = {
  // Plans
  plans: {
    all: () => ["plans"] as const,
    lists: () => [...queryKeys.plans.all(), "list"] as const,
    list: (filters?: Record<string, any>) =>
      [...queryKeys.plans.lists(), filters] as const,
    details: () => [...queryKeys.plans.all(), "detail"] as const,
    detail: (id: string | number) =>
      [...queryKeys.plans.details(), id] as const,
  },

  // Stores
  stores: {
    all: () => ["stores"] as const,
    lists: () => [...queryKeys.stores.all(), "list"] as const,
    list: (filters?: Record<string, any>) =>
      [...queryKeys.stores.lists(), filters] as const,
    details: () => [...queryKeys.stores.all(), "detail"] as const,
    detail: (id: string | number) =>
      [...queryKeys.stores.details(), id] as const,
  },

  // Auth
  auth: {
    user: () => ["auth", "user"] as const,
    permissions: () => ["auth", "permissions"] as const,
  },
};

interface QueryProviderProps {
  children: React.ReactNode;
}

export function QueryProvider({ children }: QueryProviderProps) {
  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
}

export { queryClient };
