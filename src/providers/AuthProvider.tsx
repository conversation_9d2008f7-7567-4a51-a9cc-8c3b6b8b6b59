import React, { createContext, useContext, useEffect, useState } from "react";
import { router } from "expo-router";
import { authAPI } from "../api";
import { AuthUser } from "../types";

interface AuthContextType {
  user: AuthUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: string | null;
  login: (credentials: {
    identifier: string;
    password: string;
  }) => Promise<void>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<boolean>;
  clearError: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Check authentication on app start
  useEffect(() => {
    checkInitialAuth();
  }, []);

  const checkInitialAuth = async () => {
    try {
      const isAuth = await authAPI.isAuthenticated();
      if (isAuth) {
        const profile = await authAPI.getProfile();
        setUser(profile);
      }
    } catch (error) {
      // Silently fail initial auth check
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const clearError = () => {
    setError(null);
  };

  const login = async (credentials: {
    identifier: string;
    password: string;
  }) => {
    try {
      setError(null);

      const response = await authAPI.login(credentials);

      setUser(response.user);

      // Navigate to main app
      router.push("/(tabs)/home");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Đăng nhập thất bại";
      setError(errorMessage);
      throw error; // Re-throw to let component handle error display
    }
  };

  const logout = async () => {
    try {
      await authAPI.logout();
    } catch (error) {
      // Continue logout even if API fails
    } finally {
      setUser(null);
      router.push("/(auth)/login");
    }
  };

  const checkAuth = async () => {
    try {
      const isAuth = await authAPI.isAuthenticated();
      if (isAuth && !user) {
        const profile = await authAPI.getProfile();
        setUser(profile);
      } else if (!isAuth && user) {
        setUser(null);
      }
      return isAuth;
    } catch (error) {
      // Auth check failed, user not authenticated
      setUser(null);
      return false;
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: !!user,
    error,
    login,
    logout,
    checkAuth,
    clearError,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuthContext() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuthContext must be used within an AuthProvider");
  }
  return context;
}
