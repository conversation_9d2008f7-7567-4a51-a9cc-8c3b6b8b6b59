import { QuestionType, QuestionOptions } from "./entities";

// Form submission types
export interface AnswerSubmission {
  questionId: number;
  value: AnswerValue;
}

export type AnswerValue = string | number | boolean | string[] | File[] | null;

export interface VisitSubmission {
  planId: number;
  storeId: number;
  answers: AnswerSubmission[];
  submission_date?: string;
}

// Form validation types
export interface ValidationRule {
  required?: boolean;
  min?: number;
  max?: number;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => boolean | string;
}

export interface FormFieldProps {
  id: number;
  question_text: string;
  type: QuestionType;
  is_required: boolean;
  options?: QuestionOptions;
  value?: AnswerValue;
  onChange: (value: AnswerValue) => void;
  error?: string;
}

// Component state types
export interface AuditFormState {
  answers: Record<number, AnswerValue>;
  errors: Record<number, string>;
  isSubmitting: boolean;
  isDirty: boolean;
}

export interface FormProgress {
  totalQuestions: number;
  answeredQuestions: number;
  requiredAnswered: number;
  totalRequired: number;
  isComplete: boolean;
  percentage: number;
}

// File upload types
export interface FileUploadProgress {
  questionId: number;
  files: File[];
  progress: number;
  status: "idle" | "uploading" | "success" | "error";
  error?: string;
}

export interface ImagePreview {
  id: string;
  url: string;
  file: File;
  name: string;
  size: number;
}
