// Base Strapi Types
export interface StrapiBaseEntity {
  id: number;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface StrapiApiResponse<T> {
  data: T;
  meta: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

export interface StrapiEntityData<T> {
  id: number;
  documentId?: string; // Strapi v5 documentId
  attributes: T & {
    publishedAt?: string;
    createdAt: string;
    updatedAt: string;
  };
}

export interface StrapiRelation<T = any> {
  data: StrapiEntityData<T> | StrapiEntityData<T>[] | null;
}

export interface StrapiMedia {
  id: number;
  name: string;
  alternativeText?: string;
  caption?: string;
  width?: number;
  height?: number;
  formats?: Record<string, any>;
  hash: string;
  ext: string;
  mime: string;
  size: number;
  url: string;
  previewUrl?: string;
  provider: string;
  provider_metadata?: any;
  createdAt: string;
  updatedAt: string;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    pageSize: number;
    pageCount: number;
    total: number;
  };
}
