// Question Types
export const QUESTION_TYPES = {
  TEXT: "text",
  NUMBER: "number",
  PHOTO: "photo",
  SINGLE_CHOICE: "single_choice",
  MULTI_CHOICE: "multi_choice",
  YES_NO: "yes_no",
  RATING: "rating",
  DATE_TIME: "date_time",
} as const;

// Form Status
export const FORM_STATUS = {
  DRAFT: "draft",
  ACTIVE: "active",
  ARCHIVED: "archived",
} as const;

// Plan Status
export const PLAN_STATUS = {
  PENDING: "pending",
  IN_PROGRESS: "in_progress",
  COMPLETED: "completed",
} as const;

// User Roles
export const USER_ROLES = {
  ADMIN: "admin",
  MANAGER: "manager",
  AUDITOR: "auditor",
  MERCHANDISER: "merchandiser",
  VIEWER: "viewer",
} as const;

// Note: API_ENDPOINTS moved to src/api/endpoints.ts

// Permissions
export const PERMISSIONS = {
  VIEW_PLANS: "view_plans",
  CREATE_PLANS: "create_plans",
  EDIT_PLANS: "edit_plans",
  DELETE_PLANS: "delete_plans",
  VIEW_VISITS: "view_visits",
  CREATE_VISITS: "create_visits",
  EDIT_VISITS: "edit_visits",
  DELETE_VISITS: "delete_visits",
  VIEW_AUDIT_FORMS: "view_audit_forms",
  CREATE_AUDIT_FORMS: "create_audit_forms",
  EDIT_AUDIT_FORMS: "edit_audit_forms",
  DELETE_AUDIT_FORMS: "delete_audit_forms",
  VIEW_STORES: "view_stores",
  CREATE_STORES: "create_stores",
  EDIT_STORES: "edit_stores",
  DELETE_STORES: "delete_stores",
  VIEW_USERS: "view_users",
  CREATE_USERS: "create_users",
  EDIT_USERS: "edit_users",
  DELETE_USERS: "delete_users",
} as const;

// Role Permissions Mapping
export const ROLE_PERMISSIONS = {
  [USER_ROLES.ADMIN]: Object.values(PERMISSIONS),
  [USER_ROLES.MANAGER]: [
    PERMISSIONS.VIEW_PLANS,
    PERMISSIONS.CREATE_PLANS,
    PERMISSIONS.EDIT_PLANS,
    PERMISSIONS.VIEW_VISITS,
    PERMISSIONS.CREATE_VISITS,
    PERMISSIONS.EDIT_VISITS,
    PERMISSIONS.VIEW_AUDIT_FORMS,
    PERMISSIONS.CREATE_AUDIT_FORMS,
    PERMISSIONS.EDIT_AUDIT_FORMS,
    PERMISSIONS.VIEW_STORES,
    PERMISSIONS.EDIT_STORES,
    PERMISSIONS.VIEW_USERS,
    PERMISSIONS.EDIT_USERS,
  ],
  [USER_ROLES.AUDITOR]: [
    PERMISSIONS.VIEW_PLANS,
    PERMISSIONS.VIEW_VISITS,
    PERMISSIONS.CREATE_VISITS,
    PERMISSIONS.EDIT_VISITS,
    PERMISSIONS.VIEW_AUDIT_FORMS,
    PERMISSIONS.VIEW_STORES,
  ],
  [USER_ROLES.MERCHANDISER]: [
    PERMISSIONS.VIEW_PLANS,
    PERMISSIONS.VIEW_VISITS,
    PERMISSIONS.CREATE_VISITS,
    PERMISSIONS.VIEW_AUDIT_FORMS,
    PERMISSIONS.VIEW_STORES,
  ],
  [USER_ROLES.VIEWER]: [
    PERMISSIONS.VIEW_PLANS,
    PERMISSIONS.VIEW_VISITS,
    PERMISSIONS.VIEW_AUDIT_FORMS,
    PERMISSIONS.VIEW_STORES,
  ],
} as const;

// Validation Constants
export const VALIDATION = {
  USERNAME: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 50,
    PATTERN: /^[a-zA-Z0-9_]+$/,
  },
  PASSWORD: {
    MIN_LENGTH: 6,
    MAX_LENGTH: 128,
  },
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  },
  STORE_CODE: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 20,
    PATTERN: /^[A-Z0-9_]+$/,
  },
  STORE_NAME: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 100,
  },
  QUESTION_TEXT: {
    MIN_LENGTH: 5,
    MAX_LENGTH: 500,
  },
  AUDIT_FORM_NAME: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 100,
  },
} as const;

// File Upload Constants
export const FILE_UPLOAD = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_IMAGES_DEFAULT: 50, // High limit - effectively unlimited for practical use
  MAX_IMAGES_PHOTO_QUESTION: 50, // High limit for photo questions
  MAX_IMAGES_STORE_PHOTOS: 50, // High limit for store photos
  MAX_IMAGES_UNLIMITED: 999, // Very high limit for cases that need more flexibility
  ALLOWED_TYPES: [
    "image/jpeg",
    "image/png",
    "image/gif",
    "image/webp",
    "application/pdf",
    "video/mp4",
    "video/quicktime",
  ],
  IMAGE_TYPES: ["image/jpeg", "image/png", "image/gif", "image/webp"],
  VIDEO_TYPES: ["video/mp4", "video/quicktime"],
} as const;

// Question Type Specific Image Limits
export const QUESTION_IMAGE_LIMITS = {
  text: 10, // Text questions can have supporting images
  number: 10, // Number questions can have supporting images
  photo: 50, // Photo questions are primarily about images
  single_choice: 15, // Choice questions can have supporting images
  multi_choice: 15, // Multi-choice questions can have supporting images
  yes_no: 20, // Yes/No questions can have many supporting images
  rating: 15, // Rating questions can have supporting images
  date_time: 10, // Date/time questions can have supporting images
  store_photos: 50, // Store photos section
  audit_general: 30, // General audit images
} as const;

// Context-based image limits
export const CONTEXT_IMAGE_LIMITS = {
  question_answer: 50, // When answering questions
  store_documentation: 50, // Store documentation photos
  audit_evidence: 50, // Audit evidence photos
  general_upload: 50, // General file uploads
} as const;

// Utility function to get image limit based on question type or context
export const getImageLimit = (
  questionType?: keyof typeof QUESTION_IMAGE_LIMITS,
  context?: keyof typeof CONTEXT_IMAGE_LIMITS,
  customLimit?: number
): number => {
  // Custom limit takes precedence
  if (customLimit !== undefined) {
    return customLimit;
  }

  // Question type specific limit
  if (questionType && QUESTION_IMAGE_LIMITS[questionType]) {
    return QUESTION_IMAGE_LIMITS[questionType];
  }

  // Context specific limit
  if (context && CONTEXT_IMAGE_LIMITS[context]) {
    return CONTEXT_IMAGE_LIMITS[context];
  }

  // Default fallback
  return FILE_UPLOAD.MAX_IMAGES_DEFAULT;
};
