// Re-export all types from other files
export * from "./api";
export * from "./entities";
export * from "./forms";
export * from "./auth";
export * from "./constants";
export * from "./utils";

// Legacy types for backward compatibility
export interface Audit {
  id: string;
  title: string;
  description?: string;
  status: "pending" | "in_progress" | "completed" | "cancelled";
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  assignedTo?: string;
}
