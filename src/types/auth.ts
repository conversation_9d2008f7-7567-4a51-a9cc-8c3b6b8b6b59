import { FlatUser } from "./entities";

// Authentication types
export interface LoginCredentials {
  identifier: string; // email or username
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
}

export interface AuthResponse {
  jwt: string;
  user: FlatUser;
}

export interface AuthUser extends FlatUser {
  jwt?: string;
}

export interface AuthContextValue {
  user: AuthUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => void;
  updateProfile: (data: Partial<AuthUser>) => Promise<void>;
}

// Permission types
export type Permission =
  | "view_plans"
  | "create_plans"
  | "edit_plans"
  | "delete_plans"
  | "view_visits"
  | "create_visits"
  | "edit_visits"
  | "delete_visits"
  | "view_audit_forms"
  | "create_audit_forms"
  | "edit_audit_forms"
  | "delete_audit_forms"
  | "view_stores"
  | "create_stores"
  | "edit_stores"
  | "delete_stores"
  | "view_users"
  | "create_users"
  | "edit_users"
  | "delete_users";

export interface UserPermissions {
  [key: string]: Permission[];
}

// Role-based access control
export type UserRoleType =
  | "admin"
  | "manager"
  | "auditor"
  | "merchandiser"
  | "viewer";

export interface RolePermissions {
  [role: string]: Permission[];
}
