import { StrapiBaseEntity, StrapiMedia } from "./api";
import type { StrapiRelation, StrapiEntityData } from "./api";

// Re-export types for use in other modules
export type { StrapiRelation, StrapiEntityData };

// User Types (based on users-permissions plugin)
export interface User extends StrapiBaseEntity {
  username: string;
  email: string;
  provider?: string;
  confirmed: boolean;
  blocked: boolean;
  role: StrapiRelation<UserRole>;
  plans?: StrapiRelation<Plan[]>;
}

export interface UserRole {
  id: number;
  name: string;
  description?: string;
  type: string;
}

// Store Types
export interface Store extends StrapiBaseEntity {
  store_name: string;
  store_code: string;
  address?: string;
  is_active: boolean;
  plan?: StrapiRelation<Plan>;
}

// Question Types
export interface Question extends StrapiBaseEntity {
  question_text: string;
  type: QuestionType;
  is_required: boolean;
  options?: QuestionOptions;
  question_image?: StrapiRelation<StrapiMedia[]>;
  audit_forms: StrapiRelation<AuditForm[]>;
}

export type QuestionType =
  | "text"
  | "number"
  | "photo"
  | "single_choice"
  | "multi_choice"
  | "yes_no"
  | "rating"
  | "date_time";

export interface QuestionOptions {
  choices?: Array<{
    id: string;
    label: string;
    value?: string;
  }>;
  min?: number;
  max?: number;
  step?: number;
  labels?: Record<string, string>;
  multiple?: boolean;
}

// Audit Form Types
export interface AuditForm extends StrapiBaseEntity {
  name: string;
  version?: string;
  form_status: FormStatus;
  questions: StrapiRelation<Question[]>;
  plan?: StrapiRelation<Plan>;
}

export type FormStatus = "draft" | "active" | "archived";

// Plan Types
export interface Plan extends StrapiBaseEntity {
  start_date?: string;
  end_date?: string;
  plan_status?: PlanStatus;
  visits_target?: number;
  store: StrapiRelation<Store>;
  audit_form: StrapiRelation<AuditForm>;
  merchandiser: StrapiRelation<User>;
}

export type PlanStatus = "pending" | "in_progress" | "completed" | "expired";

// Visit Types
export interface Visit extends StrapiBaseEntity {
  submission_date?: string;
  merchandiser: StrapiRelation<User>;
  store: StrapiRelation<Store>;
  plan: StrapiRelation<Plan>;
  answer?: StrapiRelation<Answer>;
}

// Answer Types
export interface Answer extends StrapiBaseEntity {
  text_value?: string;
  boolean_value?: boolean;
  photo_value?: StrapiRelation<StrapiMedia[]>;
  multi_choice_values?: any; // JSON field
  yes_no_multi?: any; // JSON field
  visit: StrapiRelation<Visit>;
  question: StrapiRelation<Question>;
}

// User Answer Types
export interface UserAnswer extends StrapiBaseEntity {
  text_value?: string;
  boolean_value?: boolean;
  photo_value?: StrapiRelation<StrapiMedia[]>;
  multi_choice_values?: MultiChoiceValue[]; // JSON field for multiple choice responses
  yes_no_multi?: YesNoMultiValue; // JSON field for yes/no responses
  merchandiser: StrapiRelation<User>;
  plan: StrapiRelation<Plan>;
  question: StrapiRelation<Question>;
}

// JSON structure types for user answers
export interface MultiChoiceValue {
  id: string | number;
  label: string;
  value?: string;
}

export interface YesNoMultiValue {
  answer: boolean | string;
  question_id?: string | number;
  timestamp?: string;
  options?: any;
}

// Flattened types for easier use in components
export interface FlatUser {
  id: number;
  documentId?: string; // Strapi v5 documentId for users
  username: string;
  email: string;
  confirmed: boolean;
  blocked: boolean;
  role?: string;
  createdAt: string;
  updatedAt: string;
}

export interface FlatStore {
  id: number;
  store_name: string;
  store_code: string;
  address?: string;
  is_active: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface FlatQuestion {
  id: number;
  documentId?: string; // Strapi v5 documentId
  question_text: string;
  type: QuestionType;
  is_required: boolean;
  options?: QuestionOptions;
  question_image?: StrapiMedia[];
  createdAt: string;
  updatedAt: string;
}

export interface FlatAuditForm {
  id: number;
  documentId?: string; // Strapi v5 documentId
  name: string;
  version?: string;
  form_status: FormStatus;
  questions?: FlatQuestion[];
  createdAt: string;
  updatedAt: string;
}

export interface FlatPlan {
  id: number;
  documentId?: string; // Strapi v5 documentId
  assigned_date?: string;
  start_date?: string;
  end_date?: string;
  plan_status?: PlanStatus;
  visits_target?: number;
  plan_title?: string;
  store?: FlatStore;
  audit_form?: FlatAuditForm;
  merchandiser?: FlatUser;
  user_answers?: any[]; // Array of user answers for this plan
  createdAt: string;
  updatedAt: string;
}

export interface FlatVisit {
  id: number;
  submission_date?: string;
  merchandiser?: FlatUser;
  store?: FlatStore;
  plan?: FlatPlan;
  answers?: FlatAnswer[];
  createdAt: string;
  updatedAt: string;
}

export interface FlatAnswer {
  id: number;
  text_value?: string;
  boolean_value?: boolean;
  photo_value?: StrapiMedia[];
  multi_choice_values?: any;
  yes_no_multi?: any;
  question?: FlatQuestion;
  createdAt: string;
  updatedAt: string;
}

export interface FlatUserAnswer {
  id: number;
  text_value?: string;
  boolean_value?: boolean;
  photo_value?: StrapiMedia[];
  multi_choice_values?: MultiChoiceValue[];
  yes_no_multi?: YesNoMultiValue;
  merchandiser?: FlatUser;
  plan?: FlatPlan;
  question?: FlatQuestion;
  createdAt: string;
  updatedAt: string;
}
