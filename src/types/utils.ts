import {
  StrapiEntityData,
  StrapiRelation,
  User,
  Store,
  Question,
  AuditForm,
  Plan,
  Visit,
  Answer,
  FlatUser,
  FlatStore,
  FlatQuestion,
  FlatAuditForm,
  FlatPlan,
  FlatVisit,
  FlatAnswer,
} from "./entities";

// Utility type to extract relation data
export type ExtractRelationData<T> = T extends StrapiRelation<infer U>
  ? U
  : never;

// Utility type to make all relations optional
export type WithOptionalRelations<T> = {
  [K in keyof T]: T[K] extends StrapiRelation<any> ? T[K] | undefined : T[K];
};

// Utility functions to flatten Strapi entities
export function flattenUser(entity: StrapiEntityData<User>): FlatUser {
  const { attributes } = entity;
  return {
    id: entity.id,
    username: attributes.username,
    email: attributes.email,
    confirmed: attributes.confirmed,
    blocked: attributes.blocked,
    role: extractRelationData(attributes.role)?.name,
    createdAt: attributes.createdAt,
    updatedAt: attributes.updatedAt,
  };
}

export function flattenStore(entity: StrapiEntityData<Store>): FlatStore {
  const { attributes } = entity;
  return {
    id: entity.id,
    store_name: attributes.store_name,
    store_code: attributes.store_code,
    address: attributes.address,
    is_active: attributes.is_active,
    createdAt: attributes.createdAt,
    updatedAt: attributes.updatedAt,
  };
}

export function flattenQuestion(
  entity: StrapiEntityData<Question>
): FlatQuestion {
  const { attributes } = entity;
  return {
    id: entity.id,
    documentId: entity.documentId, // Strapi v5 documentId
    question_text: attributes.question_text,
    type: attributes.type,
    is_required: attributes.is_required,
    options: attributes.options,
    question_image: extractRelationArrayData(attributes.question_image),
    createdAt: attributes.createdAt,
    updatedAt: attributes.updatedAt,
  };
}

export function flattenAuditForm(
  entity: StrapiEntityData<AuditForm>
): FlatAuditForm {
  const { attributes } = entity;
  return {
    id: entity.id,
    name: attributes.name,
    version: attributes.version,
    form_status: attributes.form_status,
    questions: extractRelationArrayData(attributes.questions)?.map(
      flattenQuestion
    ),
    createdAt: attributes.createdAt,
    updatedAt: attributes.updatedAt,
  };
}

export function flattenPlan(entity: StrapiEntityData<Plan>): FlatPlan {
  const { attributes } = entity;
  return {
    id: entity.id,
    documentId: entity.documentId, // Strapi v5 documentId
    start_date: attributes.start_date,
    end_date: attributes.end_date,
    plan_status: attributes.plan_status,
    visits_target: attributes.visits_target,
    store: extractRelationData(attributes.store)
      ? flattenStore(extractRelationData(attributes.store)!)
      : undefined,
    audit_form: extractRelationData(attributes.audit_form)
      ? flattenAuditForm(extractRelationData(attributes.audit_form)!)
      : undefined,
    merchandiser: extractRelationData(attributes.merchandiser)
      ? flattenUser(extractRelationData(attributes.merchandiser)!)
      : undefined,
    createdAt: attributes.createdAt,
    updatedAt: attributes.updatedAt,
  };
}

export function flattenVisit(entity: StrapiEntityData<Visit>): FlatVisit {
  const { attributes } = entity;
  return {
    id: entity.id,
    submission_date: attributes.submission_date,
    merchandiser: extractRelationData(attributes.merchandiser)
      ? flattenUser(extractRelationData(attributes.merchandiser)!)
      : undefined,
    store: extractRelationData(attributes.store)
      ? flattenStore(extractRelationData(attributes.store)!)
      : undefined,
    plan: extractRelationData(attributes.plan)
      ? flattenPlan(extractRelationData(attributes.plan)!)
      : undefined,
    createdAt: attributes.createdAt,
    updatedAt: attributes.updatedAt,
  };
}

export function flattenAnswer(entity: StrapiEntityData<Answer>): FlatAnswer {
  const { attributes } = entity;
  return {
    id: entity.id,
    text_value: attributes.text_value,
    boolean_value: attributes.boolean_value,
    photo_value: extractRelationArrayData(attributes.photo_value),
    multi_choice_values: attributes.multi_choice_values,
    question: extractRelationData(attributes.question)
      ? flattenQuestion(extractRelationData(attributes.question)!)
      : undefined,
    createdAt: attributes.createdAt,
    updatedAt: attributes.updatedAt,
  };
}

// Helper functions to extract relation data
export function extractRelationData<T>(
  relation: StrapiRelation<T> | undefined
): StrapiEntityData<T> | null {
  if (!relation?.data) return null;
  if (Array.isArray(relation.data)) return relation.data[0] || null;
  return relation.data;
}

export function extractRelationArrayData<T>(
  relation: StrapiRelation<T[]> | undefined
): StrapiEntityData<T>[] | undefined {
  if (!relation?.data) return undefined;
  if (Array.isArray(relation.data)) return relation.data;
  return [relation.data];
}

// Type guards
export function isArrayRelation<T>(
  relation: StrapiRelation<T | T[]>
): relation is StrapiRelation<T[]> {
  return Array.isArray(relation.data);
}

export function isSingleRelation<T>(
  relation: StrapiRelation<T | T[]>
): relation is StrapiRelation<T> {
  return !Array.isArray(relation.data);
}

// Helper to create Strapi API query parameters
export interface StrapiQueryParams {
  populate?: string | string[] | Record<string, any>;
  filters?: Record<string, any>;
  sort?: string | string[];
  pagination?: {
    page?: number;
    pageSize?: number;
    start?: number;
    limit?: number;
  };
  fields?: string[];
  locale?: string;
}

export function buildStrapiQuery(params: StrapiQueryParams): URLSearchParams {
  const searchParams = new URLSearchParams();

  if (params.populate) {
    if (typeof params.populate === "string") {
      searchParams.append("populate", params.populate);
    } else if (Array.isArray(params.populate)) {
      params.populate.forEach((field) =>
        searchParams.append("populate", field)
      );
    } else {
      // Handle deep populate
      Object.entries(params.populate).forEach(([key, value]) => {
        if (typeof value === "string") {
          searchParams.append(`populate[${key}]`, value);
        } else if (typeof value === "object") {
          Object.entries(value).forEach(([subKey, subValue]) => {
            searchParams.append(
              `populate[${key}][${subKey}]`,
              String(subValue)
            );
          });
        }
      });
    }
  }

  if (params.filters) {
    Object.entries(params.filters).forEach(([key, value]) => {
      if (typeof value === "object" && value !== null) {
        Object.entries(value).forEach(([operator, operatorValue]) => {
          searchParams.append(
            `filters[${key}][${operator}]`,
            String(operatorValue)
          );
        });
      } else {
        searchParams.append(`filters[${key}]`, String(value));
      }
    });
  }

  if (params.sort) {
    if (Array.isArray(params.sort)) {
      params.sort.forEach((field) => searchParams.append("sort", field));
    } else {
      searchParams.append("sort", params.sort);
    }
  }

  if (params.pagination) {
    Object.entries(params.pagination).forEach(([key, value]) => {
      searchParams.append(`pagination[${key}]`, String(value));
    });
  }

  if (params.fields) {
    params.fields.forEach((field) => searchParams.append("fields", field));
  }

  if (params.locale) {
    searchParams.append("locale", params.locale);
  }

  return searchParams;
}
