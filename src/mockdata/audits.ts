export interface AuditItem {
  id: string;
  documentId?: string; // Strapi v5 documentId
  plan_title: string;
  assigned_date: string;
  start_date: string;
  end_date: string;
  plan_status: "pending" | "in_progress" | "completed" | "expired";
  visits_target: number;
  store: {
    id: string;
    store_name: string;
    store_code: string;
    address: string;
  };
  audit_form: {
    id: string;
    name: string;
    version: string;
    form_status: "draft" | "active" | "archived";
    questions: Question[];
  };
  merchandiser: {
    id: string;
    name: string;
    email: string;
  };
}

export interface Question {
  id: string;
  documentId?: string; // Strapi v5 documentId
  question_text: string;
  type:
    | "text"
    | "number"
    | "photo"
    | "single_choice"
    | "multi_choice"
    | "yes_no"
    | "rating";
  is_required: boolean;
  options?: any;
  question_image?: string[];
  is_image_answer_require?: boolean;
  is_record_answer_require?: boolean;
  category?: string;
  sub_category?: string;
}

export interface Answer {
  id: string;
  question_id: string;
  text_value?: string;
  boolean_value?: boolean;
  photo_value?: string[];
  multi_choice_values?: string[];
  yes_no_multi?: any;
}

export interface AuditTab {
  key: string;
  label: string;
}

// Mock data for audit items
export const mockAuditData: AuditItem[] = [
  {
    id: "12",
    plan_title: "DTV",
    assigned_date: "2025-08-04T02:30:00.000Z",
    start_date: "2025-08-04T00:00:00.000Z",
    end_date: "2025-08-04T23:59:59.000Z",
    plan_status: "pending",
    visits_target: 1,
    store: {
      id: "ST001",
      store_name: "Điện thoại vui 123 Nguyễn Văn Lượng",
      store_code: "DTV001",
      address: "123 Nguyễn Văn Lượng, TP.HCM",
    },
    audit_form: {
      id: "AF001",
      name: "AuditForm DTV",
      version: "1.0",
      form_status: "active",
      questions: [
        // Category: Tư vấn sửa chữa - Sub-category: Phàn nàn giá cả và chất lượng
        {
          id: "Q001",
          question_text:
            "NV có giới thiệu về nguồn gốc Linh kiện và các chính sách bảo hành ?",
          type: "yes_no",
          is_required: true,
          options: {
            category: "Tư vấn sửa chữa",
            sub_category: "Phàn nàn giá cả và chất lượng",
            description:
              "NV có kiểm tra ngoại quan & báo lỗi phát sinh trên máy? (lỗi máy, giá, CLLK, rủi ro phát sinh)",
          },
        },
        {
          id: "Q002",
          question_text:
            "Kỹ thuật có chủ động đưa khách xem linh kiện trực tiếp hay không",
          type: "yes_no",
          is_required: true,
          options: {
            category: "Tư vấn sửa chữa",
            sub_category: "Phàn nàn giá cả và chất lượng",
          },
        },
        {
          id: "Q003",
          question_text:
            "NV vui vẻ, nhiệt tình tư vấn đầy đủ về quy trình sửa chữa, không tỏ ra thái độ khi khách thể hiện thái độ cần gấp",
          type: "yes_no",
          is_required: true,
          options: {
            category: "Tư vấn sửa chữa",
            sub_category: "Phàn nàn giá cả và chất lượng",
          },
        },
        {
          id: "Q004",
          question_text:
            "NV có hỏi quản lý/ kỹ thuật để ưu tiên làm sớm cho khách được hay không",
          type: "yes_no",
          is_required: true,
          options: {
            category: "Tư vấn sửa chữa",
            sub_category: "Phàn nàn giá cả và chất lượng",
          },
        },
        {
          id: "Q005",
          question_text:
            "Nhân viên kiên nhẫn, nhiệt tình, xin lại SĐT để chăm sóc sau",
          type: "yes_no",
          is_required: true,
          options: {
            category: "Tư vấn sửa chữa",
            sub_category: "Phàn nàn giá cả và chất lượng",
          },
        },
        // Category: Ưu đãi học sinh sinh viên - Sub-category: Không chính chủ
        {
          id: "Q006",
          question_text: "Ngày thực hiện",
          type: "yes_no",
          is_required: true,
          options: {
            category: "Ưu đãi học sinh sinh viên",
            sub_category: "Không chính chủ",
          },
        },
        {
          id: "Q007",
          question_text: "Khung giờ",
          type: "yes_no",
          is_required: true,
          options: {
            category: "Ưu đãi học sinh sinh viên",
            sub_category: "Không chính chủ",
          },
        },
        {
          id: "Q008",
          question_text: "Tên nhân viên",
          type: "yes_no",
          is_required: true,
          options: {
            category: "Ưu đãi học sinh sinh viên",
            sub_category: "Không chính chủ",
          },
        },
        {
          id: "Q009",
          question_text:
            "Nhân viên chủ động tư vấn ưu đãi chương trình S-student",
          type: "yes_no",
          is_required: true,
          options: {
            category: "Ưu đãi học sinh sinh viên",
            sub_category: "Không chính chủ",
          },
        },
        {
          id: "Q010",
          question_text:
            "Nhân viên tư vấn/giới thiệu sau khi khách hỏi về chương trình",
          type: "yes_no",
          is_required: true,
          options: {
            category: "Ưu đãi học sinh sinh viên",
            sub_category: "Không chính chủ",
          },
        },
        {
          id: "Q011",
          question_text:
            "Nhân viên không đồng ý đăng ký tài khoản khi không có chính chủ thẻ sinh viên tại cửa hàng",
          type: "yes_no",
          is_required: true,
          options: {
            category: "Ưu đãi học sinh sinh viên",
            sub_category: "Không chính chủ",
          },
        },
        {
          id: "Q012",
          question_text:
            "Nhân viên chủ động gợi ý giải pháp (video call, hình ảnh,...)",
          type: "yes_no",
          is_required: true,
          options: {
            category: "Ưu đãi học sinh sinh viên",
            sub_category: "Không chính chủ",
          },
        },
        {
          id: "Q013",
          question_text:
            "Nhân viên xin số điện thoại & gợi ý khách dắt sinh viên ra cửa hàng",
          type: "yes_no",
          is_required: true,
          options: {
            category: "Ưu đãi học sinh sinh viên",
            sub_category: "Không chính chủ",
          },
        },
        {
          id: "Q014",
          question_text: "Giảm 10% tối đa 500k",
          type: "yes_no",
          is_required: true,
          options: {
            category: "Ưu đãi học sinh sinh viên",
            sub_category: "Không chính chủ",
          },
        },
        {
          id: "Q015",
          question_text: "Tặng quà đặc quyền ĐTV trị giá 300k (quạt)",
          type: "yes_no",
          is_required: true,
          options: {
            category: "Ưu đãi học sinh sinh viên",
            sub_category: "Không chính chủ",
          },
        },
        {
          id: "Q016",
          question_text: "Trả góp 3 không",
          type: "yes_no",
          is_required: true,
          options: {
            category: "Ưu đãi học sinh sinh viên",
            sub_category: "Không chính chủ",
          },
        },
        {
          id: "Q017",
          question_text: "Thu cũ lên đời",
          type: "yes_no",
          is_required: true,
          options: {
            category: "Ưu đãi học sinh sinh viên",
            sub_category: "Không chính chủ",
          },
        },
        // Category: Chất lượng dịch vụ
        {
          id: "Q018",
          question_text: "Chất lượng dịch vụ",
          type: "rating",
          is_required: true,
          options: {
            category: "Chất lượng dịch vụ",
            choices: {
              "1": "Rất không hài lòng",
              "2": "Không hài lòng",
              "3": "Bình thường",
              "4": "Hài lòng",
              "5": "Rất hài lòng",
            },
          },
        },
        // Category: Thời gian phục vụ
        {
          id: "Q019",
          question_text: "Thời gian phục vụ",
          type: "single_choice",
          is_required: true,
          options: {
            category: "Thời gian phục vụ",
            choices: {
              fast: "Dưới 10 phút",
              normal: "10-20 phút",
              slow: "Trên 20 phút",
            },
          },
        },
        // Category: Ghi chú
        {
          id: "Q020",
          question_text: "Ghi chú thêm",
          type: "text",
          is_required: false,
          options: {
            category: "Ghi chú",
          },
        },
        // Category: Chụp ảnh
        {
          id: "Q021",
          question_text: "Chụp ảnh cửa hàng",
          type: "photo",
          is_required: true,
          options: {
            category: "Chụp ảnh",
            sample_images: ["sample1.jpg", "sample2.jpg"],
          },
        },
        // Category: Số liệu
        {
          id: "Q022",
          question_text: "Số lượng khách hàng",
          type: "number",
          is_required: true,
          options: {
            category: "Số liệu",
          },
        },
        // Category: Dịch vụ sử dụng
        {
          id: "Q023",
          question_text: "Các dịch vụ sử dụng",
          type: "multi_choice",
          is_required: true,
          options: {
            category: "Dịch vụ sử dụng",
            choices: {
              repair: "Sửa chữa",
              consult: "Tư vấn",
              warranty: "Bảo hành",
              accessory: "Phụ kiện",
            },
          },
        },
      ],
    },
    merchandiser: {
      id: "USR001",
      name: "Nguyễn Văn A",
      email: "<EMAIL>",
    },
  },
  {
    id: "AUD001",
    plan_title: "Kiểm toán tháng 12 - Quận 1",
    assigned_date: "2024-12-20T08:00:00.000Z",
    start_date: "2024-12-15T00:00:00.000Z",
    end_date: "2024-12-20T23:59:59.000Z",
    plan_status: "pending",
    visits_target: 5,
    store: {
      id: "ST001",
      store_name: "Cellphones Nguyễn Huệ",
      store_code: "CP001",
      address: "123 Nguyễn Huệ, Quận 1, TP.HCM",
    },
    audit_form: {
      id: "AF001",
      name: "TradeAudit Detail",
      version: "1.0",
      form_status: "active",
      questions: [
        {
          id: "Q008",
          question_text: "Kiểm tra trưng bày sản phẩm",
          type: "yes_no",
          is_required: true,
          options: {
            category: "Kiểm tra",
            sub_category: "Trưng bày",
          },
        },
        {
          id: "Q009",
          question_text: "Đánh giá chất lượng dịch vụ",
          type: "rating",
          is_required: true,
          options: {
            category: "Chất lượng dịch vụ",
            choices: {
              "1": "Rất kém",
              "2": "Kém",
              "3": "Trung bình",
              "4": "Tốt",
              "5": "Rất tốt",
            },
          },
        },
      ],
    },
    merchandiser: {
      id: "USR001",
      name: "Nguyễn Văn A",
      email: "<EMAIL>",
    },
  },
  {
    id: "AUD002",
    plan_title: "Kiểm toán tháng 12 - Quận 3",
    assigned_date: "2024-12-18T08:00:00.000Z",
    start_date: "2024-12-14T00:00:00.000Z",
    end_date: "2024-12-18T23:59:59.000Z",
    plan_status: "in_progress",
    visits_target: 3,
    store: {
      id: "ST002",
      store_name: "Cellphones Võ Văn Tần",
      store_code: "CP002",
      address: "456 Võ Văn Tần, Quận 3, TP.HCM",
    },
    audit_form: {
      id: "AF002",
      name: "TradeAudit DetailPlus",
      version: "2.0",
      form_status: "active",
      questions: [
        {
          id: "Q010",
          question_text: "Kiểm tra vệ sinh cửa hàng",
          type: "yes_no",
          is_required: true,
          options: {
            category: "Kiểm tra",
            sub_category: "Vệ sinh",
          },
        },
        {
          id: "Q011",
          question_text: "Ghi chú chi tiết",
          type: "text",
          is_required: false,
          options: {
            category: "Ghi chú",
          },
        },
      ],
    },
    merchandiser: {
      id: "USR002",
      name: "Trần Thị B",
      email: "<EMAIL>",
    },
  },
  {
    id: "AUD003",
    plan_title: "Mystery Shopper - Quận 7",
    assigned_date: "2024-12-17T08:00:00.000Z",
    start_date: "2024-12-13T00:00:00.000Z",
    end_date: "2024-12-17T23:59:59.000Z",
    plan_status: "completed",
    visits_target: 4,
    store: {
      id: "ST003",
      store_name: "Cellphones Crescent Mall",
      store_code: "CP003",
      address: "789 Crescent Mall, Quận 7, TP.HCM",
    },
    audit_form: {
      id: "AF003",
      name: "Mystery Shopper",
      version: "1.5",
      form_status: "active",
      questions: [
        {
          id: "Q012",
          question_text: "Đánh giá trải nghiệm khách hàng",
          type: "rating",
          is_required: true,
          options: {
            category: "Trải nghiệm khách hàng",
            choices: {
              "1": "Rất không hài lòng",
              "2": "Không hài lòng",
              "3": "Bình thường",
              "4": "Hài lòng",
              "5": "Rất hài lòng",
            },
          },
        },
      ],
    },
    merchandiser: {
      id: "USR003",
      name: "Lê Văn C",
      email: "<EMAIL>",
    },
  },
  {
    id: "AUD004",
    plan_title: "Kiểm toán tháng 12 - Bình Thạnh",
    assigned_date: "2024-12-25T08:00:00.000Z",
    start_date: "2024-12-16T00:00:00.000Z",
    end_date: "2024-12-22T23:59:59.000Z",
    plan_status: "pending",
    visits_target: 6,
    store: {
      id: "ST004",
      store_name: "Cellphones Landmark 81",
      store_code: "CP004",
      address: "321 Landmark 81, Quận Bình Thạnh, TP.HCM",
    },
    audit_form: {
      id: "AF004",
      name: "TradeAudit Standard",
      version: "1.2",
      form_status: "active",
      questions: [
        {
          id: "Q013",
          question_text: "Kiểm tra hàng tồn kho",
          type: "yes_no",
          is_required: true,
          options: {
            category: "Kiểm tra",
            sub_category: "Hàng tồn kho",
          },
        },
      ],
    },
    merchandiser: {
      id: "USR004",
      name: "Phạm Thị D",
      email: "<EMAIL>",
    },
  },
  {
    id: "AUD005",
    plan_title: "Kiểm toán tháng 12 - Quận 1",
    assigned_date: "2024-12-10T08:00:00.000Z",
    start_date: "2024-12-10T00:00:00.000Z",
    end_date: "2024-12-15T23:59:59.000Z",
    plan_status: "expired",
    visits_target: 2,
    store: {
      id: "ST005",
      store_name: "Cellphones Vincom Center",
      store_code: "CP005",
      address: "654 Vincom Center, Quận 1, TP.HCM",
    },
    audit_form: {
      id: "AF005",
      name: "TradeAudit Premium",
      version: "3.0",
      form_status: "active",
      questions: [
        {
          id: "Q014",
          question_text: "Đánh giá tổng thể",
          type: "rating",
          is_required: true,
          options: {
            category: "Đánh giá tổng thể",
            choices: {
              "1": "Kém",
              "2": "Trung bình",
              "3": "Tốt",
              "4": "Rất tốt",
              "5": "Xuất sắc",
            },
          },
        },
      ],
    },
    merchandiser: {
      id: "USR005",
      name: "Hoàng Văn E",
      email: "<EMAIL>",
    },
  },
  {
    id: "AUD006",
    plan_title: "Kiểm toán tháng 12 - Quận 2",
    assigned_date: "2024-12-28T08:00:00.000Z",
    start_date: "2024-12-25T00:00:00.000Z",
    end_date: "2024-12-30T23:59:59.000Z",
    plan_status: "pending",
    visits_target: 3,
    store: {
      id: "ST006",
      store_name: "Cellphones Thủ Đức",
      store_code: "CP006",
      address: "987 Thủ Đức, TP.HCM",
    },
    audit_form: {
      id: "AF006",
      name: "TradeAudit Basic",
      version: "1.1",
      form_status: "active",
      questions: [
        {
          id: "Q015",
          question_text: "Kiểm tra cơ bản",
          type: "yes_no",
          is_required: true,
          options: {
            category: "Kiểm tra",
            sub_category: "Cơ bản",
          },
        },
      ],
    },
    merchandiser: {
      id: "USR006",
      name: "Võ Thị F",
      email: "<EMAIL>",
    },
  },
];

export const auditTabs: AuditTab[] = [
  { key: "pending", label: "Cần thực hiện" },
  { key: "completed", label: "Đã hoàn thành" },
];
