# 📊 Mock Data Structure

Thư mục này chứa tất cả mock data được sử dụng trong ứng dụng Cellphones Audit.

## 📁 Cấu trúc thư mục

```
src/mockdata/
├── index.ts          # Export tất cả mock data
├── stores.ts         # Dữ liệu cửa hàng
├── notifications.ts  # Dữ liệu thông báo
├── users.ts          # Dữ liệu người dùng
├── audits.ts         # Dữ liệu kiểm toán
└── README.md         # Tài liệu này
```

## 🏪 Stores (`stores.ts`)

**Interface**: `StoreCodeOption`
- `value`: Mã cửa hàng (VD: "CPS-HCM-LXD-001HN")
- `label`: Tên cửa hàng (VD: "Cửa hàng Lotte Xpress Đông - Hà Nội")

**Mock Data**: `mockStoreCodes`
- 45 cửa hàng Cellphones trên toàn quốc
- Các chuỗi: Lotte Xpress, Vincom, SC VivoCity, AEON Mall, Crescent Mall, Diamond Plaza, Parkson, Gigamall, ICON68
- <PERSON><PERSON><PERSON>ành phố: <PERSON><PERSON> N<PERSON>, Sài <PERSON>òn, Đà Nẵng, Hải Phòng, Cần Thơ

## 🔔 Notifications (`notifications.ts`)

**Interface**: `Notification`
- `id`: ID thông báo
- `title`: Tiêu đề
- `message`: Nội dung
- `type`: Loại thông báo ("success" | "error" | "reminder" | "system" | "assignment")
- `timestamp`: Thời gian
- `isRead`: Trạng thái đọc
- `storeName`: Tên cửa hàng (nếu có)
- `auditId`: ID kiểm toán (nếu có)

**Mock Data**: `mockNotifications`, `notificationTabs`

## 👤 Users (`users.ts`)

**Interface**: `UserProfile`
- Thông tin cá nhân: avatar, fullName, email, phone, employeeId
- Thông tin công việc: position, department, joinDate
- Thông tin cửa hàng: storeCode, storeName, storeAddress, region
- Thông tin quản lý: manager, managerPhone

**Mock Data**: `mockUserData`, `mockAuthUser`

## 📋 Audits (`audits.ts`)

**Interface**: `AuditItem`
- `id`: ID kiểm toán
- `title`: Tiêu đề kiểm toán
- `store`: Tên cửa hàng
- `date`: Ngày kiểm toán
- `status`: Trạng thái ("completed" | "in-progress" | "pending")
- `progress`: Tiến độ (0-100)

**Mock Data**: `mockAuditData`, `auditTabs`

## 🚀 Cách sử dụng

```typescript
// Import tất cả mock data
import { mockStoreCodes, mockNotifications, mockUserData, mockAuditData } from "~/src/mockdata";

// Hoặc import từng file riêng
import { mockStoreCodes } from "~/src/mockdata/stores";
import { mockNotifications } from "~/src/mockdata/notifications";
```

## 🔄 Cập nhật Mock Data

Khi cần thêm mock data mới:

1. Tạo interface cho type safety
2. Tạo mock data với dữ liệu thực tế
3. Export từ file riêng
4. Thêm vào `index.ts` để export chung
5. Cập nhật tài liệu này

## 📝 Lưu ý

- Tất cả mock data phải có TypeScript interface
- Dữ liệu phải thực tế và đa dạng
- Sử dụng cho development và testing
- Khi tích hợp API thực tế, thay thế bằng dữ liệu từ server 