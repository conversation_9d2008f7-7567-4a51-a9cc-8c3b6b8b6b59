export interface StoreCodeOption {
  value: string;
  label: string;
}

// Mock data for store codes
export const mockStoreCodes: StoreCodeOption[] = [
  { value: "CPS-HCM-LXD-001HN", label: "Cửa hàng Lotte Xpress Đông - Hà Nội" },
  { value: "CPS-HCM-LXD-002SG", label: "Cửa hàng Lotte Xpress Đông - Sài Gòn" },
  { value: "CPS-VTA-LXD-003DN", label: "Cửa hàng Lotte Xpress Đông - Đà Nẵng" },
  {
    value: "CPS-BTB-LXD-004HP",
    label: "Cửa hàng Lotte Xpress Đông - Hải Phòng",
  },
  { value: "CPS-QNA-LXD-005CT", label: "Cửa hàng Lotte Xpress Đông - Cần Thơ" },
  { value: "CPS-HCM-VIN-006HN", label: "Cửa hàng Vincom - H<PERSON>" },
  { value: "CPS-HCM-VIN-007SG", label: "Cửa hàng Vincom - Sài Gòn" },
  { value: "CPS-VTA-VIN-008DN", label: "Cửa hàng Vincom - Đà Nẵng" },
  { value: "CPS-BTB-VIN-009HP", label: "Cửa hàng Vincom - Hải Phòng" },
  { value: "CPS-QNA-VIN-010CT", label: "Cửa hàng Vincom - Cần Thơ" },
  { value: "CPS-HCM-SCR-011HN", label: "Cửa hàng SC VivoCity - Hà Nội" },
  { value: "CPS-HCM-SCR-012SG", label: "Cửa hàng SC VivoCity - Sài Gòn" },
  { value: "CPS-VTA-SCR-013DN", label: "Cửa hàng SC VivoCity - Đà Nẵng" },
  { value: "CPS-BTB-SCR-014HP", label: "Cửa hàng SC VivoCity - Hải Phòng" },
  { value: "CPS-QNA-SCR-015CT", label: "Cửa hàng SC VivoCity - Cần Thơ" },
  { value: "CPS-HCM-AEO-016HN", label: "Cửa hàng AEON Mall - Hà Nội" },
  { value: "CPS-HCM-AEO-017SG", label: "Cửa hàng AEON Mall - Sài Gòn" },
  { value: "CPS-VTA-AEO-018DN", label: "Cửa hàng AEON Mall - Đà Nẵng" },
  { value: "CPS-BTB-AEO-019HP", label: "Cửa hàng AEON Mall - Hải Phòng" },
  { value: "CPS-QNA-AEO-020CT", label: "Cửa hàng AEON Mall - Cần Thơ" },
  { value: "CPS-HCM-CRP-021HN", label: "Cửa hàng Crescent Mall - Hà Nội" },
  { value: "CPS-HCM-CRP-022SG", label: "Cửa hàng Crescent Mall - Sài Gòn" },
  { value: "CPS-VTA-CRP-023DN", label: "Cửa hàng Crescent Mall - Đà Nẵng" },
  { value: "CPS-BTB-CRP-024HP", label: "Cửa hàng Crescent Mall - Hải Phòng" },
  { value: "CPS-QNA-CRP-025CT", label: "Cửa hàng Crescent Mall - Cần Thơ" },
  // Additional stores for better scroll testing
  { value: "CPS-HCM-DMC-026HN", label: "Cửa hàng Diamond Plaza - Hà Nội" },
  { value: "CPS-HCM-DMC-027SG", label: "Cửa hàng Diamond Plaza - Sài Gòn" },
  { value: "CPS-VTA-DMC-028DN", label: "Cửa hàng Diamond Plaza - Đà Nẵng" },
  { value: "CPS-BTB-DMC-029HP", label: "Cửa hàng Diamond Plaza - Hải Phòng" },
  { value: "CPS-QNA-DMC-030CT", label: "Cửa hàng Diamond Plaza - Cần Thơ" },
  { value: "CPS-HCM-PVT-031HN", label: "Cửa hàng Parkson - Hà Nội" },
  { value: "CPS-HCM-PVT-032SG", label: "Cửa hàng Parkson - Sài Gòn" },
  { value: "CPS-VTA-PVT-033DN", label: "Cửa hàng Parkson - Đà Nẵng" },
  { value: "CPS-BTB-PVT-034HP", label: "Cửa hàng Parkson - Hải Phòng" },
  { value: "CPS-QNA-PVT-035CT", label: "Cửa hàng Parkson - Cần Thơ" },
  { value: "CPS-HCM-GGM-036HN", label: "Cửa hàng Gigamall - Hà Nội" },
  { value: "CPS-HCM-GGM-037SG", label: "Cửa hàng Gigamall - Sài Gòn" },
  { value: "CPS-VTA-GGM-038DN", label: "Cửa hàng Gigamall - Đà Nẵng" },
  { value: "CPS-BTB-GGM-039HP", label: "Cửa hàng Gigamall - Hải Phòng" },
  { value: "CPS-QNA-GGM-040CT", label: "Cửa hàng Gigamall - Cần Thơ" },
  { value: "CPS-HCM-ICS-041HN", label: "Cửa hàng ICON68 - Hà Nội" },
  { value: "CPS-HCM-ICS-042SG", label: "Cửa hàng ICON68 - Sài Gòn" },
  { value: "CPS-VTA-ICS-043DN", label: "Cửa hàng ICON68 - Đà Nẵng" },
  { value: "CPS-BTB-ICS-044HP", label: "Cửa hàng ICON68 - Hải Phòng" },
  { value: "CPS-QNA-ICS-045CT", label: "Cửa hàng ICON68 - Cần Thơ" },
]; 