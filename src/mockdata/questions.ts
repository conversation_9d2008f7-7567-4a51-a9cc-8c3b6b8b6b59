import { Question } from "~/src/api/questions";

// Mock questions data
export const mockQuestions: Question[] = [
  {
    id: 1,
    question_text: "Cửa hàng có đang hoạt động bình thường không?",
    type: "yes_no",
    is_required: true,
    options: {},
  },
  {
    id: 2,
    question_text: "Sản phẩm có được trưng bày đúng vị trí không?",
    type: "yes_no",
    is_required: true,
    options: {},
  },
  {
    id: 3,
    question_text: "Nhân viên có mặc đồng phục không?",
    type: "yes_no",
    is_required: false,
    options: {},
  },
  {
    id: 4,
    question_text: "Chọn loại sản phẩm có sẵn:",
    type: "multi_choice",
    is_required: true,
    options: {
      choices: [
        { id: 1, label: "Điện thoại" },
        { id: 2, label: "Laptop" },
        { id: 3, label: "Tablet" },
        { id: 4, label: "<PERSON><PERSON> kiện" },
      ],
      multiple: true,
    },
  },
  {
    id: 5,
    question_text: "Chọ<PERSON> thương hiệu chính:",
    type: "single_choice",
    is_required: true,
    options: {
      choices: [
        { id: 1, label: "Apple" },
        { id: 2, label: "Samsung" },
        { id: 3, label: "Xiaomi" },
        { id: 4, label: "OPPO" },
        { id: 5, label: "Vivo" },
      ],
      multiple: false,
    },
  },
  {
    id: 6,
    question_text: "Ghi chú về tình trạng cửa hàng:",
    type: "text",
    is_required: false,
    options: {},
  },
  {
    id: 7,
    question_text: "Số lượng sản phẩm trưng bày:",
    type: "number",
    is_required: true,
    options: {
      min: 0,
      max: 1000,
    },
  },
  {
    id: 8,
    question_text: "Đánh giá chất lượng phục vụ (1-5):",
    type: "rating",
    is_required: true,
    options: {
      min: 1,
      max: 5,
      labels: ["Rất kém", "Kém", "Trung bình", "Tốt", "Rất tốt"],
    },
  },
  {
    id: 9,
    question_text: "Chụp ảnh khu vực trưng bày:",
    type: "photo",
    is_required: true,
    options: {},
  },
];

// Mock questions by audit form ID
export const getMockQuestionsByAuditForm = (
  auditFormId: number
): Question[] => {
  // Return different questions based on audit form ID
  if (auditFormId === 1) {
    return mockQuestions.slice(0, 5); // First 5 questions
  } else if (auditFormId === 2) {
    return mockQuestions.slice(2, 7); // Questions 3-7
  } else {
    return mockQuestions; // All questions
  }
};

// Mock questions service
export const MockQuestionService = {
  async getQuestionsByAuditForm(auditFormId: number): Promise<Question[]> {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 500));
    return getMockQuestionsByAuditForm(auditFormId);
  },

  async getQuestion(id: number): Promise<Question> {
    await new Promise((resolve) => setTimeout(resolve, 300));
    const question = mockQuestions.find((q) => q.id === id);
    if (!question) {
      throw new Error("Question not found");
    }
    return question;
  },
};
