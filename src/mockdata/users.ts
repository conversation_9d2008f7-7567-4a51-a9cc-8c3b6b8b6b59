export interface UserProfile {
  avatar: string;
  fullName: string;
  email: string;
  phone: string;
  employeeId: string;
  position: string;
  department: string;
  joinDate: string;
  storeCode: string;
  storeName: string;
  storeAddress: string;
  region: string;
  manager: string;
  managerPhone: string;
}

export interface MenuItem {
  id: string;
  title: string;
  icon: any;
  path: string;
  isDestructive?: boolean;
}

// Mock data for user profile
export const mockUserData: UserProfile = {
  avatar:
    "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
  fullName: "Nguyễ<PERSON>",
  email: "<EMAIL>",
  phone: "0901234567",
  employeeId: "NV001",
  position: "<PERSON>hân viên kiểm toán",
  department: "Phòng Kiểm toán",
  joinDate: "15/03/2023",
  storeCode: "CP001",
  storeName: "Cellphones Ng<PERSON>ễ<PERSON>",
  storeAddress: "123 <PERSON><PERSON><PERSON><PERSON>, Quận 1, TP.HCM",
  region: "Miề<PERSON>",
  manager: "<PERSON><PERSON><PERSON><PERSON>",
  managerPhone: "0909876543",
};

// Mock data for authentication
export const mockAuthUser = {
  id: "1",
  name: "Test User",
  email: "<EMAIL>",
  role: "auditor",
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
}; 