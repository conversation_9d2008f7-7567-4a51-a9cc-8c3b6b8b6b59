export interface Notification {
  id: string;
  title: string;
  message: string;
  type: "success" | "error" | "reminder" | "system" | "assignment";
  timestamp: string;
  isRead: boolean;
  storeName: string | null;
  auditId: string | null;
}

export interface NotificationTab {
  key: string;
  label: string;
  count: number;
}

// Mock data for notifications
export const mockNotifications: Notification[] = [
  {
    id: "1",
    title: "Kiểm toán hoàn thành",
    message:
      "Kiểm toán tại Cellphones Nguyễn Huệ đã được hoàn thành thành công",
    type: "success",
    timestamp: "2 giờ trước",
    isRead: false,
    storeName: "Cellphones Nguyễn Hu<PERSON>",
    auditId: "AUD001",
  },
  {
    id: "2",
    title: "Nhắc nhở kiểm toán",
    message: "Bạn có lịch kiểm toán tại Cellphones Võ Văn Tần vào ngày mai",
    type: "reminder",
    timestamp: "4 giờ trước",
    isRead: false,
    storeName: "Cellphones Võ Văn <PERSON>",
    auditId: "AUD002",
  },
  {
    id: "3",
    title: "Cậ<PERSON> nhật hệ thống",
    message: "<PERSON><PERSON> thống đã được cập nhật với các tính năng mới",
    type: "system",
    timestamp: "1 ngày trước",
    isRead: true,
    storeName: null,
    auditId: null,
  },
  {
    id: "4",
    title: "Kiểm toán bị từ chối",
    message:
      "Kiểm toán tại Cellphones Crescent Mall đã bị từ chối do thiếu thông tin",
    type: "error",
    timestamp: "2 ngày trước",
    isRead: true,
    storeName: "Cellphones Crescent Mall",
    auditId: "AUD003",
  },
  {
    id: "5",
    title: "Phân công mới",
    message: "Bạn được phân công kiểm toán tại Cellphones Landmark 81",
    type: "assignment",
    timestamp: "3 ngày trước",
    isRead: true,
    storeName: "Cellphones Landmark 81",
    auditId: "AUD004",
  },
];

export const notificationTabs: NotificationTab[] = [
  { key: "all", label: "Tất cả", count: 5 },
  { key: "unread", label: "Chưa đọc", count: 2 },
  { key: "success", label: "Thành công", count: 1 },
  { key: "error", label: "Lỗi", count: 1 },
  { key: "reminder", label: "Nhắc nhở", count: 1 },
]; 