// Test script to verify answer data structure and transformation
// This helps debug the JSON data structure issues

console.log('🧪 Testing Answer Data Structure and Transformation...\n');

// Mock the UserAnswerTransformer for testing
const mockTransformAnswerToUserAnswer = (questionId, value, questionType, merchandiserDocumentId, planDocumentId) => {
  const baseData = {
    merchandiser: merchandiserDocumentId,
    plan: planDocumentId,
    question: questionId,
  };

  switch (questionType) {
    case "text":
      return {
        ...baseData,
        text_value: value, // null for unanswered questions
      };

    case "number":
      return {
        ...baseData,
        text_value: value !== null ? value.toString() : null,
      };

    case "photo":
      return {
        ...baseData,
        photo_value: value !== null ? (Array.isArray(value) ? value : [value]) : null,
      };

    case "single_choice":
      return {
        ...baseData,
        text_value: value,
      };

    case "multi_choice":
      return {
        ...baseData,
        multi_choice_values: value !== null 
          ? (Array.isArray(value)
              ? value.map((choice, index) => ({
                  id: choice.id || index,
                  label: choice.label || choice,
                  value: choice.value || choice.label || choice,
                }))
              : [])
          : null,
      };

    case "yes_no":
      return {
        ...baseData,
        yes_no_multi: value !== null 
          ? {
              answer: value,
              question_id: questionId,
              timestamp: new Date().toISOString(),
            }
          : null,
      };

    case "rating":
      return {
        ...baseData,
        text_value: value !== null ? value.toString() : null,
      };

    default:
      return {
        ...baseData,
        text_value: value !== null ? value.toString() : null,
      };
  }
};

// Test data
const mockMerchandiserDocumentId = 'user_doc_123';
const mockPlanDocumentId = 'plan_doc_456';

const testCases = [
  {
    name: 'Text Question - Answered',
    questionId: 'q1_doc',
    value: 'This is a text answer',
    questionType: 'text'
  },
  {
    name: 'Text Question - Unanswered',
    questionId: 'q2_doc',
    value: null,
    questionType: 'text'
  },
  {
    name: 'Yes/No Question - Answered (True)',
    questionId: 'q3_doc',
    value: true,
    questionType: 'yes_no'
  },
  {
    name: 'Yes/No Question - Answered (False)',
    questionId: 'q4_doc',
    value: false,
    questionType: 'yes_no'
  },
  {
    name: 'Yes/No Question - Unanswered',
    questionId: 'q5_doc',
    value: null,
    questionType: 'yes_no'
  },
  {
    name: 'Multi-Choice Question - Answered',
    questionId: 'q6_doc',
    value: [
      { id: 1, label: 'Option 1', value: 'opt1' },
      { id: 2, label: 'Option 2', value: 'opt2' }
    ],
    questionType: 'multi_choice'
  },
  {
    name: 'Multi-Choice Question - Unanswered',
    questionId: 'q7_doc',
    value: null,
    questionType: 'multi_choice'
  },
  {
    name: 'Photo Question - Answered',
    questionId: 'q8_doc',
    value: [1, 2, 3],
    questionType: 'photo'
  },
  {
    name: 'Photo Question - Unanswered',
    questionId: 'q9_doc',
    value: null,
    questionType: 'photo'
  },
  {
    name: 'Rating Question - Answered',
    questionId: 'q10_doc',
    value: 4,
    questionType: 'rating'
  },
  {
    name: 'Rating Question - Unanswered',
    questionId: 'q11_doc',
    value: null,
    questionType: 'rating'
  }
];

console.log('📋 Running transformation tests...\n');

testCases.forEach((testCase, index) => {
  console.log(`🧪 Test ${index + 1}: ${testCase.name}`);
  console.log(`   Input: ${JSON.stringify(testCase.value)}`);
  
  try {
    const result = mockTransformAnswerToUserAnswer(
      testCase.questionId,
      testCase.value,
      testCase.questionType,
      mockMerchandiserDocumentId,
      mockPlanDocumentId
    );
    
    console.log(`   Output:`, JSON.stringify(result, null, 4));
    
    // Verify the structure
    const hasCorrectBase = result.merchandiser === mockMerchandiserDocumentId &&
                          result.plan === mockPlanDocumentId &&
                          result.question === testCase.questionId;
    
    console.log(`   ✅ Base structure: ${hasCorrectBase ? 'PASS' : 'FAIL'}`);
    
    // Check answer field presence
    const answerFields = ['text_value', 'yes_no_multi', 'multi_choice_values', 'photo_value'];
    const presentFields = answerFields.filter(field => result.hasOwnProperty(field));
    
    console.log(`   📊 Answer fields present: ${presentFields.join(', ')}`);
    
    // Check for null handling
    if (testCase.value === null) {
      const allAnswerFieldsNullOrUndefined = answerFields.every(field => 
        !result.hasOwnProperty(field) || result[field] === null
      );
      console.log(`   🔍 Null handling: ${allAnswerFieldsNullOrUndefined ? 'PASS' : 'FAIL'}`);
    }
    
  } catch (error) {
    console.log(`   ❌ ERROR: ${error.message}`);
  }
  
  console.log(''); // Empty line for readability
});

console.log('🎯 Key Findings:');
console.log('1. Each question type should have exactly one answer field');
console.log('2. Answered questions should have non-null values in their answer field');
console.log('3. Unanswered questions should have null values in their answer field');
console.log('4. All records should have the same base structure (merchandiser, plan, question)');
console.log('5. The hasOwnProperty check should return true for the answer field even when null');

console.log('\n📝 Expected Database Structure:');
console.log('- Answered text question: { text_value: "answer", other_fields: undefined }');
console.log('- Unanswered text question: { text_value: null, other_fields: undefined }');
console.log('- Answered yes_no question: { yes_no_multi: {...}, other_fields: undefined }');
console.log('- Unanswered yes_no question: { yes_no_multi: null, other_fields: undefined }');

console.log('\n🔧 This test helps verify:');
console.log('✅ Transformation logic correctness');
console.log('✅ Null value handling');
console.log('✅ Field presence for hasOwnProperty checks');
console.log('✅ Data structure consistency');
