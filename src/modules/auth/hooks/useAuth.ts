import { useState, useEffect } from "react";
import { AuthState, User, LoginCredentials, RegisterData } from "../types";
import { mockAuthUser } from "~/src/mockdata";

export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isLoading: true,
    isAuthenticated: false,
  });

  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      // TODO: Check token from secure storage
      setTimeout(() => {
        setAuthState({
          user: null,
          isLoading: false,
          isAuthenticated: false,
        });
      }, 1000);
    } catch (error) {
      setAuthState({
        user: null,
        isLoading: false,
        isAuthenticated: false,
      });
    }
  };

  const login = async (credentials: LoginCredentials) => {
    try {
      setAuthState((prev) => ({ ...prev, isLoading: true }));

      // TODO: Real API call

      const mockUser: User = {
        ...mockAuthUser,
        email: credentials.email,
        role: "auditor" as const,
      };

      setAuthState({
        user: mockUser,
        isLoading: false,
        isAuthenticated: true,
      });

      return { success: true, data: mockUser };
    } catch (error) {
      setAuthState((prev) => ({ ...prev, isLoading: false }));
      return {
        success: false,
        error: error instanceof Error ? error.message : "Đăng nhập thất bại",
      };
    }
  };

  const register = async (data: RegisterData) => {
    try {
      setAuthState((prev) => ({ ...prev, isLoading: true }));

      // TODO: Real API call
      const mockUser: User = {
        id: "1",
        name: data.name,
        email: data.email,
        role: "user",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      setAuthState({
        user: mockUser,
        isLoading: false,
        isAuthenticated: true,
      });

      return { success: true, data: mockUser };
    } catch (error) {
      setAuthState((prev) => ({ ...prev, isLoading: false }));
      return {
        success: false,
        error: error instanceof Error ? error.message : "Đăng ký thất bại",
      };
    }
  };

  const logout = async () => {
    try {
      // TODO: Clear tokens, call logout API
      setAuthState({
        user: null,
        isLoading: false,
        isAuthenticated: false,
      });
    } catch (error) {
      // Logout failed silently
    }
  };

  return {
    ...authState,
    login,
    register,
    logout,
  };
};
