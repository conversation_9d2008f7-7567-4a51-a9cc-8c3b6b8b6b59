import { View, Text, ScrollView, Pressable, Alert } from "react-native";
import React, { useState } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { ScreenWithBottomNav } from "~/components/shared/ScreenWithBottomNav";
import { Card, CardContent } from "~/components/ui/card";
import {
  User,
  Store,
  Settings,
  LogOut,
  ChevronRight,
  Phone,
  Mail,
  MapPin,
  Calendar,
  Shield,
  HelpCircle,
} from "lucide-react-native";
import { useColorScheme } from "~/lib/useColorScheme";
import { useAuth } from "~/src/hooks/useAuth";

import { mockUserData } from "~/src/mockdata";

const menuItems = [
  // {
  //   id: "personal",
  //   title: "Thông tin cá nhân",
  //   icon: User,
  //   path: "/profile/personal",
  // },
  // {
  //   id: "store",
  //   title: "Thông tin cửa hàng",
  //   icon: Store,
  //   path: "/profile/store",
  // },
  // {
  //   id: "settings",
  //   title: "Cài đặt",
  //   icon: Settings,
  //   path: "/profile/settings",
  // },
  // {
  //   id: "security",
  //   title: "Bảo mật",
  //   icon: Shield,
  //   path: "/profile/security",
  // },
  // {
  //   id: "help",
  //   title: "Trợ giúp",
  //   icon: HelpCircle,
  //   path: "/profile/help",
  // },
  {
    id: "logout",
    title: "Đăng xuất",
    icon: LogOut,
    path: "/logout",
    isDestructive: true,
  },
];

export default function ProfileScreen() {
  const { user, isLoading } = useAuth();

  // Sử dụng username từ API response, fallback về mock data
  const displayName = user?.username || user?.email?.split('@')[0] || mockUserData.fullName;

  const handleLogout = async () => {
    localStorage.removeItem("auth_token");
    location.reload();
  };

  const handleMenuPress = (item: any) => {
    // Handle menu item press
    // TODO: Implement menu actions
    if (item.id === "logout") {
      handleLogout();
      return;
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("");
  };

  if (isLoading) {
    return (
      <SafeAreaView className="flex-1 bg-background">
        <ScrollView className="flex-1 items-center justify-center">
          <Text className="text-lg text-gray-500">Đang tải...</Text>
        </ScrollView>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-background">
      <ScrollView className="flex-1">
        {/* Header */}
        <View className="bg-primary px-4 py-6">
          <Text className="text-white text-xl font-bold text-center">
            Hồ sơ cá nhân
          </Text>
        </View>

        {/* Profile Section */}
        <View className="px-4 py-6">
          <Card className="mb-6">
            <CardContent className="p-6">
              <View className="items-center mb-6">
                <View className="w-24 h-24 rounded-full bg-gray-200 items-center justify-center mb-4 overflow-hidden">
                  <User size={48} color="#6b7280" />
                </View>
                <Text className="text-xl font-bold text-foreground mb-2 text-center">
                  {displayName}
                </Text>
                {/* <Text className="text-sm text-gray-600 mb-1 text-center">
                  {mockUserData.position}
                </Text>
                <Text className="text-xs text-gray-500 text-center">
                  {mockUserData.department}
                </Text> */}
              </View>

              {/* Contact Info */}
              <View className="space-y-4">
                <View className="flex-row items-center">
                  <View className="w-8 h-8 rounded-full bg-gray-100 items-center justify-center mr-3">
                    <Mail size={16} color="#6b7280" />
                  </View>
                  <View className="flex-1">
                    <Text className="text-sm text-gray-600">
                      {user?.email}
                    </Text>
                  </View>
                </View>
                {/* <View className="flex-row items-center">
                  <View className="w-8 h-8 rounded-full bg-gray-100 items-center justify-center mr-3">
                    <Phone size={16} color="#6b7280" />
                  </View>
                  <View className="flex-1">
                    <Text className="text-sm text-gray-600">
                      {mockUserData.phone}
                    </Text>
                  </View>
                </View> */}
                <View className="flex-row items-center">
                  <View className="w-8 h-8 rounded-full bg-gray-100 items-center justify-center mr-3">
                    <Calendar size={16} color="#6b7280" />
                  </View>
                  <View className="flex-1">
                    <Text className="text-sm text-gray-600">
                      {user?.createdAt 
                        ? `Ngày tạo: ${new Date(user.createdAt).toLocaleDateString('vi-VN')}`
                        : `Ngày vào làm: ${mockUserData.joinDate}`
                      }
                    </Text>
                  </View>
                </View>
              </View>
            </CardContent>
          </Card>

          {/* Store Information */}
          {/* <Card className="mb-6">
            <CardContent className="p-6">
              <View className="flex-row items-center mb-6">
                <View className="w-8 h-8 rounded-full bg-blue-100 items-center justify-center mr-3">
                  <Store size={20} color="#3b82f6" />
                </View>
                <Text className="text-lg font-semibold text-foreground">
                  Thông tin cửa hàng
                </Text>
              </View>

              <View className="space-y-4">
                <View className="flex-row items-start">
                  <Text className="text-sm font-medium text-gray-600 w-24 mt-1">
                    Mã cửa hàng:
                  </Text>
                  <Text className="text-sm text-foreground flex-1 ml-3">
                    {mockUserData.storeCode}
                  </Text>
                </View>
                <View className="flex-row items-start">
                  <Text className="text-sm font-medium text-gray-600 w-24 mt-1">
                    Tên cửa hàng:
                  </Text>
                  <Text className="text-sm text-foreground flex-1 ml-3">
                    {mockUserData.storeName}
                  </Text>
                </View>
                <View className="flex-row items-start">
                  <Text className="text-sm font-medium text-gray-600 w-24 mt-1">
                    Địa chỉ:
                  </Text>
                  <Text className="text-sm text-foreground flex-1 ml-3 leading-5">
                    {mockUserData.storeAddress}
                  </Text>
                </View>
                <View className="flex-row items-start">
                  <Text className="text-sm font-medium text-gray-600 w-24 mt-1">
                    Khu vực:
                  </Text>
                  <Text className="text-sm text-foreground flex-1 ml-3">
                    {mockUserData.region}
                  </Text>
                </View>
                <View className="flex-row items-start">
                  <Text className="text-sm font-medium text-gray-600 w-24 mt-1">
                    Quản lý:
                  </Text>
                  <Text className="text-sm text-foreground flex-1 ml-3">
                    {mockUserData.manager}
                  </Text>
                </View>
                <View className="flex-row items-start">
                  <Text className="text-sm font-medium text-gray-600 w-24 mt-1">
                    SĐT QL:
                  </Text>
                  <Text className="text-sm text-foreground flex-1 ml-3">
                    {mockUserData.managerPhone}
                  </Text>
                </View>
              </View>
            </CardContent>
          </Card> */}

          {/* Menu Items */}
          <Card>
            <CardContent className="p-0">
              {menuItems.map((item, index) => (
                <Pressable
                  key={item.id}
                  onPress={() => handleMenuPress(item)}
                  className={`flex-row items-center justify-between p-4 ${
                    index !== menuItems.length - 1
                      ? "border-b border-gray-100"
                      : ""
                  }`}
                >
                  <View className="flex-row items-center flex-1">
                    <View
                      className={`w-8 h-8 rounded-full items-center justify-center mr-3 ${
                        item.isDestructive ? "bg-red-100" : "bg-gray-100"
                      }`}
                    >
                      <item.icon
                        size={18}
                        color={item.isDestructive ? "#ef4444" : "#6b7280"}
                      />
                    </View>
                    <Text
                      className={`text-sm flex-1 ${
                        item.isDestructive ? "text-red-500" : "text-foreground"
                      }`}
                    >
                      {item.title}
                    </Text>
                  </View>
                  <ChevronRight size={16} color="#9ca3af" />
                </Pressable>
              ))}
            </CardContent>
          </Card>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
