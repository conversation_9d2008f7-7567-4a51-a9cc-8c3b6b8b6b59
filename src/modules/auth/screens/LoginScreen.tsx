import React, { useState } from "react";
import { View, SafeAreaView, <PERSON><PERSON>, ScrollView, Pressable } from "react-native";
import { Text } from "~/components/ui/text";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { useAuth } from "~/src/hooks/useAuth";
import { LinearGradient } from "expo-linear-gradient";
import { 
  Eye, 
  EyeOff, 
  Mail, 
  Lock, 
  Smartphone, 
  Shield,
  ArrowRight,
  AlertCircle
} from "lucide-react-native";

export function LoginScreen() {
  const [formData, setFormData] = useState({
    identifier: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState(false);

  const { login, isLoading, error, clearError } = useAuth();

  const handleLogin = async () => {
    // Basic validation
    if (!formData.identifier.trim()) {
      Alert.alert("Lỗi", "<PERSON><PERSON> lòng nhập email hoặc tên đăng nhập");
      return;
    }

    if (!formData.password.trim()) {
      Alert.alert("Lỗi", "Vui lòng nhập mật khẩu");
      return;
    }

    try {
      await login(formData);
      // Navigation is handled in the hook
    } catch (error) {
      // Error is already shown in UI via error state
    }
  };

  const handleInputChange = (
    field: "identifier" | "password",
    value: string
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (error) {
      clearError();
    }
  };

  return (
    <LinearGradient
      colors={['#667eea', '#764ba2']}
      className="flex-1"
    >
      <SafeAreaView className="flex-1">
        <ScrollView 
          className="flex-1" 
          contentContainerStyle={{ flexGrow: 1 }}
          showsVerticalScrollIndicator={false}
        >
          <View className="flex-1 justify-center px-6 space-y-6">
            {/* Header Section - Giảm margin */}
            <View className="items-center mb-4">
              {/* App Logo/Icon */}
              <View className="w-20 h-20 bg-white/20 rounded-full items-center justify-center mb-4">
                <Smartphone size={40} color="white" />
              </View>
              
              <Text className="text-4xl font-bold text-white text-center mb-2">
                Cellphones Audit
              </Text>
              <Text className="text-white/80 text-center text-lg">
                Hệ thống kiểm toán thông minh
              </Text>
            </View>

            {/* Login Card - Tăng padding */}
            <View className="bg-white/95 rounded-3xl p-8 shadow-2xl">
              <View className="items-center mb-8">
                <Text className="text-2xl font-bold text-gray-800 mb-2">
                  Đăng nhập
                </Text>
                <Text className="text-gray-600 text-center">
                  Nhập thông tin để truy cập hệ thống
                </Text>
              </View>

              {/* Error Message */}
              {error && (
                <View className="bg-red-50 border border-red-200 rounded-xl p-4 mb-6 flex-row items-center">
                  <AlertCircle size={20} color="#ef4444" />
                  <Text className="text-red-600 text-sm ml-2 flex-1">{error}</Text>
                </View>
              )}

              {/* Login Form - Tăng spacing giữa các fields */}
              <View className="space-y-8">
                {/* Email/Username Input */}
                <View className="relative mb-4">
                  <View className="absolute left-4 top-4 z-10">
                    <Mail size={20} color="#6b7280" />
                  </View>
                  <Input
                    placeholder="Email hoặc tên đăng nhập"
                    value={formData.identifier}
                    onChangeText={(value) => handleInputChange("identifier", value)}
                    autoCapitalize="none"
                    autoCorrect={false}
                    keyboardType="email-address"
                    className="pl-12 pr-4 py-5 bg-gray-50 border-gray-200 rounded-xl text-base min-h-[56px]"
                    editable={!isLoading}
                  />
                </View>

                {/* Password Input */}
                <View className="relative mt-4">
                  <View className="absolute left-4 top-4 z-10">
                    <Lock size={20} color="#6b7280" />
                  </View>
                  <Input
                    placeholder="Mật khẩu"
                    value={formData.password}
                    onChangeText={(value) => handleInputChange("password", value)}
                    secureTextEntry={!showPassword}
                    autoCapitalize="none"
                    autoCorrect={false}
                    className="pl-12 pr-12 py-5 bg-gray-50 border-gray-200 rounded-xl text-base min-h-[56px]"
                    editable={!isLoading}
                  />
                  <Pressable
                    onPress={() => setShowPassword(!showPassword)}
                    className="absolute right-4 top-4 z-10"
                  >
                    {showPassword ? (
                      <EyeOff size={20} color="#6b7280" />
                    ) : (
                      <Eye size={20} color="#6b7280" />
                    )}
                  </Pressable>
                </View>
              </View>

              {/* Action Buttons */}
              <View className="space-y-4 mt-8">
                <Button 
                  onPress={handleLogin} 
                  className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl py-4 shadow-lg"
                  disabled={isLoading}
                >
                  <View className="flex-row items-center justify-center">
                    <Text className="text-white font-semibold text-lg mr-2">
                      {isLoading ? "Đang đăng nhập..." : "Đăng nhập"}
                    </Text>
                    <ArrowRight size={20} color="white" />
                  </View>
                </Button>

                {/* Security Note */}
                <View className="flex-row items-center justify-center mt-6">
                  <Shield size={16} color="#6b7280" />
                  <Text className="text-gray-500 text-sm ml-2">
                    Thông tin của bạn được bảo mật
                  </Text>
                </View>
              </View>
            </View>

            {/* Footer */}
            <View className="items-center mt-4">
              <Text className="text-white/70 text-sm text-center">
                © 2024 Cellphones Audit. All rights reserved.
              </Text>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </LinearGradient>
  );
}
