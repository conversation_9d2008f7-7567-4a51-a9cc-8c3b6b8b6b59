import { View, Text, ScrollView, Pressable } from "react-native";
import React, { useState } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { ScreenWithBottomNav } from "~/components/shared/ScreenWithBottomNav";
import { Card, CardContent } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import {
  Bell,
  AlertCircle,
  CheckCircle,
  Clock,
  ChevronRight,
  Filter,
  Trash2,
} from "lucide-react-native";
import { useColorScheme } from "~/lib/useColorScheme";

import { mockNotifications, notificationTabs } from "~/src/mockdata";

export default function NotificationScreen() {
  const [activeTab, setActiveTab] = useState("all");
  const { isDarkColorScheme } = useColorScheme();

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case "success":
        return <CheckCircle size={20} color="#10b981" />;
      case "error":
        return <AlertCircle size={20} color="#ef4444" />;
      case "reminder":
        return <Clock size={20} color="#f59e0b" />;
      case "system":
        return <Bell size={20} color="#3b82f6" />;
      case "assignment":
        return <AlertCircle size={20} color="#8b5cf6" />;
      default:
        return <Bell size={20} color="#6b7280" />;
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case "success":
        return "bg-green-100 border-green-200";
      case "error":
        return "bg-red-100 border-red-200";
      case "reminder":
        return "bg-orange-100 border-orange-200";
      case "system":
        return "bg-blue-100 border-blue-200";
      case "assignment":
        return "bg-purple-100 border-purple-200";
      default:
        return "bg-gray-100 border-gray-200";
    }
  };

  const getIconBackgroundColor = (type: string) => {
    switch (type) {
      case "success":
        return "bg-green-200";
      case "error":
        return "bg-red-200";
      case "reminder":
        return "bg-orange-200";
      case "system":
        return "bg-blue-200";
      case "assignment":
        return "bg-purple-200";
      default:
        return "bg-gray-200";
    }
  };

  const getNotificationTextColor = (type: string) => {
    switch (type) {
      case "success":
        return "text-green-800";
      case "error":
        return "text-red-800";
      case "reminder":
        return "text-orange-800";
      case "system":
        return "text-blue-800";
      case "assignment":
        return "text-purple-800";
      default:
        return "text-gray-800";
    }
  };

  const filteredNotifications =
    activeTab === "all"
      ? mockNotifications
      : activeTab === "unread"
      ? mockNotifications.filter((n) => !n.isRead)
      : mockNotifications.filter((n) => n.type === activeTab);

  const handleNotificationPress = (notification: Notification) => {
    // Handle notification press
    // TODO: Implement notification actions
  };

  const handleMarkAllAsRead = () => {
    // Mark all notifications as read
    // TODO: Implement mark all as read
  };

  const handleClearAll = () => {
    // Clear all notifications
    // TODO: Implement clear all
  };

  return (
    <SafeAreaView className="flex-1 bg-background">
      <ScrollView className="flex-1">
        {/* Header */}
        <View className="bg-primary px-4 py-6">
          <View className="flex-row items-center justify-between">
            <Text className="text-white text-xl font-bold">Thông báo</Text>
            <View className="flex-row space-x-2">
              <Pressable
                onPress={handleMarkAllAsRead}
                className="px-3 py-1 bg-white/20 rounded-full mr-2"
              >
                <Text className="text-white text-xs">Đánh dấu đã đọc</Text>
              </Pressable>
              <Pressable
                onPress={handleClearAll}
                className="px-3 py-1 bg-white/20 rounded-full"
              >
                <Text className="text-white text-xs">Xóa tất cả</Text>
              </Pressable>
            </View>
          </View>
        </View>

        {/* Tab Navigation */}
        <View className="py-4">
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View className="flex-row px-4">
              {notificationTabs.map((tab) => (
                <Pressable
                  key={tab.key}
                  onPress={() => setActiveTab(tab.key)}
                  className={`px-4 py-2 mx-1 rounded-full border ${
                    activeTab === tab.key
                      ? "bg-primary border-primary"
                      : "bg-white border-gray-300"
                  }`}
                >
                  <View className="flex-row items-center">
                    <Text
                      className={`font-medium text-sm ${
                        activeTab === tab.key ? "text-white" : "text-gray-700"
                      }`}
                    >
                      {tab.label}
                    </Text>
                    {tab.count > 0 && (
                      <Badge
                        className={`ml-2 ${
                          activeTab === tab.key
                            ? "bg-white text-primary"
                            : "bg-gray-200 text-gray-700"
                        }`}
                      >
                        <Text className="text-xs">{tab.count}</Text>
                      </Badge>
                    )}
                  </View>
                </Pressable>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Notifications List */}
        <View className="px-4 pb-6">
          {filteredNotifications.length === 0 ? (
            <Card>
              <CardContent className="p-8 items-center">
                <Bell size={48} color="#9ca3af" className="mb-4" />
                <Text className="text-lg font-semibold text-gray-600 mb-2">
                  Không có thông báo
                </Text>
                <Text className="text-sm text-gray-500 text-center">
                  {activeTab === "all"
                    ? "Bạn chưa có thông báo nào"
                    : `Không có thông báo ${notificationTabs
                        .find((t) => t.key === activeTab)
                        ?.label.toLowerCase()}`}
                </Text>
              </CardContent>
            </Card>
          ) : (
            filteredNotifications.map((notification, index) => (
              <Pressable
                key={notification.id}
                onPress={() => handleNotificationPress(notification)}
                className="mb-3"
              >
                <Card
                  className={`${getNotificationColor(notification.type)} ${
                    !notification.isRead ? "border-l-4 border-l-primary" : ""
                  }`}
                >
                  <CardContent className="p-4">
                    <View className="flex-row items-start">
                      <View
                        className={`w-10 h-10 rounded-full items-center justify-center mr-3 ${getIconBackgroundColor(
                          notification.type
                        )}`}
                      >
                        {getNotificationIcon(notification.type)}
                      </View>
                      <View className="flex-1">
                        <View className="flex-row items-start justify-between mb-2">
                          <Text
                            className={`font-semibold text-sm flex-1 ${
                              !notification.isRead
                                ? "text-foreground"
                                : "text-gray-600"
                            }`}
                          >
                            {notification.title}
                          </Text>
                          <Text className="text-xs text-gray-500 ml-2">
                            {notification.timestamp}
                          </Text>
                        </View>

                        <Text className="text-sm text-gray-600 mb-2 leading-5">
                          {notification.message}
                        </Text>

                        {notification.storeName && (
                          <View className="flex-row items-center justify-between">
                            <Text className="text-xs text-gray-500">
                              {notification.storeName}
                            </Text>
                            <ChevronRight size={14} color="#9ca3af" />
                          </View>
                        )}
                      </View>
                    </View>
                  </CardContent>
                </Card>
              </Pressable>
            ))
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
