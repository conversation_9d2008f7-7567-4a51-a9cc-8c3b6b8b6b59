import React from "react";
import { Button } from "~/components/ui/button";
import { Text } from "~/components/ui/text";

interface AuthButtonProps {
  title: string;
  onPress: () => void;
  variant?: "default" | "secondary" | "outline";
  className?: string;
}

export function AuthButton({
  title,
  onPress,
  variant = "default",
  className,
}: AuthButtonProps) {
  return (
    <Button variant={variant} onPress={onPress} className={className}>
      <Text>{title}</Text>
    </Button>
  );
}
