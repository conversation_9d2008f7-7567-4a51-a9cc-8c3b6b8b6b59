import React, { useState, useEffect } from "react";
import { View, Text, Alert } from "react-native";
import { PhotoPicker } from "~/src/components/ui/photo-picker";
import { ImageStorageManager, StoredImage } from "~/src/utils/imageStorage";
import {
  Camera,
  Image as ImageIcon,
  Upload,
  CheckCircle,
} from "lucide-react-native";
import { getImageLimit, QUESTION_IMAGE_LIMITS } from "~/src/types/constants";

interface UniversalImageUploadProps {
  questionId: string;
  questionType: string;
  questionText: string;
  planId?: string;
  maxImages?: number;
  enableCompression?: boolean;
  showUploadStatus?: boolean;
  onImagesChange?: (imageUris: string[]) => void;
  onStoredImagesChange?: (storedImages: StoredImage[]) => void;
}

/**
 * Universal Image Upload Component for All Question Types
 * Integrates with any question type to provide image upload functionality
 */
export function UniversalImageUpload({
  questionId,
  questionType,
  questionText,
  planId,
  maxImages,
  enableCompression = true,
  showUploadStatus = true,
  onImagesChange,
  onStoredImagesChange,
}: UniversalImageUploadProps) {
  // Calculate appropriate image limit based on question type
  const imageLimit =
    maxImages ||
    getImageLimit(
      questionType as keyof typeof QUESTION_IMAGE_LIMITS,
      "question_answer"
    );
  const [imageUris, setImageUris] = useState<string[]>([]);
  const [storedImages, setStoredImages] = useState<StoredImage[]>([]);
  const [uploadStats, setUploadStats] = useState({
    total: 0,
    uploaded: 0,
    pending: 0,
    totalSize: 0,
  });

  // Load existing images on mount and when questionId changes
  useEffect(() => {
    loadExistingImages();
  }, [questionId]);

  // Reset state when questionId changes to prevent stale data
  useEffect(() => {
    setImageUris([]);
    setStoredImages([]);
    setUploadStats({
      total: 0,
      uploaded: 0,
      pending: 0,
      totalSize: 0,
    });
  }, [questionId]);

  // Update stats when stored images change
  useEffect(() => {
    updateUploadStats();
  }, [storedImages]);

  const loadExistingImages = async () => {
    if (!questionId) {
      console.log("No questionId provided, skipping image loading");
      return;
    }

    try {
      console.log(`🔍 Loading existing images for question: ${questionId}`);
      const images = await ImageStorageManager.getQuestionImages(
        questionId,
        planId
      );

      console.log(
        `📸 Found ${images.length} existing images for question ${questionId}:`,
        images.map((img) => ({
          id: img.id,
          name: img.name,
          isUploaded: img.isUploaded,
          timestamp: img.timestamp,
        }))
      );

      setStoredImages(images);
      setImageUris(images.map((img) => img.uri));

      // Notify parent components about loaded images
      onImagesChange?.(images.map((img) => img.uri));
      onStoredImagesChange?.(images);

      console.log(
        `✅ Loaded ${images.length} existing images for question: ${questionId}`
      );
    } catch (error) {
      console.error(
        `❌ Error loading existing images for question ${questionId}:`,
        error
      );
      // Reset state on error
      setStoredImages([]);
      setImageUris([]);
      onImagesChange?.([]);
      onStoredImagesChange?.([]);
    }
  };

  const updateUploadStats = () => {
    const stats = {
      total: storedImages.length,
      uploaded: storedImages.filter((img) => img.isUploaded).length,
      pending: storedImages.filter((img) => !img.isUploaded).length,
      totalSize: storedImages.reduce((sum, img) => sum + (img.size || 0), 0),
    };
    setUploadStats(stats);
  };

  const handleImagesChange = (newImageUris: string[]) => {
    setImageUris(newImageUris);
    onImagesChange?.(newImageUris);
  };

  const handleImagesStored = (newStoredImages: StoredImage[]) => {
    // Always replace with the new list - PhotoPicker sends the complete current state
    setStoredImages(newStoredImages);
    onStoredImagesChange?.(newStoredImages);

    console.log(
      `Updated stored images for question ${questionId}:`,
      newStoredImages.length
    );
  };

  const handleClearAllImages = () => {
    Alert.alert(
      "Xóa tất cả ảnh",
      "Bạn có chắc chắn muốn xóa tất cả ảnh cho câu hỏi này?",
      [
        {
          text: "Hủy",
          style: "cancel",
        },
        {
          text: "Xóa",
          style: "destructive",
          onPress: async () => {
            try {
              await ImageStorageManager.clearQuestionImages(questionId, planId);
              setStoredImages([]);
              setImageUris([]);
              onImagesChange?.([]);
              onStoredImagesChange?.([]);

              console.log("All images cleared for question:", questionId);
            } catch (error) {
              console.error("Error clearing images:", error);
              Alert.alert("Lỗi", "Không thể xóa ảnh. Vui lòng thử lại.");
            }
          },
        },
      ]
    );
  };

  const getQuestionTypeIcon = () => {
    switch (questionType) {
      case "yes_no":
        return <CheckCircle size={16} color="#10B981" />;
      case "multi_choice":
        return <CheckCircle size={16} color="#3B82F6" />;
      case "text":
        return <CheckCircle size={16} color="#8B5CF6" />;
      case "photo":
        return <Camera size={16} color="#F59E0B" />;
      default:
        return <ImageIcon size={16} color="#6B7280" />;
    }
  };

  const getQuestionTypeLabel = () => {
    switch (questionType) {
      case "yes_no":
        return "Câu hỏi Có/Không";
      case "multi_choice":
        return "Câu hỏi Nhiều lựa chọn";
      case "text":
        return "Câu hỏi Văn bản";
      case "photo":
        return "Câu hỏi Ảnh";
      default:
        return "Câu hỏi";
    }
  };

  return (
    <View className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      {/* Question Header */}
      <View className="mb-4">
        <View className="flex-row items-center mb-2">
          {getQuestionTypeIcon()}
          <Text className="text-xs text-gray-500 ml-2 font-medium">
            {getQuestionTypeLabel()}
          </Text>
        </View>

        <Text className="text-sm font-medium text-gray-800 mb-2">
          {questionText}
        </Text>

        {/* Upload Stats */}
        {showUploadStatus && storedImages.length > 0 && (
          <View className="flex-row items-center justify-between p-2 bg-gray-50 rounded-lg">
            <View className="flex-row items-center">
              <Upload size={14} color="#6B7280" />
              <Text className="text-xs text-gray-600 ml-1">
                {uploadStats.uploaded}/{uploadStats.total} đã tải lên
              </Text>
            </View>

            <View className="flex-row items-center">
              <Text className="text-xs text-gray-500">
                {uploadStats.totalSize > 0
                  ? `${(uploadStats.totalSize / 1024 / 1024).toFixed(1)}MB`
                  : "0MB"}
              </Text>
            </View>
          </View>
        )}
      </View>

      {/* Image Upload Component */}
      <PhotoPicker
        value={imageUris}
        onChange={handleImagesChange}
        maxPhotos={imageLimit}
        placeholder={`Thêm ảnh cho câu hỏi này (tối đa ${imageLimit} ảnh)`}
        questionId={questionId}
        planId={planId}
        enableCompression={enableCompression}
        showPreview={true}
        onImagesStored={handleImagesStored}
      />

      {/* Additional Actions */}
      {storedImages.length > 0 && (
        <View className="mt-4 pt-4 border-t border-gray-100">
          <View className="flex-row justify-between items-center">
            <View>
              <Text className="text-xs text-gray-500">
                {uploadStats.pending > 0 && (
                  <Text className="text-orange-600">
                    {uploadStats.pending} ảnh chưa tải lên •
                  </Text>
                )}
                <Text className="text-green-600">
                  {uploadStats.uploaded} ảnh đã tải lên
                </Text>
              </Text>
            </View>

            {/* Clear All Button */}
            <Text
              className="text-xs text-red-500 font-medium"
              onPress={handleClearAllImages}
            >
              Xóa tất cả
            </Text>
          </View>
        </View>
      )}

      {/* Help Text */}
      {imageUris.length === 0 && (
        <View className="mt-2">
          <Text className="text-xs text-gray-400 text-center">
            {enableCompression
              ? "Ảnh sẽ được nén tự động định dạng WebP để tiết kiệm dung lượng"
              : "Thêm ảnh để minh họa cho câu trả lời của bạn"}
          </Text>
        </View>
      )}
    </View>
  );
}

// Export types for use in other components
export type { UniversalImageUploadProps };
