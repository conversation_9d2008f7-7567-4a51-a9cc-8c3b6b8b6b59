import React from "react";
import { View } from "react-native";
import { Text } from "~/components/ui/text";
import { Input } from "~/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { CheckboxItem } from "./CheckboxItem";

interface ChecklistItem {
  key: string;
  label: string;
  value: boolean | null;
}

interface ChecklistSectionProps {
  title: string;
  items: ChecklistItem[];
  note: string;
  noteLabel: string;
  onItemChange: (key: string, value: boolean) => void;
  onNoteChange: (note: string) => void;
}

export function ChecklistSection({
  title,
  items,
  note,
  noteLabel,
  onItemChange,
  onNoteChange,
}: ChecklistSectionProps) {
  return (
    <Card className="mb-4">
      <CardHeader>
        <CardTitle>{title} *</CardTitle>
      </CardHeader>
      <CardContent>
        {items.map((item) => (
          <CheckboxItem
            key={item.key}
            label={item.label}
            value={item.value}
            onValueChange={(value) => onItemChange(item.key, value)}
          />
        ))}

        <View className="mt-4">
          <Text className="text-sm font-medium mb-1">{noteLabel}</Text>
          <Input
            value={note}
            onChangeText={onNoteChange}
            placeholder="Nhập ghi chú..."
            multiline
            numberOfLines={3}
          />
        </View>
      </CardContent>
    </Card>
  );
}
