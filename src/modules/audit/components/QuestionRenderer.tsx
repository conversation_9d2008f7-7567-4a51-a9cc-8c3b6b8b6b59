import React from "react";
import { View, TextInput, Image } from "react-native";
import { Text } from "~/components/ui/text";
import { Button } from "~/components/ui/button";
import { YesNoQuestion } from "./YesNoQuestion";
import { MultiChoiceQuestion } from "./MultiChoiceQuestion";
import { PhotoPicker } from "~/src/components/ui/photo-picker";
import { FileUpload } from "~/src/components/ui/file-upload";
import { UniversalImageUpload } from "./UniversalImageUpload";
import { DateTimePicker } from "~/src/components/ui/DateTimePicker";
import { getImageLimit, QUESTION_IMAGE_LIMITS } from "~/src/types/constants";

interface Question {
  id: number;
  documentId?: string; // Strapi v5 documentId
  question_text: string;
  type: string;
  is_required: boolean;
  options?: any;
  question_image?: string | string[];
}

interface QuestionRendererProps {
  question: Question;
  value: any;
  onChange: (value: any) => void;
  disabled?: boolean;
  enableImageUpload?: boolean; // Enable image upload for all question types
  maxImages?: number; // Maximum number of images allowed
  planId?: string; // For form isolation
  onImagesChange?: (imageUris: string[]) => void; // Callback for image changes
  onStoredImagesChange?: (storedImages: any[]) => void; // Callback for stored images
}

export function QuestionRenderer({
  question,
  value,
  onChange,
  disabled = false,
  enableImageUpload = true,
  maxImages = 3,
  planId,
  onImagesChange,
  onStoredImagesChange,
}: QuestionRendererProps) {
  // Helper function to get photo value
  const getPhotoValue = () => {
    if (!value) return [];
    return value.photo_value || [];
  };

  // Helper function to get file value
  const getFileValue = () => {
    if (!value) return [];
    return value.file_value || [];
  };

  const photoValue = getPhotoValue();
  const fileValue = getFileValue();

  const renderQuestionText = () => (
    <Text className="text-foreground text-base leading-6 mb-4">
      {question.question_text}
    </Text>
  );

  switch (question.type) {
    case "yes_no":
      return (
        <YesNoQuestion
          question={question}
          value={value}
          onChange={onChange}
          disabled={disabled}
          enableImageUpload={enableImageUpload}
          maxImages={maxImages}
          onImagesChange={onImagesChange}
        />
      );

    case "single_choice":
    case "multi_choice":
      return (
        <MultiChoiceQuestion
          question={question}
          value={value}
          onChange={onChange}
          disabled={disabled}
          enableImageUpload={enableImageUpload}
          maxImages={maxImages}
          onImagesChange={onImagesChange}
        />
      );

    case "text":
      return (
        <View className="space-y-4">
          {/* Question Image */}
          {question.question_image && (
            <View className="mb-4">
              <Text className="text-sm text-gray-600 mb-2">
                Hình ảnh câu hỏi:
              </Text>
              <Image
                source={{
                  uri: Array.isArray(question.question_image)
                    ? question.question_image[0]
                    : question.question_image,
                }}
                className="w-full h-48 rounded-lg"
                resizeMode="cover"
              />
            </View>
          )}

          {renderQuestionText()}
          <TextInput
            value={value || ""}
            onChangeText={(text) => onChange(text)}
            placeholder="Nhập câu trả lời..."
            multiline
            numberOfLines={4}
            className="border border-gray-300 rounded-lg p-4 text-foreground bg-white min-h-24 text-base"
            textAlignVertical="top"
            editable={!disabled}
          />

          {/* Image Upload Section */}
          {enableImageUpload === true && (
            <UniversalImageUpload
              questionId={question.documentId || question.id.toString()}
              questionType="text"
              questionText={question.question_text}
              planId={planId}
              maxImages={maxImages}
              enableCompression={true}
              showUploadStatus={true}
              onImagesChange={onImagesChange}
            />
          )}
        </View>
      );

    case "number":
      const { min = 0, max = 100 } = question.options || {};
      return (
        <View className="space-y-4">
          {/* Question Image */}
          {question.question_image && (
            <View className="mb-4">
              <Text className="text-sm text-gray-600 mb-2">
                Hình ảnh câu hỏi:
              </Text>
              <Image
                source={{
                  uri: Array.isArray(question.question_image)
                    ? question.question_image[0]
                    : question.question_image,
                }}
                className="w-full h-48 rounded-lg"
                resizeMode="cover"
              />
            </View>
          )}

          {renderQuestionText()}
          <TextInput
            value={value?.toString() || ""}
            onChangeText={(text) => {
              const num = parseInt(text);
              if (!isNaN(num) && num >= min && num <= max) {
                onChange(num);
              } else if (text === "") {
                onChange(null);
              }
            }}
            placeholder={`Nhập số từ ${min} đến ${max}`}
            keyboardType="numeric"
            className="border border-gray-300 rounded-lg p-4 text-foreground bg-white text-base"
            editable={!disabled}
          />

          {/* Image Upload Section */}
          {enableImageUpload === true && (
            <UniversalImageUpload
              questionId={question.documentId || question.id.toString()}
              questionType="number"
              questionText={question.question_text}
              maxImages={maxImages}
              enableCompression={true}
              showUploadStatus={true}
              onImagesChange={onImagesChange}
            />
          )}
        </View>
      );

    case "rating":
      const { min: ratingMin = 1, max: ratingMax = 5 } = question.options || {};
      return (
        <View className="space-y-4">
          {/* Question Image */}
          {question.question_image && (
            <View className="mb-4">
              <Text className="text-sm text-gray-600 mb-2">
                Hình ảnh câu hỏi:
              </Text>
              <Image
                source={{
                  uri: Array.isArray(question.question_image)
                    ? question.question_image[0]
                    : question.question_image,
                }}
                className="w-full h-48 rounded-lg"
                resizeMode="cover"
              />
            </View>
          )}

          {renderQuestionText()}
          <View className="flex-row flex-wrap gap-3">
            {Array.from(
              { length: ratingMax - ratingMin + 1 },
              (_, i) => ratingMin + i
            ).map((ratingValue) => (
              <Button
                key={ratingValue}
                variant={value === ratingValue ? "default" : "outline"}
                onPress={() => onChange(ratingValue)}
                className="min-w-12 py-3"
                disabled={disabled}
              >
                <Text
                  className={
                    value === ratingValue
                      ? "text-white font-medium"
                      : "text-foreground font-medium"
                  }
                >
                  {ratingValue}
                </Text>
              </Button>
            ))}
          </View>

          {/* Enhanced Photo Upload with Compression */}
          {enableImageUpload === true && (
            <UniversalImageUpload
              questionId={question.documentId || question.id.toString()}
              questionType="rating"
              questionText={question.question_text}
              planId={planId}
              maxImages={maxImages}
              enableCompression={true}
              showUploadStatus={true}
              onImagesChange={onImagesChange}
            />
          )}
        </View>
      );

    case "photo":
      return (
        <View className="space-y-4">
          {/* Question Image */}
          {question.question_image && (
            <View className="mb-4">
              <Text className="text-sm text-gray-600 mb-2">
                Hình ảnh câu hỏi:
              </Text>
              <Image
                source={{
                  uri: Array.isArray(question.question_image)
                    ? question.question_image[0]
                    : question.question_image,
                }}
                className="w-full h-48 rounded-lg"
                resizeMode="cover"
              />
            </View>
          )}

          {renderQuestionText()}

          {/* Enhanced Photo Upload with Compression */}
          <UniversalImageUpload
            questionId={question.documentId || question.id.toString()}
            questionType="photo"
            questionText={question.question_text}
            planId={planId}
            maxImages={
              maxImages ||
              getImageLimit(
                question.type as keyof typeof QUESTION_IMAGE_LIMITS,
                "question_answer"
              )
            } // Dynamic limit based on question type
            enableCompression={true}
            showUploadStatus={true}
            onImagesChange={onImagesChange}
            onStoredImagesChange={onStoredImagesChange}
          />
        </View>
      );

    case "date_time":
      return (
        <View className="space-y-4">
          {/* Question Image */}
          {question.question_image && (
            <View className="mb-4">
              <Text className="text-sm text-gray-600 mb-2">
                Hình ảnh câu hỏi:
              </Text>
              <Image
                source={{
                  uri: Array.isArray(question.question_image)
                    ? question.question_image[0]
                    : question.question_image,
                }}
                className="w-full h-48 rounded-lg"
                resizeMode="cover"
              />
            </View>
          )}

          {renderQuestionText()}
          <DateTimePicker
            date={value ? new Date(value) : undefined}
            setDate={(date) => onChange(date ? date.toISOString() : null)}
            disabled={disabled}
            defaultToToday={true}
          />

          {/* Image Upload Section */}
          {enableImageUpload === true && (
            <UniversalImageUpload
              questionId={question.documentId || question.id.toString()}
              questionType="date_time"
              questionText={question.question_text}
              planId={planId}
              maxImages={maxImages}
              enableCompression={true}
              showUploadStatus={true}
              onImagesChange={onImagesChange}
            />
          )}
        </View>
      );

    default:
      return (
        <View className="space-y-4">
          {/* Question Image */}
          {question.question_image && (
            <View className="mb-4">
              <Text className="text-sm text-gray-600 mb-2">
                Hình ảnh câu hỏi:
              </Text>
              <Image
                source={{
                  uri: Array.isArray(question.question_image)
                    ? question.question_image[0]
                    : question.question_image,
                }}
                className="w-full h-48 rounded-lg"
                resizeMode="cover"
              />
            </View>
          )}

          {renderQuestionText()}
          <Text className="text-gray-500">
            Loại câu hỏi không được hỗ trợ: {question.type}
          </Text>
        </View>
      );
  }
}
