import React from "react";
import { View, Pressable } from "react-native";
import { Text } from "~/components/ui/text";

interface CheckboxItemProps {
  label: string;
  value: boolean | null;
  onValueChange: (value: boolean) => void;
}

export function CheckboxItem({
  label,
  value,
  onValueChange,
}: CheckboxItemProps) {
  return (
    <View className="flex-row items-center justify-between py-2 border-b border-gray-200">
      <Text className="flex-1 text-sm">{label}</Text>
      <View className="flex-row space-x-4 ">
        <Pressable
          onPress={() => onValueChange(true)}
          className={`px-3 mr-2 py-1 rounded ${
            value === true ? "bg-green-500" : "bg-gray-200"
          }`}
        >
          <Text
            className={`text-sm ${
              value === true ? "text-white" : "text-gray-600"
            }`}
          >
            Yes
          </Text>
        </Pressable>
        <Pressable
          onPress={() => onValueChange(false)}
          className={`px-3  py-1 rounded ${
            value === false ? "bg-red-500" : "bg-gray-200"
          }`}
        >
          <Text
            className={`text-sm ${
              value === false ? "text-white" : "text-gray-600"
            }`}
          >
            No
          </Text>
        </Pressable>
      </View>
    </View>
  );
}
