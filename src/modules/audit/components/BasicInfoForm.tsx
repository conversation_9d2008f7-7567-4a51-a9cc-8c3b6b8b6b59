import React from "react";
import { View } from "react-native";
import { Text } from "~/components/ui/text";
import { Input } from "~/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { SearchableSelect } from "./SearchableSelect";
import { mockStoreCodes } from "~/src/mockdata";

interface BasicInfoData {
  storeCode: string;
  staffName: string;
}

interface BasicInfoFormProps {
  data: BasicInfoData;
  onDataChange: (data: BasicInfoData) => void;
}

export function BasicInfoForm({ data, onDataChange }: BasicInfoFormProps) {
  const updateField = (field: keyof BasicInfoData, value: string) => {
    onDataChange({ ...data, [field]: value });
  };

  return (
    <Card className="mb-4">
      <CardHeader>
        <CardTitle>Thông tin cơ bản</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <View className="w-full pb-3">
          <Text className="text-sm font-medium mb-1">M<PERSON> cửa hàng *</Text>
          <SearchableSelect
            value={data.storeCode}
            onValueChange={(value) => updateField("storeCode", value)}
            placeholder="Chọn mã cửa hàng"
            searchPlaceholder="Tìm kiếm mã cửa hàng..."
            options={mockStoreCodes}
          />
        </View>

        <View>
          <Text className="text-sm font-medium mb-1">
            Tên nhân sự thực hiện *
          </Text>
          <Input
            value={data.staffName}
            onChangeText={(text) => updateField("staffName", text)}
            placeholder="Nhập tên nhân sự"
          />
        </View>
      </CardContent>
    </Card>
  );
}
