import React from "react";
import { View, Image, TouchableOpacity, Alert } from "react-native";
import { Text } from "~/components/ui/text";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { useFileUpload, LocalFile } from "~/src/hooks/useFileUpload";
import { Ionicons } from "@expo/vector-icons";
import { ImagePreview } from "~/src/components/ui/image-preview";

interface ImageUploadSectionProps {
  title: string;
  subtitle?: string;
  maxFiles: number;
  maxSize: number; // in MB
  minFiles: number;
  onImagesChange?: (uploadedFiles: any[]) => void;
  onLocalImagesChange?: (localFiles: LocalFile[]) => void;
}

export function ImageUploadSection({
  title,
  subtitle,
  maxFiles,
  maxSize,
  minFiles,
  onImagesChange,
  onLocalImagesChange,
}: ImageUploadSectionProps) {
  const {
    localFiles,
    uploadedFiles,
    isUploading,
    pickImages,
    takePhoto,
    removeLocalFile,
    uploadFiles,
    clearAll,
  } = useFileUpload();

  const [showPreview, setShowPreview] = React.useState(false);
  const [previewImages, setPreviewImages] = React.useState<string[]>([]);
  const [previewIndex, setPreviewIndex] = React.useState(0);

  // Notify parent component when files change
  React.useEffect(() => {
    onLocalImagesChange?.(localFiles);
  }, [localFiles, onLocalImagesChange]);

  React.useEffect(() => {
    onImagesChange?.(uploadedFiles);
  }, [uploadedFiles, onImagesChange]);

  const handlePickImages = () => {
    const remainingSlots = maxFiles - localFiles.length;
    if (remainingSlots <= 0) {
      Alert.alert("Giới hạn", `Bạn chỉ có thể chọn tối đa ${maxFiles} ảnh.`);
      return;
    }
    pickImages(remainingSlots);
  };

  const handleTakePhoto = () => {
    if (localFiles.length >= maxFiles) {
      Alert.alert("Giới hạn", `Bạn chỉ có thể chọn tối đa ${maxFiles} ảnh.`);
      return;
    }
    takePhoto();
  };

  const handleRemoveFile = (fileId: string) => {
    removeLocalFile(fileId);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const renderImagePreview = (file: LocalFile, index: number) => (
    <TouchableOpacity
      key={file.id}
      onPress={() => {
        const allImages = [...localFiles.map(f => f.uri), ...uploadedFiles.map(f => f.url)];
        setPreviewImages(allImages);
        setPreviewIndex(index);
        setShowPreview(true);
      }}
      className="relative mr-2 mb-2"
    >
      <Image
        source={{ uri: file.uri }}
        className="w-20 h-20 rounded-lg"
        resizeMode="cover"
      />
      <TouchableOpacity
        onPress={(e) => {
          e.stopPropagation();
          handleRemoveFile(file.id);
        }}
        className="absolute -top-2 -right-2 bg-red-500 rounded-full w-6 h-6 items-center justify-center"
      >
        <Ionicons name="close" size={14} color="white" />
      </TouchableOpacity>
      <View className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 rounded-b-lg">
        <Text className="text-white text-xs px-1 text-center">
          {formatFileSize(file.size)}
        </Text>
      </View>
    </TouchableOpacity>
  );

  const renderUploadedImage = (file: any, index: number) => (
    <TouchableOpacity
      key={file.id}
      onPress={() => {
        const allImages = [...localFiles.map(f => f.uri), ...uploadedFiles.map(f => f.url)];
        setPreviewImages(allImages);
        setPreviewIndex(localFiles.length + index);
        setShowPreview(true);
      }}
      className="relative mr-2 mb-2"
    >
      <Image
        source={{ uri: file.url }}
        className="w-20 h-20 rounded-lg"
        resizeMode="cover"
      />
      <View className="absolute top-0 left-0 bg-green-500 rounded-tl-lg px-1">
        <Ionicons name="checkmark" size={12} color="white" />
      </View>
    </TouchableOpacity>
  );

  return (
    <Card className="mb-4">
      <CardHeader>
        <CardTitle className="text-lg">{title} *</CardTitle>
        {subtitle && <Text className="text-sm text-gray-600">{subtitle}</Text>}
        <Text className="text-xs text-gray-500">
          Tối thiểu {minFiles} ảnh, tối đa {maxFiles} ảnh. Kích thước tối đa{" "}
          {maxSize}MB mỗi ảnh.
        </Text>
        <Text className="text-xs text-gray-500">
          Đã chọn: {localFiles.length + uploadedFiles.length}/{maxFiles} ảnh
        </Text>
      </CardHeader>
      <CardContent>
        {/* Image Previews */}
        <View className="flex-row flex-wrap mb-3">
          {/* Show uploaded files first */}
          {uploadedFiles.map((file, index) => renderUploadedImage(file, index))}
          
          {/* Show local files */}
          {localFiles.map((file, index) => renderImagePreview(file, index))}
          
          {/* Empty slots */}
          {Array.from({ length: maxFiles - localFiles.length - uploadedFiles.length }).map((_, index) => (
            <View
              key={`empty-${index}`}
              className="w-20 h-20 bg-gray-200 rounded-lg mr-2 mb-2 items-center justify-center"
            >
              <Ionicons name="add" size={24} color="#9CA3AF" />
            </View>
          ))}
        </View>

        {/* Action Buttons */}
        <View className="flex-row space-x-2">
          <Button
            onPress={handlePickImages}
            variant="outline"
            disabled={localFiles.length + uploadedFiles.length >= maxFiles || isUploading}
            className="flex-1"
          >
            <Ionicons name="images" size={16} className="mr-2" />
            <Text>Chọn ảnh</Text>
          </Button>
          
          <Button
            onPress={handleTakePhoto}
            variant="outline"
            disabled={localFiles.length + uploadedFiles.length >= maxFiles || isUploading}
            className="flex-1"
          >
            <Ionicons name="camera" size={16} className="mr-2" />
            <Text>Chụp ảnh</Text>
          </Button>
        </View>

        {/* Upload Status */}
        {isUploading && (
          <View className="mt-3 p-3 bg-blue-100 rounded-lg">
            <Text className="text-blue-800 text-center">
              ⏳ Đang upload ảnh...
            </Text>
          </View>
        )}

        {/* Validation Message */}
        {localFiles.length + uploadedFiles.length < minFiles && (
          <Text className="text-red-500 text-sm mt-2">
            Cần ít nhất {minFiles} ảnh để tiếp tục
          </Text>
        )}
      </CardContent>

      {/* Image Preview Modal */}
      <ImagePreview
        visible={showPreview}
        onClose={() => setShowPreview(false)}
        images={previewImages}
        initialIndex={previewIndex}
      />
    </Card>
  );
}
