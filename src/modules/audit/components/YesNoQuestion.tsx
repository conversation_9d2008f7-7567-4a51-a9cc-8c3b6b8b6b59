import React from "react";
import { View } from "react-native";
import { Text } from "~/components/ui/text";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { UniversalImageUpload } from "./UniversalImageUpload";

interface YesNoQuestionProps {
  question: {
    id: number;
    documentId?: string; // Strapi v5 documentId
    question_text: string;
    type: string;
    is_required: boolean;
    options?: any;
  };
  value: any;
  onChange: (value: any) => void;
  disabled?: boolean;
  enableImageUpload?: boolean; // Enable image upload for this question
  maxImages?: number; // Maximum number of images allowed
  planId?: string; // For form isolation
  onImagesChange?: (imageUris: string[]) => void; // Callback for image changes
}

/**
 * YesNoQuestion component for handling hierarchical yes/no questions.
 *
 * Key features:
 * - Handles both array and object formats in question options
 * - Cleans up existing values to remove numeric keys from array indices
 * - Always returns complete data structure with all question items
 * - Distinguishes between "No" answers (false) and unanswered questions (null)
 *
 * Data structure format:
 * {
 *   "Category": {
 *     "Subcategory": {
 *       "Item1": true,   // Yes answer
 *       "Item2": false,  // No answer
 *       "Item3": null    // Unanswered
 *     }
 *   }
 * }
 */
export function YesNoQuestion({
  question,
  value,
  onChange,
  disabled = false,
  enableImageUpload = true,
  maxImages = 3,
  planId,
  onImagesChange,
}: YesNoQuestionProps) {
  // Parse options structure
  const options = question.options || {};

  // Clean up existing value by removing numeric keys and ensuring proper structure
  const cleanupValue = (existingValue: any) => {
    if (!existingValue || typeof existingValue !== "object") return null;

    const cleanedValue: any = {};
    Object.keys(options).forEach((category) => {
      cleanedValue[category] = {};
      Object.keys(options[category]).forEach((subcategory) => {
        cleanedValue[category][subcategory] = {};

        // Get the expected items from options
        const subcategoryData = options[category][subcategory];
        const expectedItems = Array.isArray(subcategoryData)
          ? subcategoryData
          : Object.keys(subcategoryData || {});

        // Only copy non-numeric keys that match expected items
        const existingSubcategory = existingValue[category]?.[subcategory];
        if (existingSubcategory && typeof existingSubcategory === "object") {
          expectedItems.forEach((item) => {
            if (existingSubcategory.hasOwnProperty(item)) {
              cleanedValue[category][subcategory][item] =
                existingSubcategory[item];
            } else {
              cleanedValue[category][subcategory][item] = null;
            }
          });
        } else {
          // Initialize with null values for expected items
          expectedItems.forEach((item) => {
            cleanedValue[category][subcategory][item] = null;
          });
        }
      });
    });
    return cleanedValue;
  };

  // Initialize value structure if not exists
  const initializeValue = () => {
    if (value) {
      // Clean up existing value to remove numeric keys
      return cleanupValue(value);
    }

    const newValue: any = {};
    Object.keys(options).forEach((category) => {
      newValue[category] = {};
      Object.keys(options[category]).forEach((subcategory) => {
        newValue[category][subcategory] = {};

        // Handle both array and object formats for items
        // This prevents creating numeric keys ("0", "1", "2", etc.) when arrays are used
        const subcategoryData = options[category][subcategory];
        if (Array.isArray(subcategoryData)) {
          // If it's an array, use the array items as keys
          subcategoryData.forEach((item) => {
            newValue[category][subcategory][item] = null;
          });
        } else if (
          typeof subcategoryData === "object" &&
          subcategoryData !== null
        ) {
          // If it's an object, use the object keys as items
          Object.keys(subcategoryData).forEach((item) => {
            newValue[category][subcategory][item] = null;
          });
        }
      });
    });
    return newValue;
  };

  const currentValue = initializeValue();

  // Ensure complete structure is always maintained
  const ensureCompleteStructure = (valueToCheck: any) => {
    const completeValue: any = {};
    Object.keys(options).forEach((category) => {
      completeValue[category] = {};
      Object.keys(options[category]).forEach((subcategory) => {
        completeValue[category][subcategory] = {};

        // Get the expected items from options
        const subcategoryData = options[category][subcategory];
        const expectedItems = Array.isArray(subcategoryData)
          ? subcategoryData
          : Object.keys(subcategoryData || {});

        // Ensure all expected items are present
        expectedItems.forEach((item) => {
          const existingValue = valueToCheck?.[category]?.[subcategory]?.[item];
          completeValue[category][subcategory][item] =
            existingValue !== undefined ? existingValue : null;
        });
      });
    });
    return completeValue;
  };

  const handleItemChange = (
    category: string,
    subcategory: string,
    item: string,
    answer: boolean
  ) => {
    const newValue = { ...currentValue };
    if (!newValue[category]) {
      newValue[category] = {};
    }
    if (!newValue[category][subcategory]) {
      newValue[category][subcategory] = {};
    }
    newValue[category][subcategory][item] = answer;

    // Always ensure complete structure with all items (answered and unanswered)
    // This ensures we have a complete audit trail and can distinguish between
    // "No" answers (false) and unanswered questions (null)
    const completeStructure = ensureCompleteStructure(newValue);
    onChange(completeStructure);
  };

  const renderItem = (category: string, subcategory: string, item: string) => {
    const currentAnswer = currentValue[category]?.[subcategory]?.[item];

    return (
      <View key={`${category}-${subcategory}-${item}`} className="mb-4">
        <Card>
          <CardContent className="p-4">
            {/* Item Question */}
            <Text className="font-medium text-base text-foreground mb-3">
              {item}
            </Text>

            {/* Yes/No Buttons */}
            <View className="flex-row space-x-3">
              <Button
                variant={currentAnswer === true ? "default" : "outline"}
                onPress={() =>
                  handleItemChange(category, subcategory, item, true)
                }
                className={`flex-1 py-2 ${
                  currentAnswer === true ? "bg-green-600 border-green-600" : ""
                }`}
                disabled={disabled}
              >
                <Text
                  className={
                    currentAnswer === true
                      ? "text-white font-medium"
                      : "text-foreground font-medium"
                  }
                >
                  Có
                </Text>
              </Button>
              <Button
                variant={currentAnswer === false ? "default" : "outline"}
                onPress={() =>
                  handleItemChange(category, subcategory, item, false)
                }
                className={`flex-1 py-2 ${
                  currentAnswer === false ? "bg-red-600 border-red-600" : ""
                }`}
                disabled={disabled}
              >
                <Text
                  className={
                    currentAnswer === false
                      ? "text-white font-medium"
                      : "text-foreground font-medium"
                  }
                >
                  Không
                </Text>
              </Button>
            </View>
          </CardContent>
        </Card>
      </View>
    );
  };

  const renderSubcategory = (
    category: string,
    subcategory: string,
    items: string[] | Record<string, any>
  ) => {
    // Convert items to array format for consistent rendering
    const itemsArray = Array.isArray(items) ? items : Object.keys(items);

    return (
      <View key={`${category}-${subcategory}`} className="mb-6">
        {/* Subcategory Title */}
        <Text className="font-semibold text-lg text-foreground mb-4">
          {subcategory}
        </Text>

        {/* Items as individual questions */}
        {itemsArray.map((item) => renderItem(category, subcategory, item))}
      </View>
    );
  };

  const renderCategory = (category: string, subcategories: any) => {
    return (
      <View key={category} className="mb-6">
        {/* Category Title */}
        <Text className="text-lg font-bold text-foreground mb-4">
          {category}
        </Text>

        {/* Subcategories */}
        {Object.entries(subcategories).map(([subcategory, items]) =>
          renderSubcategory(
            category,
            subcategory,
            items as string[] | Record<string, any>
          )
        )}
      </View>
    );
  };

  return (
    <View className="space-y-4">
      {/* Question Text */}
      <Text className="text-foreground text-base leading-6 mb-4">
        {question.question_text}
      </Text>

      {/* Categories and Subcategories */}
      {Object.entries(options).map(([category, subcategories]) =>
        renderCategory(category, subcategories as any)
      )}

      {/* Image Upload Section */}
      {enableImageUpload && (
        <UniversalImageUpload
          questionId={question.documentId || question.id.toString()}
          questionType="yes_no"
          questionText={question.question_text}
          planId={planId}
          maxImages={maxImages}
          enableCompression={true}
          showUploadStatus={true}
          onImagesChange={onImagesChange}
        />
      )}
    </View>
  );
}
