// Common types for audit components
export interface ImageUpload {
  uri: string;
  name: string;
  size: number;
}

export interface CheckboxItemProps {
  label: string;
  value: boolean | null;
  onValueChange: (value: boolean) => void;
}

export interface ImageUploadSectionProps {
  title: string;
  subtitle?: string;
  images: ImageUpload[];
  maxFiles: number;
  maxSize: number;
  minFiles: number;
  onAddImage: () => void;
}

export interface AuditHeaderProps {
  title?: string;
  subtitle?: string;
  onBack?: () => void;
  onProfilePress?: () => void;
  onLogoutPress?: () => void;
}

export interface BasicInfoData {
  storeCode: string;
  staffName: string;
}

export interface BasicInfoFormProps {
  data: BasicInfoData;
  onDataChange: (data: BasicInfoData) => void;
}

export interface ChecklistItem {
  key: string;
  label: string;
  value: boolean | null;
}

export interface ChecklistSectionProps {
  title: string;
  items: ChecklistItem[];
  note: string;
  noteLabel: string;
  onItemChange: (key: string, value: boolean) => void;
  onNoteChange: (note: string) => void;
}

export interface SearchableSelectProps {
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  options: Array<{ value: string; label: string }>;
  searchPlaceholder?: string;
}

export interface StoreCodeOption {
  value: string;
  label: string;
}
