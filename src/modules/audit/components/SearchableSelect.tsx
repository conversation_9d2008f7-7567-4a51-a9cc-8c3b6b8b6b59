import React, { useState, useMemo } from "react";
import { View, ScrollView } from "react-native";
import { Text } from "~/components/ui/text";
import { Input } from "~/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  type Option,
} from "~/components/ui/select";

interface SearchableSelectProps {
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  options: Array<{ value: string; label: string }>;
  searchPlaceholder?: string;
}

export function SearchableSelect({
  value,
  onValueChange,
  placeholder = "Chọn...",
  options,
  searchPlaceholder = "Tìm kiếm...",
}: SearchableSelectProps) {
  const [search, setSearch] = useState("");

  // Filter options based on search
  const filteredOptions = useMemo(() => {
    if (!search) return options;
    return options.filter(
      (option) =>
        option.label.toLowerCase().includes(search.toLowerCase()) ||
        option.value.toLowerCase().includes(search.toLowerCase())
    );
  }, [options, search]);

  const selectedOption = options.find((option) => option.value === value);

  return (
    <View>
      <Select
        value={value ? { value, label: value } : undefined}
        onValueChange={(option: Option | undefined) => {
          if (option?.value) {
            onValueChange(option.value);
          }
        }}
        onOpenChange={(open) => {
          if (!open) {
            setSearch(""); // Reset search when closed
          }
        }}
      >
        <SelectTrigger className="w-full">
          <SelectValue
            className="text-foreground text-sm"
            placeholder={placeholder}
          />
        </SelectTrigger>
        <SelectContent className="w-80 max-w-[90vw] max-h-96">
          {/* Search Input */}
          <View className="p-2 border-b border-border">
            <Input
              value={search}
              onChangeText={setSearch}
              placeholder={searchPlaceholder}
              className="h-8 text-sm"
              autoFocus={false}
            />
          </View>

          {/* Scrollable Options */}
          <ScrollView className="max-h-64" showsVerticalScrollIndicator={true}>
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option) => (
                <SelectItem
                  key={option.value}
                  label={option.value}
                  value={option.value}
                  className="px-2 py-2"
                >
                  <View className="flex-1 min-w-0">
                    <Text className="font-medium text-sm" numberOfLines={1}>
                      {option.value}
                    </Text>
                    <Text
                      className="text-xs text-muted-foreground"
                      numberOfLines={1}
                    >
                      {option.label}
                    </Text>
                  </View>
                </SelectItem>
              ))
            ) : (
              <View className="p-4">
                <Text className="text-center text-muted-foreground text-sm">
                  Không tìm thấy kết quả
                </Text>
              </View>
            )}
          </ScrollView>
        </SelectContent>
      </Select>
    </View>
  );
}

// Import mock data from centralized location
import { mockStoreCodes } from "~/src/mockdata";
