import React from "react";
import { render } from "@testing-library/react-native";
import { YesNoQuestion } from "../YesNoQuestion";

// Mock the UI components
jest.mock("~/components/ui/text", () => ({
  Text: ({ children, ...props }: any) => <text {...props}>{children}</text>,
}));

jest.mock("~/components/ui/button", () => ({
  Button: ({ children, onPress, ...props }: any) => (
    <button onClick={onPress} {...props}>
      {children}
    </button>
  ),
}));

jest.mock("~/components/ui/card", () => ({
  Card: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  CardContent: ({ children, ...props }: any) => (
    <div {...props}>{children}</div>
  ),
}));

describe("YesNoQuestion", () => {
  const mockOnChange = jest.fn();

  beforeEach(() => {
    mockOnChange.mockClear();
  });

  it("should handle array-based options structure without creating numeric keys", () => {
    const question = {
      id: 1,
      question_text: "Test question",
      type: "yes_no",
      is_required: true,
      options: {
        Oppo: {
          "Lỗi POSM": ["POSM"],
          "Lỗi trưng bày": [
            "Vệ sinh",
            "Vật dụng thừa",
            "Form cấu hình đầy đủ",
            "Demo hoạt động: pin, alarm, wifi,...",
            "Trưng bày đúng số lượng demo theo quy định",
          ],
          "Bảng giá điện tử": ["BGĐT đúng loại, vị trí"],
        },
      },
    };

    const { getByText } = render(
      <YesNoQuestion question={question} value={null} onChange={mockOnChange} />
    );

    // Verify that the component renders the items from arrays
    expect(getByText("POSM")).toBeTruthy();
    expect(getByText("Vệ sinh")).toBeTruthy();
    expect(getByText("Vật dụng thừa")).toBeTruthy();
    expect(getByText("BGĐT đúng loại, vị trí")).toBeTruthy();
  });

  it("should handle object-based options structure", () => {
    const question = {
      id: 1,
      question_text: "Test question",
      type: "yes_no",
      is_required: true,
      options: {
        Category1: {
          Subcategory1: {
            Item1: "Item 1 description",
            Item2: "Item 2 description",
          },
        },
      },
    };

    const { getByText } = render(
      <YesNoQuestion question={question} value={null} onChange={mockOnChange} />
    );

    // Verify that the component renders the items from object keys
    expect(getByText("Item1")).toBeTruthy();
    expect(getByText("Item2")).toBeTruthy();
  });

  it("should initialize value structure correctly for array-based options", () => {
    const question = {
      id: 1,
      question_text: "Test question",
      type: "yes_no",
      is_required: true,
      options: {
        Oppo: {
          "Lỗi POSM": ["POSM"],
          "Lỗi trưng bày": ["Vệ sinh", "Vật dụng thừa"],
        },
      },
    };

    render(
      <YesNoQuestion question={question} value={null} onChange={mockOnChange} />
    );

    // The component should initialize without creating numeric keys
    // This is tested implicitly by ensuring the component renders without errors
    expect(mockOnChange).not.toHaveBeenCalled();
  });

  it("should clean up existing values with numeric keys", () => {
    const question = {
      id: 1,
      question_text: "Test question",
      type: "yes_no",
      is_required: true,
      options: {
        Samsung: {
          "Lỗi POSM": ["POSM"],
          "Lỗi trưng bày": [
            "Vệ sinh",
            "Vật dụng thừa",
            "Trưng bày đúng số lượng demo theo quy định",
          ],
        },
      },
    };

    // Existing value with numeric keys (the problematic format)
    const existingValue = {
      Samsung: {
        "Lỗi POSM": {
          "0": null,
          POSM: false,
        },
        "Lỗi trưng bày": {
          "0": null,
          "1": null,
          "2": null,
          "Trưng bày đúng số lượng demo theo quy định": true,
        },
      },
    };

    const { getByText } = render(
      <YesNoQuestion
        question={question}
        value={existingValue}
        onChange={mockOnChange}
      />
    );

    // Verify that the component renders the expected items
    expect(getByText("POSM")).toBeTruthy();
    expect(getByText("Vệ sinh")).toBeTruthy();
    expect(getByText("Vật dụng thừa")).toBeTruthy();
    expect(
      getByText("Trưng bày đúng số lượng demo theo quy định")
    ).toBeTruthy();

    // The component should not crash and should clean up the numeric keys internally
    expect(mockOnChange).not.toHaveBeenCalled();
  });

  it("should always return complete structure with all items when an answer is changed", () => {
    const question = {
      id: 1,
      question_text: "Test question",
      type: "yes_no",
      is_required: true,
      options: {
        Samsung: {
          "Lỗi POSM": ["POSM"],
          "Lỗi trưng bày": ["Vệ sinh", "Vật dụng thừa", "Form cấu hình đầy đủ"],
        },
      },
    };

    const { getByText } = render(
      <YesNoQuestion question={question} value={null} onChange={mockOnChange} />
    );

    // Find and click the "Có" (Yes) button for POSM
    const posmYesButton = getByText("Có");
    posmYesButton.props.onPress();

    // Verify that onChange was called with complete structure
    expect(mockOnChange).toHaveBeenCalledWith({
      Samsung: {
        "Lỗi POSM": {
          POSM: true,
        },
        "Lỗi trưng bày": {
          "Vệ sinh": null,
          "Vật dụng thừa": null,
          "Form cấu hình đầy đủ": null,
        },
      },
    });
  });
});
