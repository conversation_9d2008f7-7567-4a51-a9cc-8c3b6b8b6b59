import React, { useState } from "react";
import { View, TouchableOpacity } from "react-native";
import { router } from "expo-router";
import { Text } from "~/components/ui/text";
import { ChevronLeft } from "lucide-react-native";

interface AuditHeaderProps {
  title?: string;
  subtitle?: string;
  onBack?: () => void;
  onProfilePress?: () => void;
  onLogoutPress?: () => void;
}

function AvatarDropdown({
  onLogoutPress,
  onProfilePress,
}: {
  onLogoutPress: () => void;
  onProfilePress: () => void;
}) {
  const [showDropdown, setShowDropdown] = useState(false);

  return (
    <View className="relative">
      <TouchableOpacity
        onPress={() => setShowDropdown(!showDropdown)}
        className="w-10 h-10 bg-blue-100 rounded-full items-center justify-center border border-blue-200"
      >
        <Text className="text-blue-600 text-sm font-bold">U</Text>
      </TouchableOpacity>

      {showDropdown && (
        <View className="absolute top-12 right-0 bg-white rounded-lg border border-gray-200 shadow-lg min-w-[150px] z-10">
          <TouchableOpacity
            onPress={() => {
              setShowDropdown(false);
              onProfilePress();
            }}
            className="px-4 py-3 border-b border-gray-100"
          >
            <Text className="text-gray-700 font-medium">Hồ sơ</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              setShowDropdown(false);
              onLogoutPress();
            }}
            className="px-4 py-3"
          >
            <Text className="text-red-600 font-medium">Đăng xuất</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}

export function AuditHeader({
  title = "Audit Chi tiết",
  subtitle = "Hệ thống kiểm toán Cellphones",
  onBack,
  onProfilePress = () => {
    // Handle profile press
    // TODO: Implement profile navigation
  },
  onLogoutPress = () => router.replace("/"),
}: AuditHeaderProps) {
  return (
    <View className="bg-white px-6 py-2 border-b border-gray-200">
      <View className="flex-row justify-between items-center">
        <View className="flex-1">
          <View className="flex-row items-center">
            {onBack && (
              <TouchableOpacity onPress={onBack} className="mr-3">
                <ChevronLeft className="w-4 h-4" />
              </TouchableOpacity>
            )}
            <View>
              <Text className="text-xl font-bold text-gray-900">{title}</Text>
              {subtitle && (
                <Text className="text-sm text-gray-500 ">{subtitle}</Text>
              )}
            </View>
          </View>
        </View>
        <AvatarDropdown
          onLogoutPress={onLogoutPress}
          onProfilePress={onProfilePress}
        />
      </View>
    </View>
  );
}
