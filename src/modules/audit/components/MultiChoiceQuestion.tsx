import React from "react";
import { View } from "react-native";
import { Text } from "~/components/ui/text";
import { Button } from "~/components/ui/button";
import { UniversalImageUpload } from "./UniversalImageUpload";
import { Card, CardContent } from "~/components/ui/card";
import { Check } from "lucide-react-native";

interface MultiChoiceQuestionProps {
  question: {
    id: number;
    documentId?: string; // Strapi v5 documentId
    question_text: string;
    type: string;
    is_required: boolean;
    options?: {
      answers?: string[];
      answer_per_point?: number;
      choices?: Array<{
        id: string | number;
        label: string;
      }>;
      multiple?: boolean;
    };
  };
  value: string[] | string | null;
  onChange: (value: string[] | string) => void;
  disabled?: boolean;
  enableImageUpload?: boolean; // Enable image upload for this question
  maxImages?: number; // Maximum number of images allowed
  planId?: string; // For form isolation
  onImagesChange?: (imageUris: string[]) => void; // Callback for image changes
}

export function MultiChoiceQuestion({
  question,
  value,
  onChange,
  disabled = false,
  enableImageUpload = true,
  maxImages = 3,
  planId,
  onImagesChange,
}: MultiChoiceQuestionProps) {
  // Get choices from different possible structures
  const getChoices = () => {
    if (!question.options) return [];

    if (question.options.choices) {
      return question.options.choices;
    }
    if (question.options.answers && Array.isArray(question.options.answers)) {
      return question.options.answers.map((answer, index) => ({
        id: index.toString(),
        label: answer,
      }));
    }
    return [];
  };

  const choices = getChoices();
  const isMultiple = question.type === "multi_choice";
  const currentValue = isMultiple
    ? (value as string[]) || []
    : (value as string);

  const handleChoicePress = (choiceId: string) => {
    if (isMultiple) {
      const currentArray = currentValue as string[];
      const isSelected = currentArray.includes(choiceId);
      const newArray = isSelected
        ? currentArray.filter((id) => id !== choiceId)
        : [...currentArray, choiceId];
      onChange(newArray);
    } else {
      onChange(choiceId);
    }
  };

  const isChoiceSelected = (choiceId: string) => {
    if (isMultiple) {
      return (currentValue as string[]).includes(choiceId);
    } else {
      return currentValue === choiceId;
    }
  };

  return (
    <View className="space-y-4">
      {/* Question Text */}
      <Text className="text-foreground text-xl font-bold leading-6 mb-4">
        {question.question_text}
      </Text>

      {/* Answer per point info */}

      {/* Choices */}
      <View className="space-y-3">
        {choices.map((choice, index) => {
          const isSelected = isChoiceSelected(choice.id.toString());

          return (
            <View key={choice.id} className="mb-3">
              <Card
                className={`border-2 ${
                  isSelected
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-200 bg-white"
                }`}
              >
                <CardContent className="p-4">
                  <View className="flex-row items-start space-x-3">
                    {/* Checkbox */}
                    <View
                      className={`w-6 h-6 rounded border-2 items-center justify-center mt-1 ${
                        isSelected
                          ? "bg-blue-500 border-blue-500"
                          : "border-gray-300"
                      }`}
                    >
                      {isSelected && (
                        <Check size={16} color="white" strokeWidth={3} />
                      )}
                    </View>

                    {/* Answer Text */}
                    <View className="flex-1 ml-2">
                      <Text
                        className={`text-base leading-6 ${
                          isSelected
                            ? "text-blue-900 font-medium"
                            : "text-foreground"
                        }`}
                      >
                        {choice.label}
                      </Text>
                    </View>
                  </View>

                  {/* Touchable overlay */}
                  <Button
                    variant="ghost"
                    onPress={() => handleChoicePress(choice.id.toString())}
                    className="absolute inset-0 h-full w-full web:hover:bg-transparent web:active:bg-transparent web:focus:bg-transparent"
                    disabled={disabled}
                    style={{ backgroundColor: "transparent" }}
                  />
                </CardContent>
              </Card>
            </View>
          );
        })}
      </View>

      {/* Selected count for multi-choice */}
      {isMultiple && currentValue.length > 0 && (
        <View className="bg-green-50 p-3 rounded-lg mt-4">
          <Text className="text-green-700 text-sm font-medium">
            ✅ Đã chọn {currentValue.length} câu trả lời
          </Text>
        </View>
      )}

      {/* Image Upload Section */}
      {enableImageUpload && (
        <UniversalImageUpload
          questionId={question.documentId || question.id.toString()}
          questionType="multi_choice"
          questionText={question.question_text}
          planId={planId}
          maxImages={maxImages}
          enableCompression={true}
          showUploadStatus={true}
          onImagesChange={onImagesChange}
        />
      )}
    </View>
  );
}
