import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>View, Alert, View, ActivityIndicator } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import { Text } from "~/components/ui/text";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { ScreenWithBottomNav } from "~/components/shared/ScreenWithBottomNav";
import AuthHeader from "~/components/shared/AuthHeader";
import { View as RNView } from "react-native";
import { QuestionRenderer } from "../components";
import { PlanService } from "~/src/api/plans";
import { AuthService } from "~/src/api/auth";
import { QuestionService, Question } from "~/src/api/questions";
import { FlatPlan } from "~/src/types/entities";
import {
  UserAnswerService,
  UserAnswerTransformer,
} from "~/src/api/userAnswers";
import { useAuth } from "~/src/hooks/useAuth";
import { AuditSuccessModal } from "~/src/components/audit/AuditSuccessModal";
import {
  FormSubmissionHandler,
  ImageStorageManager,
} from "~/src/modules/audit/utils/formSubmissionHandler";

interface AuditDetailScreenProps {
  id: string;
}

interface QuestionAnswer {
  questionId: number;
  answer: any;
}

export default function AuditDetailScreen({ id }: AuditDetailScreenProps) {
  const { user } = useAuth();
  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [plan, setPlan] = useState<FlatPlan | null>(null);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [submissionProgress, setSubmissionProgress] = useState<any>(null);

  // Initialize FormSubmissionHandler
  const submissionHandler = React.useMemo(() => {
    return new FormSubmissionHandler((progress) => {
      setSubmissionProgress(progress);
    });
  }, []);

  const getCardStyle = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-50 border-green-200";
      case "in_progress":
        return "bg-blue-50 border-blue-200";
      case "pending":
        return "bg-orange-50 border-orange-200";
      case "expired":
        return "bg-red-50 border-red-200";
      default:
        return "bg-gray-50 border-gray-200";
    }
  };

  const getPageBackgroundStyle = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-50/30"; // Nhẹ hơn cho background trang
      case "in_progress":
        return "bg-blue-50/30";
      case "pending":
        return "bg-orange-50/30";
      case "expired":
        return "bg-red-50/30";
      default:
        return "bg-gray-50/30";
    }
  };

  const handleBack = () => {
    router.back();
  };

  // Fetch plan data and questions from API
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Fetch plan data
        const planData = await PlanService.getPlan(parseInt(id));
        setPlan(planData);

        // Log audit form status
        console.log(
          "AuditDetail - Plan loaded:",
          planData.id,
          "- Has audit_form:",
          !!planData.audit_form
        );

        // Nếu không có audit_form thì báo lỗi và dừng lại
        if (!planData.audit_form) {
          console.log(
            "AuditDetail - No audit_form found for plan:",
            planData.id
          );
          setError(
            "Kế hoạch này chưa được gán audit form. Vui lòng kiểm tra lại!"
          );
          setIsLoading(false);
          return;
        }

        // Fetch questions nếu có audit_form
        const questionsData = await QuestionService.getQuestionsByAuditForm(
          planData.audit_form.id
        );
        setQuestions(questionsData);
      } catch (err: any) {
        setError(err.message || "Không thể tải thông tin kế hoạch");
        console.error("Error fetching data:", err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [id]);

  // Cleanup images when component unmounts or plan changes
  useEffect(() => {
    return () => {
      // Clear images for this plan when navigating away
      if (plan?.id) {
        console.log("🧹 Cleaning up images for plan:", plan.id);
        ImageStorageManager.clearPlanImages(plan.id.toString()).catch(
          (error) => {
            console.error("Error cleaning up plan images:", error);
          }
        );
      }
    };
  }, [plan?.id]);

  // Get audit form and store from plan
  const auditForm = plan?.audit_form;
  const store = plan?.store;

  const updateAnswer = (questionId: string, answer: any) => {
    console.log(
      "🔍 UPDATING ANSWER - questionId:",
      questionId,
      "answer:",
      JSON.stringify(answer, null, 2)
    );
    setAnswers((prev) => ({ ...prev, [questionId]: answer }));
  };

  const handleSubmit = async () => {
    // Check if all required questions are answered
    const unansweredQuestions = questions.filter((q) => {
      if (!q.is_required) return false;

      const answer = answers[String(q.documentId || q.id)];

      // Handle different question types
      if (q.type === "yes_no") {
        // For yes_no questions, check if all subcategories are answered
        if (!answer || typeof answer !== "object") return true;

        // Check if all categories and subcategories are answered
        const options = (q.options as any) || {};
        for (const category in options) {
          if (!answer[category]) return true;
          const categoryOptions = options[category] as Record<string, any>;
          for (const subcategory in categoryOptions) {
            if (!answer[category][subcategory]) return true;
            const subcategoryData = categoryOptions[subcategory];
            // Handle both array and object formats for items
            const itemsToCheck = Array.isArray(subcategoryData)
              ? subcategoryData
              : Object.keys(subcategoryData || {});

            for (const item of itemsToCheck) {
              if (
                answer[category][subcategory][item] === null ||
                answer[category][subcategory][item] === undefined
              ) {
                return true;
              }
            }
          }
        }
        return false;
      } else {
        // For other question types, check if answer exists
        return answer === undefined || answer === null || answer === "";
      }
    });

    if (unansweredQuestions.length > 0) {
      Alert.alert("Lỗi", "Vui lòng trả lời tất cả các câu hỏi bắt buộc");
      return;
    }

    setIsSubmitting(true);
    setSubmissionProgress(null);

    try {
      console.log("🔥 STARTING COMPREHENSIVE FORM SUBMISSION WITH IMAGES");

      // Get fresh user and plan data to ensure we have documentIds
      const freshUser = await AuthService.getProfile();
      const freshPlan = await PlanService.getPlan(plan?.id);

      if (!freshUser.documentId || !freshPlan.documentId) {
        Alert.alert("Lỗi", "Không thể lấy thông tin người dùng hoặc kế hoạch");
        return;
      }

      // Get questions with documentId from the component state (not from plan)
      const questionsWithDocumentId = questions || [];
      console.log("Questions with documentId:", questionsWithDocumentId);

      // Validate all documentIds are present
      const missingDocumentIds = questionsWithDocumentId.filter(
        (q) => !q.documentId
      );
      if (missingDocumentIds.length > 0) {
        console.error("❌ Questions missing documentId:", missingDocumentIds);
        Alert.alert("Lỗi", "Một số câu hỏi không có documentId");
        return;
      }

      console.log("✅ ALL DOCUMENTIDS READY:");
      console.log("- User documentId:", freshUser.documentId);
      console.log("- Plan documentId:", freshPlan.documentId);
      console.log(
        "- Questions documentIds:",
        questionsWithDocumentId.map((q) => q.documentId)
      );

      // STEP 5: Get all stored images from ImageStorageManager
      console.log(
        "🔥 STEP 5: Getting stored images from ImageStorageManager..."
      );
      const allStoredImages = await ImageStorageManager.getAllStoredImages();
      console.log("📸 Total stored images found:", allStoredImages.length);

      // CRITICAL DEBUG: Log all stored images details
      console.log("📸 DETAILED STORED IMAGES:");
      allStoredImages.forEach((img, index) => {
        console.log(`📸 Stored image ${index + 1}:`, {
          questionId: img.questionId,
          id: img.id,
          name: img.name,
          isUploaded: img.isUploaded,
          uri: img.uri.substring(0, 50) + "...",
        });
      });

      // Group stored images by question ID
      const storedImagesByQuestion: { [questionId: string]: string[] } = {};
      allStoredImages.forEach((img) => {
        if (!storedImagesByQuestion[img.questionId]) {
          storedImagesByQuestion[img.questionId] = [];
        }
        storedImagesByQuestion[img.questionId].push(img.uri);
      });

      console.log(
        "📸 Stored images by question:",
        Object.keys(storedImagesByQuestion).map((qId) => ({
          questionId: qId,
          imageCount: storedImagesByQuestion[qId].length,
        }))
      );

      // STEP 6: Prepare form submission data with stored images
      console.log(
        "🔥 STEP 6: Preparing form submission data with stored images..."
      );

      const formAnswers = questionsWithDocumentId.map((question: any) => {
        const questionId = String(question.documentId || question.id);
        const storedImageUris = storedImagesByQuestion[questionId] || [];
        const answerValue = answers[questionId];

        console.log(`📸 Question ${questionId} images:`, {
          questionType: question.type,
          answerValue: answerValue,
          storedCount: storedImageUris.length,
          finalImageUris: storedImageUris.slice(0, 2), // Log first 2 URIs
        });

        return {
          questionId,
          questionType: question.type,
          answerValue: answerValue,
          imageUris: storedImageUris,
        };
      });

      console.log("🔥 STEP 6: Form answers prepared:", formAnswers.length);
      console.log(
        "📋 Form answers with images:",
        formAnswers
          .filter((fa) => fa.imageUris.length > 0)
          .map((fa) => ({
            questionId: fa.questionId,
            questionType: fa.questionType,
            imageCount: fa.imageUris.length,
          }))
      );

      const submissionData = {
        planId: freshPlan.documentId,
        storeId: (freshPlan.store as any)?.documentId || freshPlan.store?.id,
        userId: freshUser.documentId,
        answers: formAnswers,
      };

      console.log(
        "🔥 STEP 7: Starting comprehensive submission with stored images..."
      );
      console.log(
        "🔥 Form answers with images:",
        formAnswers.map((fa) => ({
          questionId: fa.questionId,
          questionType: fa.questionType,
          imageCount: fa.imageUris.length,
        }))
      );

      console.log(
        "🚨 ABOUT TO CALL submissionHandler.submitForm() - WATCH FOR LOGS!"
      );

      try {
        const result = await submissionHandler.submitForm(submissionData);
        console.log(
          "🚨 FormSubmissionHandler.submitForm() COMPLETED SUCCESSFULLY:",
          result
        );

        if (!result.success) {
          throw new Error(result.error || "Submission failed");
        }

        console.log("✅ Form submitted successfully with images:", result);

        // If we get here, FormSubmissionHandler worked, so we can return early
        setShowSuccessModal(true);
        return;
      } catch (formSubmissionError) {
        console.error(
          "🚨 FormSubmissionHandler.submitForm() FAILED:",
          formSubmissionError
        );
        console.error("🚨 Error details:", {
          message:
            formSubmissionError instanceof Error
              ? formSubmissionError.message
              : String(formSubmissionError),
          stack:
            formSubmissionError instanceof Error
              ? formSubmissionError.stack
              : undefined,
          name:
            formSubmissionError instanceof Error
              ? formSubmissionError.name
              : typeof formSubmissionError,
        });
        console.log("🚨 FALLING BACK TO OLD SUBMISSION METHOD...");

        // Continue with the old submission method as fallback
      }

      // FALLBACK: Old submission method (without image upload)
      console.log("🔄 Using fallback submission method...");
      const userAnswers = UserAnswerTransformer.transformAnswersToUserAnswers(
        answers,
        freshUser.documentId,
        freshPlan.documentId,
        questionsWithDocumentId
          .filter((q) => q.documentId)
          .map((q) => ({
            id: q.documentId!,
            type: q.type,
          }))
      );

      const savePromises = userAnswers.map((answerData) =>
        UserAnswerService.saveUserAnswer(answerData)
      );

      await Promise.all(savePromises);

      // Update plan status to completed
      try {
        const updatedPlan = await PlanService.updatePlanStatus(
          freshPlan.documentId || freshPlan.id,
          "completed"
        );
        console.log("Plan status updated successfully:", updatedPlan);
      } catch (statusError) {
        console.error("Failed to update plan status:", statusError);
        Alert.alert(
          "Cảnh báo",
          "Báo cáo đã được lưu nhưng không thể cập nhật trạng thái kế hoạch. Vui lòng kiểm tra lại."
        );
      }

      setShowSuccessModal(true);
    } catch (error: any) {
      console.error("Failed to submit answers:", error);
      const errorMessage =
        error?.message || "Không thể gửi báo cáo. Vui lòng thử lại.";
      Alert.alert("Lỗi", errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <ScreenWithBottomNav>
        <SafeAreaView className="flex-1 bg-background">
          <AuthHeader />
          <View className="flex-1 justify-center items-center">
            <ActivityIndicator size="large" />
            <Text className="mt-4 text-gray-600">
              Đang tải thông tin audit...
            </Text>
          </View>
        </SafeAreaView>
      </ScreenWithBottomNav>
    );
  }

  // Show error state
  if (error) {
    return (
      <ScreenWithBottomNav>
        <SafeAreaView className="flex-1 bg-background">
          <AuthHeader />
          <View className="flex-1 justify-center items-center p-4">
            <Text className="text-red-600 text-center mb-4">{error}</Text>
            <Button onPress={() => router.back()}>
              <Text className="text-white font-medium">Quay lại</Text>
            </Button>
          </View>
        </SafeAreaView>
      </ScreenWithBottomNav>
    );
  }

  // Show no plan found state
  if (!plan) {
    return (
      <ScreenWithBottomNav>
        <SafeAreaView className="flex-1 bg-background">
          <AuthHeader />
          <View className="flex-1 justify-center items-center p-4">
            <Text className="text-gray-600 text-center mb-4">
              Không tìm thấy kế hoạch audit với ID: {id}
            </Text>
            <Button onPress={() => router.back()}>
              <Text className="text-white font-medium">Quay lại</Text>
            </Button>
          </View>
        </SafeAreaView>
      </ScreenWithBottomNav>
    );
  }

  return (
    <ScreenWithBottomNav>
      <SafeAreaView
        className={`flex-1 ${getPageBackgroundStyle(
          plan?.plan_status || "pending"
        )}`}
      >
        <AuthHeader />

        <ScrollView className="flex-1 p-4">
          <Text className="text-2xl font-bold text-center mb-8 text-foreground">
            {auditForm?.name}
          </Text>

          {store && (
            <Card
              className={`mb-8 ${getCardStyle(plan?.plan_status || "pending")}`}
            >
              <CardContent className="p-6">
                <Text className="font-semibold text-lg mb-4 text-foreground">
                  Thông tin cửa hàng
                </Text>
                <View className="space-y-3">
                  <View className="flex-row items-center">
                    <Text className="font-medium text-gray-600 w-20">Tên:</Text>
                    <Text className="text-foreground flex-1">
                      {store.store_name}
                    </Text>
                  </View>
                  <View className="flex-row items-center">
                    <Text className="font-medium text-gray-600 w-20">Mã:</Text>
                    <Text className="text-foreground flex-1">
                      {store.store_code}
                    </Text>
                  </View>
                  <View className="flex-row items-start">
                    <Text className="font-medium text-gray-600 w-20 mt-1">
                      Địa chỉ:
                    </Text>
                    <Text className="text-foreground flex-1">
                      {store.address}
                    </Text>
                  </View>
                </View>
              </CardContent>
            </Card>
          )}

          {/* Render questions */}
          {questions.map((question, index) => (
            <Card
              key={question.id}
              className={`mb-6 ${getCardStyle(plan?.plan_status || "pending")}`}
            >
              <CardContent className="p-6">
                <View className="mb-6">
                  <View className="flex-row items-center">
                    <View className="bg-primary rounded-full w-8 h-8 items-center justify-center mr-3">
                      <Text className="text-white font-bold text-sm">
                        {index + 1}
                      </Text>
                    </View>
                    <Text className="font-semibold text-lg text-foreground">
                      Câu {index + 1}
                      {question.is_required && (
                        <Text className="text-red-500 ml-1">*</Text>
                      )}
                    </Text>
                  </View>
                </View>

                <QuestionRenderer
                  question={question}
                  value={answers[String(question.documentId || question.id)]}
                  onChange={(value) =>
                    updateAnswer(
                      String(question.documentId || question.id),
                      value
                    )
                  }
                  planId={plan?.id?.toString()}
                  enableImageUpload={
                    question.type !== "photo" &&
                    question.is_image_answer_require === true
                  }
                />
              </CardContent>
            </Card>
          ))}

          {questions.length === 0 && (
            <Card
              className={`mb-6 ${getCardStyle(plan?.plan_status || "pending")}`}
            >
              <CardContent className="p-6">
                <View className="mb-6">
                  <View className="flex-row items-center mb-4">
                    <View className="bg-primary rounded-full w-8 h-8 items-center justify-center mr-3">
                      <Text className="text-white font-bold text-sm">1</Text>
                    </View>
                    <Text className="font-semibold text-lg text-foreground">
                      Không có câu hỏi
                    </Text>
                  </View>
                </View>

                <View className="space-y-4">
                  <Text className="text-foreground text-base leading-6 mb-4">
                    Không có câu hỏi nào cho audit form này.
                  </Text>
                  <Text className="text-gray-500 text-sm">
                    Audit Form ID: {auditForm?.id}
                  </Text>
                </View>
              </CardContent>
            </Card>
          )}

          <View className="mt-8 mb-8">
            <Button
              onPress={handleSubmit}
              disabled={isSubmitting}
              className="py-4"
            >
              <Text className="text-white font-semibold text-lg">
                {isSubmitting ? "Đang gửi..." : "Gửi báo cáo Audit"}
              </Text>
            </Button>
          </View>
        </ScrollView>

        {/* Success Modal */}
        <AuditSuccessModal
          visible={showSuccessModal}
          onClose={() => {
            setShowSuccessModal(false);
            // Navigate to home after successful submission
            router.replace("/(tabs)/home");
          }}
          onViewAnswers={() => {
            setShowSuccessModal(false);
            // Use documentId if available, otherwise use id
            const planIdentifier = plan?.documentId || plan?.id;
            console.log(
              "Navigating to submitted answers with planId:",
              planIdentifier
            );
            router.replace(
              `/(audit)/submitted-answers?planId=${planIdentifier}`
            );
          }}
          onBackToHome={() => {
            setShowSuccessModal(false);
            // Navigate back and refresh the home screen
            router.replace("/(tabs)/home");
          }}
          answerCount={Object.keys(answers).length}
          totalQuestions={questions.length}
          planTitle={plan?.plan_title}
          storeName={plan?.store?.store_name}
        />
      </SafeAreaView>
    </ScreenWithBottomNav>
  );
}
