import React, { useState } from "react";
import { View, Text, ScrollView, Alert } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import { ScreenWithBottomNav } from "~/components/shared/ScreenWithBottomNav";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
// TODO: Replace with API calls
// import { questionaires } from "~/src/mockdata/questionaire";
// import { stores } from "~/src/mockdata/store";
// import { merchandisers } from "~/src/mockdata/merchandiser";
// import { plans } from "~/src/mockdata/plan";

import AuthHeader from "~/components/shared/AuthHeader";

// Temporary empty data until API integration
const questionaires: any[] = [];
const stores: any[] = [];
const merchandisers: any[] = [];
const plans: any[] = [];

interface CreateAuditForm {
  questionnaireId: number | null;
  storeId: number | null;
  merchandiserIds: number[];
  scheduledDate: string;
}

export default function CreateScreen() {
  const router = useRouter();
  const [formData, setFormData] = useState<CreateAuditForm>({
    questionnaireId: null,
    storeId: null,
    merchandiserIds: [],
    scheduledDate: new Date().toISOString().split("T")[0],
  });

  const handleSubmit = () => {
    if (
      !formData.questionnaireId ||
      !formData.storeId ||
      formData.merchandiserIds.length === 0
    ) {
      Alert.alert("Lỗi", "Vui lòng điền đầy đủ các trường bắt buộc");
      return;
    }

    // Create new audit plan (simulate API call)
    const newPlanId = Math.max(...plans.map((p) => p.id)) + 1;

    Alert.alert(
      "Thành công",
      `Tạo kế hoạch kiểm toán thành công! ID: ${newPlanId}`,
      [
        {
          text: "OK",
          onPress: () => router.back(),
        },
      ]
    );
  };

  const handleBack = () => {
    router.back();
  };

  const selectedQuestionnaire = questionaires.find(
    (q) => q.id === formData.questionnaireId
  );
  const selectedStore = stores.find((s) => s.id === formData.storeId);

  return (
    <ScreenWithBottomNav>
      <SafeAreaView className="flex-1 bg-background">
        <AuthHeader title="Tạo kiểm toán mới" onBack={handleBack} />

        <ScrollView className="flex-1 p-4">
          {/* Questionnaire Selection */}
          <Card className="mb-4">
            <CardContent className="p-4">
              <Text className="text-lg font-semibold mb-3">
                Chọn bảng câu hỏi *
              </Text>
              {questionaires.map((questionnaire) => (
                <View key={questionnaire.id} className="mb-2">
                  <Button
                    variant={
                      formData.questionnaireId === questionnaire.id
                        ? "default"
                        : "outline"
                    }
                    onPress={() =>
                      setFormData({
                        ...formData,
                        questionnaireId: questionnaire.id,
                      })
                    }
                    className="justify-start h-auto p-3"
                  >
                    <View className="flex-1">
                      <Text
                        className={`font-medium ${
                          formData.questionnaireId === questionnaire.id
                            ? "text-white"
                            : "text-foreground"
                        }`}
                      >
                        {questionnaire.name}
                      </Text>
                      <Text
                        className={`text-sm mt-1 ${
                          formData.questionnaireId === questionnaire.id
                            ? "text-gray-200"
                            : "text-gray-600"
                        }`}
                      >
                        Phiên bản: {questionnaire.version}
                      </Text>
                    </View>
                  </Button>
                </View>
              ))}
            </CardContent>
          </Card>

          {/* Store Selection */}
          <Card className="mb-4">
            <CardContent className="p-4">
              <Text className="text-lg font-semibold mb-3">
                Chọn cửa hàng *
              </Text>
              {stores
                .filter((store) => store.is_active)
                .map((store) => (
                  <View key={store.id} className="mb-2">
                    <Button
                      variant={
                        formData.storeId === store.id ? "default" : "outline"
                      }
                      onPress={() =>
                        setFormData({ ...formData, storeId: store.id })
                      }
                      className="justify-start h-auto p-3"
                    >
                      <View className="flex-1">
                        <Text
                          className={`font-medium ${
                            formData.storeId === store.id
                              ? "text-white"
                              : "text-foreground"
                          }`}
                        >
                          {store.store_name}
                        </Text>
                        <Text
                          className={`text-sm mt-1 ${
                            formData.storeId === store.id
                              ? "text-gray-200"
                              : "text-gray-600"
                          }`}
                        >
                          {store.address}
                        </Text>
                        <Text
                          className={`text-xs mt-1 ${
                            formData.storeId === store.id
                              ? "text-gray-300"
                              : "text-gray-500"
                          }`}
                        >
                          Mã: {store.store_code}
                        </Text>
                      </View>
                    </Button>
                  </View>
                ))}
            </CardContent>
          </Card>

          {/* Merchandiser Selection */}
          <Card className="mb-4">
            <CardContent className="p-4">
              <Text className="text-lg font-semibold mb-3">
                Chọn người thực hiện *
              </Text>
              {merchandisers
                .filter((merchandiser) => merchandiser.is_active)
                .map((merchandiser) => (
                  <View key={merchandiser.id} className="mb-2">
                    <Button
                      variant={
                        formData.merchandiserIds.includes(merchandiser.id)
                          ? "default"
                          : "outline"
                      }
                      onPress={() => {
                        const isSelected = formData.merchandiserIds.includes(
                          merchandiser.id
                        );
                        const newMerchandiserIds = isSelected
                          ? formData.merchandiserIds.filter(
                              (id) => id !== merchandiser.id
                            )
                          : [...formData.merchandiserIds, merchandiser.id];
                        setFormData({
                          ...formData,
                          merchandiserIds: newMerchandiserIds,
                        });
                      }}
                      className="justify-start h-auto p-3"
                    >
                      <View className="flex-1">
                        <Text
                          className={`font-medium ${
                            formData.merchandiserIds.includes(merchandiser.id)
                              ? "text-white"
                              : "text-foreground"
                          }`}
                        >
                          {merchandiser.name}
                        </Text>
                        <Text
                          className={`text-sm mt-1 ${
                            formData.merchandiserIds.includes(merchandiser.id)
                              ? "text-gray-200"
                              : "text-gray-600"
                          }`}
                        >
                          {merchandiser.email}
                        </Text>
                      </View>
                    </Button>
                  </View>
                ))}
            </CardContent>
          </Card>

          {/* Summary */}
          {(formData.questionnaireId ||
            formData.storeId ||
            formData.merchandiserIds.length > 0) && (
            <Card className="mb-4">
              <CardContent className="p-4">
                <Text className="text-lg font-semibold mb-3">Tóm tắt</Text>
                {selectedQuestionnaire && (
                  <Text className="text-sm mb-2">
                    Bảng câu hỏi: {selectedQuestionnaire.name}
                  </Text>
                )}
                {selectedStore && (
                  <Text className="text-sm mb-2">
                    Cửa hàng: {selectedStore.store_name}
                  </Text>
                )}
                {formData.merchandiserIds.length > 0 && (
                  <Text className="text-sm mb-2">
                    Người thực hiện:{" "}
                    {merchandisers
                      .filter((m) => formData.merchandiserIds.includes(m.id))
                      .map((m) => m.name)
                      .join(", ")}
                  </Text>
                )}
              </CardContent>
            </Card>
          )}

          {/* Submit Button */}
          <Button onPress={handleSubmit} className="mb-8">
            <Text className="text-white font-medium">
              Tạo kế hoạch kiểm toán
            </Text>
          </Button>
        </ScrollView>
      </SafeAreaView>
    </ScreenWithBottomNav>
  );
}
