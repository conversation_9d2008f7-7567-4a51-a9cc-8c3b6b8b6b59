import React, { useState } from "react";
import { View, Text, ScrollView, Pressable } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import { Card, CardContent } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import {
  CheckCircle,
  Clock,
  Circle,
  ChevronRight,
  Calendar,
  Store,
  User,
  AlertCircle,
  Eye,
} from "lucide-react-native";
import AuthHeader from "~/components/shared/AuthHeader";

import { mockAuditData, auditTabs } from "~/src/mockdata";

const auditData = mockAuditData;

export default function PlanScreen() {
  const [activeTab, setActiveTab] = useState("pending");
  const router = useRouter();

  const handleAuditPress = (auditId: string) => {
    router.push(`/(audit)/audit-form?id=${auditId}`);
  };

  const handleViewAnswers = (audit: any, event: any) => {
    event.stopPropagation();
    const planIdentifier = audit.id; // Use numeric ID instead of documentId
    console.log("Navigating to submitted answers with planId:", planIdentifier);
    router.push(`/(audit)/submitted-answers?planId=${planIdentifier}`);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle size={20} color="#10b981" />;
      case "in_progress":
        return <Clock size={20} color="#3b82f6" />;
      case "pending":
        return <Circle size={20} color="#f59e0b" />;
      case "expired":
        return <AlertCircle size={20} color="#ef4444" />;
      default:
        return null;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "completed":
        return "Hoàn thành";
      case "in_progress":
        return "Đang thực hiện";
      case "pending":
        return "Cần thực hiện";
      case "expired":
        return "Đã hết hạn";
      default:
        return "";
    }
  };

  const getCardStyle = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-50 border-green-200";
      case "in_progress":
        return "bg-blue-50 border-blue-200";
      case "pending":
        return "bg-orange-50 border-orange-200";
      case "expired":
        return "bg-red-50 border-red-200";
      default:
        return "bg-gray-50 border-gray-200";
    }
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString("vi-VN", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const isUpcoming = (assignedDate: string) => {
    const assigned = new Date(assignedDate);
    const now = new Date();
    const sevenDaysFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
    return assigned >= now && assigned <= sevenDaysFromNow;
  };

  const getFilteredData = () => {
    switch (activeTab) {
      case "pending":
        return auditData.filter((item) => item.plan_status === "pending");
      case "completed":
        return auditData.filter((item) => item.plan_status === "completed");
      default:
        return auditData.filter((item) => item.plan_status === "pending");
    }
  };

  const filteredData = getFilteredData();

  return (
    <SafeAreaView className="flex-1 bg-background">
      <ScrollView className="flex-1">
        {/* Header */}
        <AuthHeader />

        {/* Tab Navigation */}
        <View className="py-4">
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View className="flex-row px-4">
              {auditTabs.map((tab) => (
                <Pressable
                  key={tab.key}
                  onPress={() => setActiveTab(tab.key)}
                  className={`px-4 py-2 mx-1 rounded-full border border-gray-300 ${
                    activeTab === tab.key ? "bg-black" : "bg-white"
                  }`}
                >
                  <Text
                    className={`font-medium ${
                      activeTab === tab.key ? "text-white" : "text-black"
                    }`}
                  >
                    {tab.label}
                  </Text>
                </Pressable>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Audit List */}
        <View className="p-4 pt-0">
          {filteredData.map((audit) => (
            <Pressable
              key={audit.id}
              onPress={() => handleAuditPress(audit.id)}
              className="mb-4"
            >
              <Card className={`${getCardStyle(audit.plan_status)}`}>
                <CardContent className="p-4">
                  <View className="flex-row justify-between items-start mb-3">
                    <View className="flex-1 mr-3">
                      <Text className="text-lg font-semibold text-foreground mb-2">
                        {audit.plan_title}
                      </Text>

                      {/* Store Info */}
                      <View className="flex-row items-center mb-2">
                        <Store size={16} color="#6b7280" />
                        <Text className="text-sm text-gray-600 ml-1">
                          {audit.store.store_name} ({audit.store.store_code})
                        </Text>
                      </View>

                      {/* Assigned Date */}
                      <View className="flex-row items-center mb-2">
                        <Calendar size={16} color="#6b7280" />
                        <Text className="text-sm text-gray-600 ml-1">
                          Ngày thực hiện: {formatDateTime(audit.assigned_date)}
                        </Text>
                      </View>

                      {/* Merchandiser */}
                      <View className="flex-row items-center">
                        <User size={16} color="#6b7280" />
                        <Text className="text-sm text-gray-600 ml-1">
                          {audit.merchandiser.name}
                        </Text>
                      </View>
                    </View>

                    <View className="flex-row items-center">
                      {getStatusIcon(audit.plan_status)}
                      <Text className="text-sm ml-2 font-medium">
                        {getStatusLabel(audit.plan_status)}
                      </Text>
                    </View>
                  </View>

                  <View className="flex-row justify-between items-center">
                    <Text className="text-sm text-gray-600">
                      ID: {audit.id}
                    </Text>
                    <View className="flex-row items-center">
                      {audit.plan_status === "completed" && (
                        <Button
                          variant="outline"
                          size="sm"
                          onPress={(event) => handleViewAnswers(audit, event)}
                          className="mr-2 px-3 py-1"
                        >
                          <View className="flex-row items-center">
                            <Eye size={14} color="#3b82f6" />
                            <Text className="ml-1 text-xs text-blue-600">
                              Xem câu trả lời
                            </Text>
                          </View>
                        </Button>
                      )}
                      <ChevronRight size={20} color="#6b7280" />
                    </View>
                  </View>
                </CardContent>
              </Card>
            </Pressable>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
