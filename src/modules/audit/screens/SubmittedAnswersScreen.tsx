import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  ScrollView,
  ActivityIndicator,
  Alert,
  Image,
  TouchableOpacity,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter, useLocalSearchParams } from "expo-router";
import { ScreenWithBottomNav } from "~/components/shared/ScreenWithBottomNav";
import { Card, CardContent } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { ImagePreview } from "~/src/components/ui/image-preview";
import {
  ArrowLeft,
  CheckCircle,
  FileText,
  Calendar,
  User,
  Store,
  Camera,
  ImageIcon,
} from "lucide-react-native";
import AuthHeader from "~/components/shared/AuthHeader";
import { UserAnswerService } from "~/src/api/userAnswers";
import { PlanService } from "~/src/api/plans";
import { QuestionService, Question } from "~/src/api/questions";
import { FlatUserAnswer, FlatPlan, StrapiMedia } from "~/src/types/entities";
import { useAuth } from "~/src/hooks/useAuth";

export default function SubmittedAnswersScreen() {
  const { planId } = useLocalSearchParams();
  const router = useRouter();
  const { user } = useAuth();

  const [userAnswers, setUserAnswers] = useState<FlatUserAnswer[]>([]);
  const [plan, setPlan] = useState<FlatPlan | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Image preview state
  const [previewImages, setPreviewImages] = useState<string[]>([]);
  const [previewIndex, setPreviewIndex] = useState(0);
  const [showImagePreview, setShowImagePreview] = useState(false);

  useEffect(() => {
    loadSubmittedAnswers();
  }, [planId]);

  const loadSubmittedAnswers = async () => {
    if (!planId) {
      setError("Không tìm thấy ID kế hoạch");
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      console.log(
        "Loading submitted answers for planId:",
        planId,
        "type:",
        typeof planId
      );

      // Load plan details first to get the correct plan documentId for user answers
      const planData = await PlanService.getPlan(planId as string);
      console.log("Loaded plan data:", planData);

      // Use the plan's documentId for user answers query (Strapi v5)
      if (!planData.documentId) {
        throw new Error("Plan documentId not found");
      }

      console.log(
        "Using plan documentId for user answers query:",
        planData.documentId
      );
      const answersData = await UserAnswerService.getUserAnswersByPlan(
        planData.documentId
      );
      console.log("Loaded answers data:", answersData);

      setPlan(planData);
      setUserAnswers(answersData);

      // CRITICAL DEBUG: Log photo_value data for each answer
      console.log("🖼️ SUBMITTED ANSWERS PHOTO_VALUE ANALYSIS:");
      answersData.forEach((answer, index) => {
        console.log(`📋 Answer ${index + 1}:`, {
          questionId: answer.question?.documentId || answer.question?.id,
          questionType: answer.question?.type,
          questionText:
            answer.question?.question_text?.substring(0, 50) + "...",
          photoValue: answer.photo_value,
          photoValueType: typeof answer.photo_value,
          photoValueIsArray: Array.isArray(answer.photo_value),
          photoValueLength: Array.isArray(answer.photo_value)
            ? answer.photo_value.length
            : "N/A",
          photoValueSample:
            Array.isArray(answer.photo_value) && answer.photo_value.length > 0
              ? {
                  id: answer.photo_value[0]?.id,
                  url: answer.photo_value[0]?.url,
                  name: answer.photo_value[0]?.name,
                }
              : null,
        });
      });
    } catch (error: any) {
      console.error("Failed to load submitted answers:", error);
      setError(error?.message || "Không thể tải câu trả lời đã gửi");
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to get image URLs from photo_value
  const getImageUrls = (photoValue?: StrapiMedia[]): string[] => {
    console.log("🔍 getImageUrls called with:", {
      photoValue,
      isArray: Array.isArray(photoValue),
      length: Array.isArray(photoValue) ? photoValue.length : "N/A",
    });

    if (!photoValue || !Array.isArray(photoValue)) {
      console.log("❌ No valid photo_value array found");
      return [];
    }

    const imageUrls = photoValue
      .filter((media) => {
        const isValid = media && media.url;
        console.log("🖼️ Media item:", {
          id: media?.id,
          url: media?.url,
          name: media?.name,
          isValid,
        });
        return isValid;
      })
      .map((media) => {
        // Handle both absolute and relative URLs
        let finalUrl;
        if (media.url.startsWith("http")) {
          finalUrl = media.url;
        } else {
          // For relative URLs, prepend the Strapi base URL
          const baseUrl =
            process.env.EXPO_PUBLIC_API_URL || "http://localhost:1337";
          finalUrl = `${baseUrl}${media.url}`;
        }

        console.log("🔗 URL transformation:", {
          originalUrl: media.url,
          finalUrl,
          baseUrl: process.env.EXPO_PUBLIC_API_URL || "http://localhost:1337",
        });

        return finalUrl;
      });

    console.log("✅ Final image URLs:", imageUrls);
    return imageUrls;
  };

  // Component to render uploaded images
  const renderUploadedImages = (answer: FlatUserAnswer) => {
    const imageUrls = getImageUrls(answer.photo_value);

    if (imageUrls.length === 0) {
      return null;
    }

    return (
      <View className="mt-3">
        <View className="flex-row items-center mb-2">
          <Camera size={16} color="#6b7280" />
          <Text className="ml-2 text-sm text-gray-500">
            Ảnh đã tải lên ({imageUrls.length})
          </Text>
        </View>

        <View className="flex-row flex-wrap gap-2">
          {imageUrls.map((imageUrl, index) => (
            <TouchableOpacity
              key={index}
              onPress={() => {
                setPreviewImages(imageUrls);
                setPreviewIndex(index);
                setShowImagePreview(true);
              }}
              className="relative"
            >
              <Image
                source={{ uri: imageUrl }}
                className="w-20 h-20 rounded-lg bg-gray-200"
                resizeMode="cover"
                onError={(error) => {
                  console.error("Failed to load image:", imageUrl, error);
                }}
              />

              {/* Image overlay with index */}
              <View className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 rounded-b-lg">
                <Text className="text-white text-xs px-1 text-center">
                  {index + 1}/{imageUrls.length}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  const formatAnswerValue = (answer: FlatUserAnswer): string => {
    if (answer.text_value) {
      return answer.text_value;
    }

    // Special handling for multi-choice questions with enhanced visual display
    if (answer.question?.type === "multi_choice") {
      // If we have multi_choice_values with data, process them
      if (answer.multi_choice_values && answer.multi_choice_values.length > 0) {
        // Check if we have the question with options.answers to map indices to actual text
        if (
          answer.question?.options?.answers &&
          Array.isArray(answer.question.options.answers)
        ) {
          const selectedAnswers = answer.multi_choice_values
            .map((choice) => {
              // The choice.label or choice.value contains 0-based index (e.g., "0", "1", "2")
              // Parse as 0-based index directly for the answers array
              const indexStr = choice.label || choice.value;
              const zeroBasedIndex = parseInt(indexStr, 10);

              // Validate the index
              if (
                isNaN(zeroBasedIndex) ||
                zeroBasedIndex < 0 ||
                zeroBasedIndex >= answer.question.options.answers.length
              ) {
                return choice.label || choice.value; // Fallback to original value
              }

              const answerText =
                answer.question.options.answers[zeroBasedIndex];

              // Return the actual answer text if found, otherwise fallback to original value
              return answerText || choice.label || choice.value;
            })
            .filter(Boolean); // Remove any null/undefined values

          // Return enhanced badge-style display for multi-choice answers
          return (
            <View className="bg-blue-50 rounded-lg p-3 border border-blue-200">
              <View className="flex-row flex-wrap gap-2">
                {selectedAnswers.map((answerText, index) => (
                  <View
                    key={index}
                    className="bg-blue-500 rounded-full px-3 py-1.5 border border-blue-600"
                  >
                    <Text className="text-sm font-medium text-white">
                      {answerText}
                    </Text>
                  </View>
                ))}
              </View>
            </View>
          );
        }

        // Fallback to original behavior if question options are not available
        const fallbackAnswers = answer.multi_choice_values
          .map((choice) => choice.label || choice.value)
          .filter(Boolean);

        return (
          <View className="bg-gray-50 rounded-lg p-3 border border-gray-200">
            <View className="flex-row flex-wrap gap-2">
              {fallbackAnswers.map((answerText, index) => (
                <View
                  key={index}
                  className="bg-gray-500 rounded-full px-3 py-1.5 border border-gray-600"
                >
                  <Text className="text-sm font-medium text-white">
                    {answerText}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        );
      }

      // For multi-choice questions with no selections, return appropriate message
      return (
        <Text className="text-base text-gray-500 italic">
          Không có lựa chọn nào được chọn
        </Text>
      );
    }

    // Handle other multi-choice values (for non-multi_choice question types)
    if (answer.multi_choice_values && answer.multi_choice_values.length > 0) {
      return answer.multi_choice_values
        .map((choice) => choice.label || choice.value)
        .join(", ");
    }

    // Check boolean_value after multi_choice_values
    if (answer.boolean_value !== undefined) {
      return answer.boolean_value ? "Có" : "Không";
    }

    if (answer.yes_no_multi) {
      const yesNoAnswer = answer.yes_no_multi.answer;

      // Handle simple boolean answers
      if (typeof yesNoAnswer === "boolean") {
        return yesNoAnswer ? "Có" : "Không";
      }

      // Handle hierarchical structure (object with categories/subcategories/items)
      if (typeof yesNoAnswer === "object" && yesNoAnswer !== null) {
        return "Xem chi tiết bên dưới"; // Indicate hierarchical data is available
      }

      return yesNoAnswer?.toString() || "Không có dữ liệu";
    }

    if (answer.photo_value && answer.photo_value.length > 0) {
      return `${answer.photo_value.length} hình ảnh đã tải lên`;
    }

    if (answer.date_time_value) {
      return formatDate(answer.date_time_value);
    }

    return "Không có dữ liệu";
  };

  // Helper function to render hierarchical yes/no answers with enhanced visual structure
  const renderHierarchicalYesNo = (hierarchicalData: any): JSX.Element => {
    if (!hierarchicalData || typeof hierarchicalData !== "object") {
      return (
        <Text className="text-base text-gray-500 italic">
          Không có dữ liệu phân cấp
        </Text>
      );
    }

    return (
      <View className="bg-gray-50 rounded-lg p-4 border border-gray-200">
        {Object.keys(hierarchicalData).map((category) => {
          const categoryData = hierarchicalData[category];

          if (typeof categoryData !== "object" || categoryData === null) {
            return null;
          }

          // Check if this category has any answered items
          const hasAnsweredItems = Object.keys(categoryData).some(
            (subcategory) => {
              const subcategoryData = categoryData[subcategory];
              if (
                typeof subcategoryData === "object" &&
                subcategoryData !== null
              ) {
                return Object.values(subcategoryData).some(
                  (value) => value !== null
                );
              }
              return false;
            }
          );

          if (!hasAnsweredItems) {
            return null;
          }

          return (
            <View key={category} className="mb-4 last:mb-0">
              {/* Category Header */}
              <View className="mb-3">
                <Text className="text-lg font-bold text-blue-800 bg-blue-100 px-3 py-2 rounded-md border-l-4 border-blue-500">
                  📱 {category}
                </Text>
              </View>

              {/* Subcategories */}
              <View className="ml-4 space-y-3">
                {Object.keys(categoryData).map((subcategory) => {
                  const subcategoryData = categoryData[subcategory];

                  if (
                    typeof subcategoryData !== "object" ||
                    subcategoryData === null
                  ) {
                    return null;
                  }

                  // Check if this subcategory has any answered items
                  const subcategoryAnsweredItems = Object.entries(
                    subcategoryData
                  ).filter(([, value]) => value !== null);

                  if (subcategoryAnsweredItems.length === 0) {
                    return null;
                  }

                  return (
                    <View
                      key={`${category}-${subcategory}`}
                      className="bg-white rounded-md p-3 border border-gray-300 shadow-sm"
                    >
                      {/* Subcategory Header */}
                      <View className="mb-2">
                        <Text className="text-base font-semibold text-gray-700 bg-gray-100 px-2 py-1 rounded border-l-2 border-gray-400">
                          🔧 {subcategory}
                        </Text>
                      </View>

                      {/* Items */}
                      <View className="ml-3 flex-row flex-wrap gap-2">
                        {subcategoryAnsweredItems.map(([item, itemValue]) => {
                          const answerText =
                            itemValue === true
                              ? "Có"
                              : itemValue === false
                              ? "Không"
                              : String(itemValue);
                          const isPositive = itemValue === true;
                          const isNegative = itemValue === false;

                          return (
                            <View
                              key={`${category}-${subcategory}-${item}`}
                              className="mb-2"
                            >
                              <View
                                className={`px-3 py-2 rounded-lg border-2 ${
                                  isPositive
                                    ? "bg-green-50 border-green-300"
                                    : isNegative
                                    ? "bg-red-50 border-red-300"
                                    : "bg-gray-50 border-gray-300"
                                }`}
                              >
                                <Text className="text-sm font-medium text-gray-800 mb-1">
                                  {item}
                                </Text>
                                <View
                                  className={`px-2 py-1 rounded-full self-start ${
                                    isPositive
                                      ? "bg-green-500"
                                      : isNegative
                                      ? "bg-red-500"
                                      : "bg-gray-500"
                                  }`}
                                >
                                  <Text className="text-xs font-bold text-white">
                                    {isPositive
                                      ? "✓ "
                                      : isNegative
                                      ? "✗ "
                                      : "• "}
                                    {answerText}
                                  </Text>
                                </View>
                              </View>
                            </View>
                          );
                        })}
                      </View>
                    </View>
                  );
                })}
              </View>
            </View>
          );
        })}
      </View>
    );
  };

  // Get selected answers as an array for multi-choice questions
  const getSelectedAnswers = (answer: FlatUserAnswer): string[] => {
    // Special handling for multi-choice questions
    if (answer.question?.type === "multi_choice") {
      // If we have multi_choice_values with data, process them
      if (answer.multi_choice_values && answer.multi_choice_values.length > 0) {
        // Check if we have the question with options.answers to map indices to actual text
        if (
          answer.question?.options?.answers &&
          Array.isArray(answer.question.options.answers)
        ) {
          const selectedAnswers = answer.multi_choice_values
            .map((choice) => {
              // The choice.label or choice.value contains 0-based index (e.g., "0", "1", "2")
              // Parse as 0-based index directly for the answers array
              const indexStr = choice.label || choice.value;
              const zeroBasedIndex = parseInt(indexStr, 10);

              // Validate the index
              if (
                isNaN(zeroBasedIndex) ||
                zeroBasedIndex < 0 ||
                zeroBasedIndex >= answer.question.options.answers.length
              ) {
                return choice.label || choice.value; // Fallback to original value
              }

              const answerText =
                answer.question.options.answers[zeroBasedIndex];

              // Return the actual answer text if found, otherwise fallback to original value
              return answerText || choice.label || choice.value;
            })
            .filter(Boolean); // Remove any null/undefined values

          return selectedAnswers;
        }

        // Fallback to original behavior if question options are not available
        return answer.multi_choice_values
          .map((choice) => choice.label || choice.value)
          .filter(Boolean);
      }

      // For multi-choice questions with no selections, return empty array
      return [];
    }

    // For non-multi-choice questions, return single answer as array
    return [formatAnswerValue(answer)];
  };

  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      return date.toLocaleString("vi-VN", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch {
      return dateString;
    }
  };

  // Component to render answer value with badges for multi-choice and hierarchical yes/no
  const renderAnswerValue = (answer: FlatUserAnswer) => {
    // Handle multi-choice questions with enhanced visual display
    if (answer.question?.type === "multi_choice") {
      const selectedAnswers = getSelectedAnswers(answer);

      if (selectedAnswers.length === 0) {
        return (
          <Text className="text-base text-gray-500 italic">
            Không có lựa chọn nào được chọn
          </Text>
        );
      }

      // Enhanced badge-style display for multi-choice answers with mobile text wrapping
      return (
        <View className="bg-blue-50 rounded-lg p-3 border border-blue-200">
          <View className="flex-row flex-wrap gap-2">
            {selectedAnswers.map((answerText, index) => (
              <View
                key={index}
                className="bg-blue-500 rounded-lg px-3 py-2 border border-blue-600 flex-shrink max-w-full"
                style={{ minWidth: 60 }}
              >
                <Text
                  className="text-sm font-medium text-white text-left"
                  style={{
                    flexWrap: "wrap",
                    lineHeight: 18,
                  }}
                  numberOfLines={0}
                >
                  {answerText}
                </Text>
              </View>
            ))}
          </View>
        </View>
      );
    }

    // Handle hierarchical yes/no questions
    if (answer.question?.type === "yes_no" && answer.yes_no_multi) {
      const yesNoAnswer = answer.yes_no_multi.answer;

      // Handle hierarchical structure (object with categories/subcategories/items)
      if (typeof yesNoAnswer === "object" && yesNoAnswer !== null) {
        return renderHierarchicalYesNo(yesNoAnswer);
      }

      // Handle simple boolean answers
      if (typeof yesNoAnswer === "boolean") {
        return (
          <Badge
            variant={yesNoAnswer ? "default" : "destructive"}
            className="self-start"
          >
            <Text className="text-sm font-medium">
              {yesNoAnswer ? "Có" : "Không"}
            </Text>
          </Badge>
        );
      }
    }

    // Multi-choice questions are already handled above, don't process them again
    if (answer.question?.type === "multi_choice") {
      return (
        <Text className="text-base text-gray-500 italic">
          Không có câu trả lời
        </Text>
      );
    }

    // Handle photo questions - show text if available, otherwise indicate images below
    if (answer.question?.type === "photo") {
      const imageUrls = getImageUrls(answer.photo_value);
      const hasText = answer.text_value && answer.text_value.trim();

      if (hasText) {
        return (
          <Text className="text-base text-gray-800">{answer.text_value}</Text>
        );
      } else if (imageUrls.length > 0) {
        return (
          <View className="flex-row items-center">
            <ImageIcon size={16} color="#6b7280" />
            <Text className="ml-2 text-base text-gray-600 italic">
              Xem ảnh bên dưới
            </Text>
          </View>
        );
      } else {
        return (
          <Text className="text-base text-gray-500 italic">
            Không có câu trả lời hoặc ảnh
          </Text>
        );
      }
    }

    // For all other question types, display as regular text
    return (
      <Text className="text-base text-gray-800">
        {formatAnswerValue(answer)}
      </Text>
    );
  };

  if (isLoading) {
    return (
      <ScreenWithBottomNav>
        <SafeAreaView className="flex-1 bg-gray-50">
          <AuthHeader title="Câu trả lời đã gửi" showBackButton />
          <View className="flex-1 justify-center items-center">
            <ActivityIndicator size="large" color="#3b82f6" />
            <Text className="mt-4 text-gray-600">Đang tải...</Text>
          </View>
        </SafeAreaView>
      </ScreenWithBottomNav>
    );
  }

  if (error) {
    return (
      <ScreenWithBottomNav>
        <SafeAreaView className="flex-1 bg-gray-50">
          <AuthHeader title="Câu trả lời đã gửi" showBackButton />
          <View className="flex-1 justify-center items-center px-4">
            <Text className="text-red-600 text-center mb-4">{error}</Text>
            <Button onPress={loadSubmittedAnswers}>
              <Text className="text-white">Thử lại</Text>
            </Button>
          </View>
        </SafeAreaView>
      </ScreenWithBottomNav>
    );
  }

  return (
    <ScreenWithBottomNav>
      <SafeAreaView className="flex-1 bg-gray-50">
        <AuthHeader title="Câu trả lời đã gửi" showBackButton />

        <ScrollView className="flex-1 px-4 py-4">
          {/* Plan Information */}
          {plan && (
            <Card className="mb-6">
              <CardContent className="p-4">
                <View className="flex-row items-center mb-3">
                  <FileText size={20} color="#3b82f6" />
                  <Text className="ml-2 text-lg font-semibold text-gray-900">
                    {plan.plan_title || "Kế hoạch kiểm toán"}
                  </Text>
                </View>

                {plan.store && (
                  <View className="flex-row items-center mb-2">
                    <Store size={16} color="#6b7280" />
                    <Text className="ml-2 text-gray-700">
                      {plan.store.store_name}
                    </Text>
                  </View>
                )}

                <View className="flex-row items-center mb-2">
                  <User size={16} color="#6b7280" />
                  <Text className="ml-2 text-gray-700">
                    {user?.username || "Người dùng"}
                  </Text>
                </View>

                <View className="flex-row items-center justify-between">
                  <View className="flex-row items-center">
                    <CheckCircle size={16} color="#10b981" />
                    <Text className="ml-2 text-gray-700">
                      {userAnswers.length} câu trả lời
                    </Text>
                  </View>
                  <Badge variant="secondary">
                    <Text className="text-green-600 font-medium">
                      Đã hoàn thành
                    </Text>
                  </Badge>
                </View>
              </CardContent>
            </Card>
          )}

          {/* Submitted Answers */}
          <View className="space-y-4">
            <Text className="text-lg font-semibold text-gray-900 mb-2">
              Câu trả lời chi tiết
            </Text>

            {userAnswers.length === 0 ? (
              <Card>
                <CardContent className="p-6 text-center">
                  <Text className="text-gray-500">
                    Chưa có câu trả lời nào được gửi
                  </Text>
                </CardContent>
              </Card>
            ) : (
              userAnswers.map((answer, index) => (
                <Card key={answer.id} className="mb-3">
                  <CardContent className="p-4">
                    <View className="mb-3">
                      <Text className="text-sm text-gray-500 mb-1">
                        Câu hỏi {index + 1}
                      </Text>
                      <Text className="text-base font-medium text-gray-900">
                        {answer.question?.question_text ||
                          "Câu hỏi không xác định"}
                      </Text>
                    </View>

                    <View className="mb-3">
                      <Text className="text-sm text-gray-500 mb-1">
                        Câu trả lời:
                      </Text>
                      {renderAnswerValue(answer)}
                    </View>

                    {/* Render uploaded images if any */}
                    {renderUploadedImages(answer)}

                    <View className="flex-row items-center justify-between">
                      <View className="flex-row items-center">
                        <Calendar size={14} color="#6b7280" />
                        <Text className="ml-1 text-xs text-gray-500">
                          {formatDate(answer.createdAt)}
                        </Text>
                      </View>

                      {answer.question?.type && (
                        <Badge variant="outline">
                          <Text className="text-xs">
                            {answer.question.type}
                          </Text>
                        </Badge>
                      )}
                    </View>
                  </CardContent>
                </Card>
              ))
            )}
          </View>

          {/* Back Button */}
          <View className="mt-6 mb-8">
            <Button
              onPress={() => router.replace("/(tabs)/home")}
              variant="outline"
              className="py-3"
            >
              <View className="flex-row items-center justify-center">
                <ArrowLeft size={18} color="#3b82f6" />
                <Text className="ml-2 text-blue-600 font-medium">
                  Về trang chủ
                </Text>
              </View>
            </Button>
          </View>
        </ScrollView>
      </SafeAreaView>

      {/* Image Preview Modal */}
      <ImagePreview
        visible={showImagePreview}
        images={previewImages}
        initialIndex={previewIndex}
        onClose={() => setShowImagePreview(false)}
      />
    </ScreenWithBottomNav>
  );
}
