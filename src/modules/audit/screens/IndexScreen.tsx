import React from "react";
import { View, Text, ScrollView, Pressable } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { ScreenWithBottomNav } from "~/components/shared/ScreenWithBottomNav";
import { CheckCircle, Clock, Circle, ChevronRight } from "lucide-react-native";
import AuthHeader from "~/components/shared/AuthHeader";

// Temporary empty data until API integration
const plans: any[] = [];
const stores: any[] = [];
const merchandisers: any[] = [];

interface AuditSummary {
  id: string;
  title: string;
  store: string;
  date: string;
  status: "completed" | "in-progress" | "pending" | "canceled";
  merchandiserNames: string;
}

export default function IndexScreen() {
  const handleCreateAudit = () => {
    router.push("/(audit)/create");
  };

  const handleViewPlan = () => {
    router.push("/(audit)/plan");
  };

  const handleViewDetail = (id: string) => {
    router.push(`/(audit)/audit-detail?id=${id}`);
  };

  // Get recent audit plans (last 3)
  const getRecentAudits = (): AuditSummary[] => {
    return plans
      .map((plan) => {
        const store = stores.find((s) => s.id === plan.store);
        const planMerchandisers = merchandisers.filter((m) =>
          plan.merchandiser.includes(m.id)
        );

        const statusMap: Record<
          string,
          "completed" | "in-progress" | "pending" | "canceled"
        > = {
          completed: "completed",
          in_progress: "in-progress",
          planed: "pending",
          canceled: "canceled",
        };

        return {
          id: plan.id.toString(),
          title: `Kiểm toán ${store?.store_name || "Cửa hàng không xác định"}`,
          store: store?.store_name || "Cửa hàng không xác định",
          date: new Date(plan.assigned_date).toLocaleDateString("vi-VN"),
          status: statusMap[plan.status_progress] || "pending",
          merchandiserNames: planMerchandisers.map((m) => m.name).join(", "),
        };
      })
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, 3);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle size={16} color="#10b981" />;
      case "in-progress":
        return <Clock size={16} color="#3b82f6" />;
      case "pending":
        return <Circle size={16} color="#f59e0b" />;
      case "canceled":
        return <Circle size={16} color="#ef4444" />;
      default:
        return null;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "completed":
        return "Hoàn thành";
      case "in-progress":
        return "Đang thực hiện";
      case "pending":
        return "Chờ thực hiện";
      case "canceled":
        return "Đã hủy";
      default:
        return "";
    }
  };

  const recentAudits = getRecentAudits();

  return (
    <ScreenWithBottomNav>
      <SafeAreaView className="flex-1 bg-background">
        <AuthHeader title="Kiểm toán Cellphones" />

        <ScrollView className="flex-1 p-4">
          {/* Quick Actions */}
          <View className="mb-6">
            <Text className="text-lg font-semibold text-foreground mb-4">
              Thao tác nhanh
            </Text>

            <View className="flex-row justify-between space-x-2">
              <Button onPress={handleCreateAudit} className="flex-1 mr-2">
                <Text className="text-white font-medium text-center">
                  Tạo mới
                </Text>
              </Button>

              <Button
                onPress={handleViewPlan}
                variant="outline"
                className="flex-1 ml-2"
              >
                <Text className="text-foreground font-medium text-center">
                  Xem kế hoạch
                </Text>
              </Button>
            </View>
          </View>

          {/* Recent Audits */}
          <View className="mb-6">
            <View className="flex-row justify-between items-center mb-4">
              <Text className="text-lg font-semibold text-foreground">
                Kiểm toán gần đây
              </Text>
              <Pressable onPress={handleViewPlan}>
                <Text className="text-blue-600 text-sm">Xem tất cả</Text>
              </Pressable>
            </View>

            {recentAudits.length > 0 ? (
              recentAudits.map((audit) => (
                <Pressable
                  key={audit.id}
                  onPress={() => handleViewDetail(audit.id)}
                  className="mb-3"
                >
                  <Card>
                    <CardContent className="p-4">
                      <View className="flex-row justify-between items-start">
                        <View className="flex-1 mr-3">
                          <Text className="font-semibold text-foreground mb-1">
                            {audit.title}
                          </Text>
                          <Text className="text-sm text-gray-600 mb-1">
                            {audit.store}
                          </Text>
                          <Text className="text-xs text-blue-600 mb-2">
                            {audit.merchandiserNames}
                          </Text>
                          <View className="flex-row items-center">
                            {getStatusIcon(audit.status)}
                            <Text className="text-xs ml-2 text-gray-500">
                              {getStatusLabel(audit.status)} • {audit.date}
                            </Text>
                          </View>
                        </View>
                        <ChevronRight size={16} color="#6b7280" />
                      </View>
                    </CardContent>
                  </Card>
                </Pressable>
              ))
            ) : (
              <Card>
                <CardContent className="p-4">
                  <Text className="text-gray-500 text-center">
                    Chưa có kế hoạch kiểm toán nào
                  </Text>
                </CardContent>
              </Card>
            )}
          </View>

          {/* Statistics */}
          <View className="mb-6">
            <Text className="text-lg font-semibold text-foreground mb-4">
              Thống kê
            </Text>

            <View className="flex-row justify-between space-x-2">
              <Card className="flex-1 mr-1">
                <CardContent className="p-3">
                  <Text className="text-2xl font-bold text-green-600 text-center">
                    {
                      plans.filter((p) => p.status_progress === "completed")
                        .length
                    }
                  </Text>
                  <Text className="text-xs text-gray-600 text-center mt-1">
                    Hoàn thành
                  </Text>
                </CardContent>
              </Card>

              <Card className="flex-1 mx-1">
                <CardContent className="p-3">
                  <Text className="text-2xl font-bold text-blue-600 text-center">
                    {
                      plans.filter((p) => p.status_progress === "in_progress")
                        .length
                    }
                  </Text>
                  <Text className="text-xs text-gray-600 text-center mt-1">
                    Đang thực hiện
                  </Text>
                </CardContent>
              </Card>

              <Card className="flex-1 ml-1">
                <CardContent className="p-3">
                  <Text className="text-2xl font-bold text-orange-600 text-center">
                    {plans.filter((p) => p.status_progress === "planed").length}
                  </Text>
                  <Text className="text-xs text-gray-600 text-center mt-1">
                    Đã lên kế hoạch
                  </Text>
                </CardContent>
              </Card>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    </ScreenWithBottomNav>
  );
}
