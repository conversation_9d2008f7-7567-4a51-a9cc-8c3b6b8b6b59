import React, { useState } from "react";
import { View, Text, ScrollView, Pressable, Alert } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter, useLocalSearchParams } from "expo-router";
import { ScreenWithBottomNav } from "~/components/shared/ScreenWithBottomNav";
import { Card, CardContent } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Checkbox } from "~/components/ui/checkbox";
import { PhotoPicker } from "~/src/components/ui/photo-picker";
import { FileUpload } from "~/src/components/ui/file-upload";
import { Image } from "react-native";
import { ChevronDown, ChevronRight } from "lucide-react-native";
import AuthHeader from "~/components/shared/AuthHeader";
import { mockAuditData, Question, Answer } from "~/src/mockdata";
import { Question<PERSON>enderer } from "~/src/modules/audit/components/QuestionRenderer";
import { ImageUploadSection } from "~/src/modules/audit/components/ImageUploadSection";
// Note: UploadConfirmModal removed - FormSubmissionHandler handles uploads automatically
import {
  UserAnswerService,
  UserAnswerTransformer,
} from "~/src/api/userAnswers";
import { useAuth } from "~/src/hooks/useAuth";
import { PlanService } from "~/src/api/plans";
import { AuthService } from "~/src/api/auth";
import { AuditSuccessModal } from "~/src/components/audit/AuditSuccessModal";
import {
  FormSubmissionHandler,
  SubmissionProgress,
  ImageStorageManager,
} from "~/src/modules/audit/utils/formSubmissionHandler";
import { getImageLimit } from "~/src/types/constants";

interface GroupedQuestions {
  [category: string]: {
    [subCategory: string]: Question[];
  };
}

export default function AuditFormScreen() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const { user, checkAuth } = useAuth();

  // Force refresh user profile to ensure we have documentId
  React.useEffect(() => {
    checkAuth();
  }, []);

  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [currentCategoryIndex, setCurrentCategoryIndex] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [plan, setPlan] = useState<any>(null);
  const [isLoadingPlan, setIsLoadingPlan] = useState(true);
  const [submittedPlan, setSubmittedPlan] = useState<any>(null); // Store the fresh plan after submission
  const [submissionProgress, setSubmissionProgress] =
    useState<SubmissionProgress | null>(null);
  const [questionImages, setQuestionImages] = useState<
    Record<string, string[]>
  >({});
  // Note: uploadedImages removed - now handled by FormSubmissionHandler
  // Note: localImages removed - FormSubmissionHandler handles all images via ImageStorageManager
  // Note: showUploadConfirm removed - FormSubmissionHandler handles uploads automatically
  const [imageUploadStatus, setImageUploadStatus] = useState<{
    [questionId: string]: {
      total: number;
      uploaded: number;
      pending: number;
      errors: string[];
    };
  }>({});

  // 🔍 LOG STATUS FOR DEBUGGING
  React.useEffect(() => {
    console.log("=== AUDIT FORM SCREEN DEBUG ===");
    console.log("📋 Plan ID:", id);
    console.log("📊 Plan found:", plan ? "✅ YES" : "❌ NO");
    if (plan) {
      console.log("🎯 Plan Status:", plan.plan_status);
      console.log("📝 Plan Title:", plan.plan_title);
      console.log("🏪 Store Name:", plan.store?.store_name);
      console.log("🏪 Store Code:", plan.store?.store_code);
      console.log("📅 Start Date:", plan.start_date);
      console.log("📅 End Date:", plan.end_date);
      console.log("👤 Merchandiser:", plan.merchandiser?.name);
      console.log("🔍 Audit Form:", plan.audit_form);
      console.log("🔍 Questions:", plan.audit_form?.questions);
      console.log("🔍 Questions Length:", plan.audit_form?.questions?.length);
    }
    console.log("================================");
  }, [id, plan]);

  // Fetch real plan data from API to get documentId
  React.useEffect(() => {
    const fetchPlan = async () => {
      if (!id) return;

      try {
        setIsLoadingPlan(true);
        console.log("🔥 Fetching plan from API with ID:", id);

        // Clear image cache when starting a new plan to prevent old images from persisting
        console.log("🧹 Clearing image cache for new plan...");
        try {
          await ImageStorageManager.clearAllImages();
          console.log("✅ Image cache cleared successfully");
        } catch (cacheError) {
          console.warn(
            "⚠️ Failed to clear image cache (non-critical):",
            cacheError
          );
        }

        // Reset local image states
        setQuestionImages({});
        setImageUploadStatus({});
        console.log("🔄 Local image states reset");

        const realPlan = await PlanService.getPlan(id as string);
        console.log("🔥 Real plan data:", realPlan);
        setPlan(realPlan);
      } catch (error) {
        console.error("Failed to fetch plan:", error);
        // No fallback to mock data - just set plan to null
        setPlan(null);
      } finally {
        setIsLoadingPlan(false);
      }
    };

    fetchPlan();
  }, [id]);

  // Cleanup effect when component unmounts
  React.useEffect(() => {
    return () => {
      console.log("🧹 AuditFormScreen unmounting, cleaning up image cache...");
      // Note: We don't await this since it's in a cleanup function
      ImageStorageManager.clearAllImages().catch((error) => {
        console.warn("⚠️ Failed to clear image cache on unmount:", error);
      });
    };
  }, []);

  if (isLoadingPlan) {
    return (
      <SafeAreaView className="flex-1 bg-background">
        <View className="flex-1 justify-center items-center">
          <Text className="text-lg text-gray-600">Đang tải kế hoạch...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const getPageBackgroundStyle = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-50/30"; // Nhẹ hơn cho background trang
      case "in_progress":
        return "bg-blue-50/30";
      case "pending":
        return "bg-orange-50/30";
      case "expired":
        return "bg-red-50/30";
      default:
        return "bg-gray-50/30";
    }
  };

  const getCardBackgroundStyle = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-50 border-green-200"; // Card đậm hơn background trang
      case "in_progress":
        return "bg-blue-50 border-blue-200";
      case "pending":
        return "bg-orange-50 border-orange-200";
      case "expired":
        return "bg-red-50 border-red-200";
      default:
        return "bg-gray-50 border-gray-200";
    }
  };

  // Log background style after function is defined
  if (plan) {
    console.log(
      "🎨 Page Background Style:",
      getPageBackgroundStyle(plan.plan_status)
    );
    console.log(
      "🎨 Card Background Style:",
      getCardBackgroundStyle(plan.plan_status)
    );
  }

  if (!plan || !plan.audit_form || !plan.audit_form.questions) {
    return (
      <SafeAreaView className="flex-1 bg-background">
        <View className="flex-1 justify-center items-center">
          <Text className="text-lg text-gray-600">
            {!plan ? "Không tìm thấy kế hoạch" : "Đang tải dữ liệu..."}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const questions = plan.audit_form.questions;

  // Group questions by category and sub-category
  const groupedQuestions: GroupedQuestions = questions.reduce(
    (acc: GroupedQuestions, question: Question) => {
      // Read category and sub_category from options
      const category = question.options?.category || "Khác";
      const subCategory = question.options?.sub_category || "Chung";

      if (!acc[category]) {
        acc[category] = {};
      }
      if (!acc[category][subCategory]) {
        acc[category][subCategory] = [];
      }
      acc[category][subCategory].push(question);
      return acc;
    },
    {} as GroupedQuestions
  );

  // Filter out category 6
  const categories = Object.keys(groupedQuestions).filter(
    (category) => category !== "Category 6"
  );

  // Check if current category is the combined category (category 1 + 8)
  const isCombinedCategory = currentCategoryIndex === 0; // First category will be combined
  const isLastCategory = currentCategoryIndex === categories.length - 1;

  // Create answer object for API submission
  const createAnswerObject = (
    questionId: string,
    value: any,
    questionType: string
  ) => {
    const baseAnswer = {
      question_id: questionId,
      user_id: user?.documentId || user?.id,
      plan_id: plan?.documentId || plan?.id,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    switch (questionType) {
      case "text":
      case "number":
      case "single_choice":
        return {
          ...baseAnswer,
          text_value: value,
        };
      case "multi_choice":
        return {
          ...baseAnswer,
          multi_choice_values: value,
        };
      case "yes_no":
        return {
          ...baseAnswer,
          yes_no_multi: {
            answer: value,
          },
          // FIX: Attach imageUris for Yes/No questions
          imageUris: questionImages[questionId] || [],
        };
      case "photo":
        return {
          ...baseAnswer,
          photo_value: value,
        };
      case "date_time":
        return {
          ...baseAnswer,
          date_time_value: value,
        };
      default:
        return baseAnswer;
    }
  };

  const handleAnswerChange = (
    questionId: string,
    value: any,
    questionType: string
  ) => {
    setAnswers((prev) => ({
      ...prev,
      [questionId]: createAnswerObject(questionId, value, questionType),
    }));
  };

  const handleNext = () => {
    if (currentCategoryIndex < categories.length - 1) {
      setCurrentCategoryIndex(currentCategoryIndex + 1);
    }
  };

  const handlePrevious = () => {
    if (currentCategoryIndex > 0) {
      setCurrentCategoryIndex(currentCategoryIndex - 1);
    }
  };

  // Handle image changes for questions
  const handleImagesChange = (questionId: string, imageUris: string[]) => {
    console.log("🔄 handleImagesChange called:", {
      questionId,
      imageUrisCount: imageUris.length,
      imageUris: imageUris.slice(0, 3), // Log first 3 URIs
    });

    setQuestionImages((prev) => ({
      ...prev,
      [questionId]: imageUris,
    }));
  };

  // Handle when images are stored via ImageStorageManager
  const handleImagesStored = (questionId: string, storedImages: any[]) => {
    console.log("📸 handleImagesStored called:", {
      questionId,
      storedImagesCount: storedImages.length,
      storedImages: storedImages.map((img) => ({
        id: img.id,
        name: img.name,
        isUploaded: img.isUploaded,
      })),
    });

    // Update the questionImages state with the stored image URIs
    const imageUris = storedImages.map((img) => img.uri);
    setQuestionImages((prev) => ({
      ...prev,
      [questionId]: imageUris,
    }));

    // Update image upload status tracking
    const uploadedCount = storedImages.filter((img) => img.isUploaded).length;
    const pendingCount = storedImages.length - uploadedCount;

    setImageUploadStatus((prev) => ({
      ...prev,
      [questionId]: {
        total: storedImages.length,
        uploaded: uploadedCount,
        pending: pendingCount,
        errors: [],
      },
    }));

    console.log("📊 Updated image upload status for question:", questionId, {
      total: storedImages.length,
      uploaded: uploadedCount,
      pending: pendingCount,
    });
  };

  const handleSubmit = async () => {
    if (!user) {
      Alert.alert("Lỗi", "Vui lòng đăng nhập để gửi báo cáo.");
      return;
    }

    // FormSubmissionHandler now handles all image uploads automatically
    await submitReport();
  };

  const submitReport = async () => {
    setIsSubmitting(true);
    setSubmissionProgress(null);

    try {
      console.log("🔥 STARTING COMPREHENSIVE FORM SUBMISSION WITH IMAGES");

      // Get fresh user and plan data to ensure we have documentIds
      const freshUser = await AuthService.getProfile();
      const freshPlan = await PlanService.getPlan(plan?.documentId || plan?.id);

      if (!freshUser.documentId || !freshPlan.documentId) {
        Alert.alert("Lỗi", "Không thể lấy thông tin người dùng hoặc kế hoạch");
        return;
      }

      // Note: Image uploads are now handled by FormSubmissionHandler
      // which retrieves images from ImageStorageManager and uploads them properly
      console.log(
        "🔥 Skipping legacy image upload - using FormSubmissionHandler instead"
      );

      // STEP 3: Get questions with documentId from the plan
      console.log("🔥 STEP 3: Getting questions with documentId...");
      const questionsWithDocumentId = freshPlan.audit_form?.questions || [];
      console.log("Questions with documentId:", questionsWithDocumentId);

      // STEP 4: Validate all documentIds are present
      const missingDocumentIds = questionsWithDocumentId.filter(
        (q: any) => !q.documentId
      );
      if (missingDocumentIds.length > 0) {
        console.error("❌ Questions missing documentId:", missingDocumentIds);
        Alert.alert("Lỗi", "Một số câu hỏi không có documentId");
        return;
      }

      console.log("✅ ALL DOCUMENTIDS READY:");
      console.log("- User documentId:", freshUser.documentId);
      console.log("- Plan documentId:", freshPlan.documentId);
      console.log(
        "- Questions documentIds:",
        questionsWithDocumentId.map((q: any) => q.documentId)
      );

      // STEP 5: Get all stored images from ImageStorageManager
      console.log(
        "🔥 STEP 5: Getting stored images from ImageStorageManager..."
      );
      const allStoredImages = await ImageStorageManager.getAllStoredImages();
      console.log("📸 Total stored images found:", allStoredImages.length);

      // CRITICAL DEBUG: Log all stored images details
      console.log("📸 DETAILED STORED IMAGES:");
      allStoredImages.forEach((img, index) => {
        console.log(`📸 Stored image ${index + 1}:`, {
          questionId: img.questionId,
          id: img.id,
          name: img.name,
          isUploaded: img.isUploaded,
          uri: img.uri.substring(0, 50) + "...",
        });
      });

      // Group stored images by question ID
      const storedImagesByQuestion: { [questionId: string]: string[] } = {};
      allStoredImages.forEach((img) => {
        if (!storedImagesByQuestion[img.questionId]) {
          storedImagesByQuestion[img.questionId] = [];
        }
        storedImagesByQuestion[img.questionId].push(img.uri);
      });

      console.log(
        "📸 Stored images by question:",
        Object.keys(storedImagesByQuestion).map((qId) => ({
          questionId: qId,
          imageCount: storedImagesByQuestion[qId].length,
        }))
      );

      // STEP 6: Prepare form submission data with stored images
      const formAnswers = questionsWithDocumentId.map((question: any) => {
        const questionId = question.documentId || question.id.toString();
        const storedImageUris = storedImagesByQuestion[questionId] || [];
        const questionImageUris = questionImages[questionId] || [];

        // Use stored images if available, otherwise fall back to questionImages state
        const imageUris =
          storedImageUris.length > 0 ? storedImageUris : questionImageUris;

        console.log(`📸 Question ${questionId} images:`, {
          questionType: question.type,
          answerValue: answers[questionId],
          storedCount: storedImageUris.length,
          stateCount: questionImageUris.length,
          finalCount: imageUris.length,
          finalImageUris: imageUris.slice(0, 2), // Log first 2 URIs
        });

        return {
          questionId,
          questionType: question.type,
          answerValue: answers[questionId],
          imageUris,
        };
      });

      console.log("🔥 STEP 6: Form answers prepared:", formAnswers.length);
      console.log(
        "📋 Form answers with images:",
        formAnswers
          .filter((fa) => fa.imageUris.length > 0)
          .map((fa) => ({
            questionId: fa.questionId,
            questionType: fa.questionType,
            imageCount: fa.imageUris.length,
          }))
      );

      // STEP 7: Use FormSubmissionHandler for comprehensive submission
      const submissionHandler = new FormSubmissionHandler((progress) => {
        setSubmissionProgress(progress);
        console.log("Submission progress:", progress);
      });

      const submissionData = {
        planId: freshPlan.documentId,
        storeId: (freshPlan.store as any)?.documentId || freshPlan.store?.id,
        userId: freshUser.documentId,
        answers: formAnswers,
      };

      console.log(
        "🔥 STEP 7: Starting comprehensive submission with stored images..."
      );
      console.log(
        "🔥 Form answers with images:",
        formAnswers.map((fa) => ({
          questionId: fa.questionId,
          questionType: fa.questionType,
          imageCount: fa.imageUris.length,
        }))
      );

      console.log(
        "🚨 ABOUT TO CALL submissionHandler.submitForm() - WATCH FOR LOGS!"
      );

      const result = await submissionHandler.submitForm(submissionData);
      console.log("🚨 FormSubmissionHandler.submitForm() COMPLETED:", result);

      if (!result.success) {
        throw new Error(result.error || "Submission failed");
      }

      // CRITICAL FIX: Update plan status to completed after successful submission
      try {
        console.log("🔄 Updating plan status to completed...");
        const updatedPlan = await PlanService.updatePlanStatus(
          freshPlan.documentId || freshPlan.id,
          "completed"
        );
        console.log("✅ Plan status updated successfully:", updatedPlan);
        setSubmittedPlan(updatedPlan); // Store the updated plan for navigation
      } catch (statusError) {
        console.error("❌ Failed to update plan status:", statusError);
        Alert.alert(
          "Cảnh báo",
          "Báo cáo đã được lưu thành công nhưng không thể cập nhật trạng thái kế hoạch. Vui lòng kiểm tra lại."
        );
      }

      console.log("✅ Form submitted successfully with images:", result);

      // Note: submittedPlan is already set above after successful plan status update
      setShowSuccessModal(true);
    } catch (error) {
      console.error("Failed to submit answers:", error);
      Alert.alert("Lỗi", "Không thể gửi báo cáo. Vui lòng thử lại sau.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderQuestion = (question: Question) => {
    const questionId = question.documentId || question.id.toString();
    const currentAnswer = answers[questionId];

    // Get the appropriate value for the question type
    const getValue = () => {
      if (!currentAnswer) return null;

      switch (question.type) {
        case "text":
        case "number":
        case "single_choice":
        case "rating":
          return currentAnswer.text_value;
        case "multi_choice":
          return currentAnswer.multi_choice_values;
        case "yes_no":
          return currentAnswer.yes_no_multi?.answer;
        case "photo":
          return currentAnswer.photo_value;
        default:
          return null;
      }
    };

    return (
      <QuestionRenderer
        question={question as any}
        value={getValue()}
        onChange={(value) =>
          handleAnswerChange(questionId, value, question.type)
        }
        disabled={isSubmitting}
        enableImageUpload={
          question.type !== "photo" && question.is_image_answer_require === true
        }
        maxImages={question.type === "photo" ? 5 : 3}
        onImagesChange={(imageUris) =>
          handleImagesChange(questionId, imageUris)
        }
        onStoredImagesChange={(storedImages) =>
          handleImagesStored(questionId, storedImages)
        }
      />
    );
  };

  const renderCurrentCategory = () => {
    if (!categories || categories.length === 0 || !groupedQuestions) {
      return (
        <View className="flex-1 justify-center items-center">
          <Text className="text-lg text-gray-600">Đang tải câu hỏi...</Text>
        </View>
      );
    }

    const categoryQuestions =
      groupedQuestions[categories[currentCategoryIndex]];

    return (
      <ScrollView
        className={`flex-1 p-4 ${getPageBackgroundStyle(
          plan?.plan_status || "pending"
        )}`}
      >
        <Card
          className={`mb-4 ${getCardBackgroundStyle(
            plan?.plan_status || "pending"
          )}`}
        >
          <CardContent className="p-4">
            <Text className="text-xl font-bold text-blue-600 mb-4">
              {categories[currentCategoryIndex]}
            </Text>

            {Object.entries(categoryQuestions).map(
              ([subCategory, questions]) => (
                <View key={subCategory} className="mb-6">
                  {subCategory !== "Chung" && (
                    <Text className="text-lg font-semibold text-gray-700 mb-3">
                      {subCategory}
                    </Text>
                  )}
                  <View className="space-y-4">
                    {questions.map((question) => (
                      <View
                        key={question.documentId || question.id}
                        className="bg-gray-50 p-4 rounded-lg"
                      >
                        {renderQuestion(question)}
                      </View>
                    ))}
                  </View>
                </View>
              )
            )}

            {/* Add photo section at the end of the first category (combined category) */}
            {isCombinedCategory && (
              <View className="mt-6 pt-6 border-t border-gray-200">
                {/* Image upload section for store photos */}
                <ImageUploadSection
                  title="Ảnh cửa hàng"
                  subtitle="Chụp ảnh hoặc chọn ảnh từ thư viện"
                  maxFiles={getImageLimit(undefined, "store_documentation")}
                  maxSize={10}
                  minFiles={1}
                  onImagesChange={(images) => {
                    console.log("Store images changed:", images.length);
                    // Store images are now handled by FormSubmissionHandler
                  }}
                  onLocalImagesChange={(localImages) => {
                    console.log(
                      "Local store images changed:",
                      localImages.length
                    );
                    // Local images are now handled by FormSubmissionHandler
                  }}
                />
              </View>
            )}
          </CardContent>
        </Card>
      </ScrollView>
    );
  };

  return (
    <SafeAreaView
      className={`flex-1 ${getPageBackgroundStyle(
        plan?.plan_status || "pending"
      )}`}
    >
      <View
        className={`flex-1 ${getPageBackgroundStyle(
          plan?.plan_status || "pending"
        )}`}
      >
        {/* Header */}
        <AuthHeader />

        {/* Progress */}
        <View className="px-4 py-2">
          <View className="flex-row justify-between items-center mb-2">
            <Text className="text-sm text-gray-600">
              Category {currentCategoryIndex + 1} / {categories.length}
            </Text>
            <Text className="text-sm text-gray-600">
              {Math.round(
                ((currentCategoryIndex + 1) / categories.length) * 100
              )}
              %
            </Text>
          </View>
          <View className="w-full bg-gray-200 rounded-full h-2">
            <View
              className="bg-blue-500 h-2 rounded-full"
              style={{
                width: `${
                  ((currentCategoryIndex + 1) / categories.length) * 100
                }%`,
              }}
            />
          </View>
        </View>

        {/* Current Category Content */}
        {renderCurrentCategory()}

        {/* Navigation */}
        <View
          className={`p-4 border-t border-gray-200 ${getPageBackgroundStyle(
            plan?.plan_status || "pending"
          )}`}
        >
          <View className="flex-row justify-between">
            <Button
              onPress={handlePrevious}
              variant="outline"
              disabled={currentCategoryIndex === 0}
            >
              <Text>Trước</Text>
            </Button>

            {isLastCategory ? (
              <Button onPress={handleSubmit} disabled={isSubmitting}>
                <Text className="text-white font-semibold">
                  {isSubmitting ? "Đang gửi..." : "Gửi báo cáo"}
                </Text>
              </Button>
            ) : (
              <Button
                onPress={handleNext}
                disabled={false} // Always allow next since we show all questions in category
              >
                <Text className="text-white font-semibold">Tiếp theo</Text>
              </Button>
            )}
          </View>
        </View>
      </View>

      {/* Success Modal */}
      <AuditSuccessModal
        visible={showSuccessModal}
        onClose={() => {
          setShowSuccessModal(false);
          // Navigate to home after successful submission
          router.replace("/(tabs)/home");
        }}
        onViewAnswers={() => {
          setShowSuccessModal(false);
          // Use the fresh plan documentId from submission
          const planIdentifier =
            submittedPlan?.documentId ||
            submittedPlan?.id ||
            plan?.documentId ||
            plan?.id;
          console.log(
            "Navigating to submitted answers with FRESH planId:",
            planIdentifier
          );
          router.replace(`/(audit)/submitted-answers?planId=${planIdentifier}`);
        }}
        onBackToHome={() => {
          setShowSuccessModal(false);
          // Navigate back and refresh the home screen
          router.replace("/(tabs)/home");
        }}
        answerCount={Object.keys(answers).length}
        totalQuestions={plan?.audit_form?.questions?.length || 0}
        planTitle={plan?.plan_title}
        storeName={plan?.store?.store_name}
      />

      {/* Note: UploadConfirmModal removed - FormSubmissionHandler handles uploads automatically */}
    </SafeAreaView>
  );
}
