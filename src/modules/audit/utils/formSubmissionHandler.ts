import { Alert } from "react-native";
import { ImageStorageManager, StoredImage } from "~/src/utils/imageStorage";
import { compressImages, cleanupTempImages } from "~/src/utils/imageUtils";
import {
  uploadMultipleQuestionImages,
  UploadedFile,
} from "~/src/api/fileUpload";
import { submitUserAnswers } from "~/src/api/userAnswers";
import { PlanService } from "~/src/api/plans";

// Re-export ImageStorageManager for use in other modules
export { ImageStorageManager };

// Types for form submission
export interface QuestionAnswer {
  questionId: string;
  questionType: string;
  answerValue: any;
  imageUris?: string[];
}

export interface FormSubmissionData {
  planId: string;
  storeId: string;
  userId: string;
  answers: QuestionAnswer[];
}

export interface SubmissionProgress {
  stage:
    | "preparing"
    | "compressing"
    | "uploading"
    | "submitting"
    | "cleanup"
    | "complete";
  message: string;
  progress: number; // 0-100
  currentItem?: string;
}

export interface SubmissionResult {
  success: boolean;
  submissionId?: string;
  uploadedImages?: { questionId: string; files: UploadedFile[] }[];
  error?: string;
}

/**
 * Comprehensive Form Submission Handler with Image Processing
 */
export class FormSubmissionHandler {
  private onProgress?: (progress: SubmissionProgress) => void;
  private aborted = false;

  constructor(onProgress?: (progress: SubmissionProgress) => void) {
    this.onProgress = onProgress;
  }

  /**
   * Submit form with comprehensive image processing
   */
  async submitForm(data: FormSubmissionData): Promise<SubmissionResult> {
    console.log(
      "🚨🚨🚨 FORMSUBMISSIONHANDLER.SUBMITFORM CALLED - NEW VERSION WITH LOGGING 🚨🚨🚨"
    );
    console.log("📋 Submission data received:", {
      planId: data.planId,
      storeId: data.storeId,
      userId: data.userId,
      answersCount: data.answers.length,
    });

    try {
      this.aborted = false;

      // Stage 1: Prepare submission
      this.updateProgress({
        stage: "preparing",
        message: "Chuẩn bị dữ liệu gửi...",
        progress: 0,
      });

      // CRITICAL DEBUG: Log all answers with their image data
      console.log("🔍 CRITICAL DEBUG - All answers with image data:");
      data.answers.forEach((answer, index) => {
        console.log(`📋 Answer ${index + 1}:`, {
          questionId: answer.questionId,
          questionType: answer.questionType,
          imageUrisCount: answer.imageUris?.length || 0,
          imageUris: answer.imageUris?.slice(0, 2) || [], // Log first 2 URIs
          answerValue: answer.answerValue,
        });
      });

      // Get all pending images for all questions
      console.log("🚨🚨🚨 REAL-TIME MONITORING: Getting pending images 🚨🚨🚨");
      console.log(
        "🔍 STEP 1: Getting pending images from ImageStorageManager..."
      );
      const pendingImages = await ImageStorageManager.getPendingImages();
      const totalImages = pendingImages.reduce(
        (sum, item) => sum + item.images.length,
        0
      );

      console.log("🚨 FORM SUBMISSION ANALYSIS:");
      console.log("📊 Form submission started:", {
        answers: data.answers.length,
        questionsWithImages: pendingImages.length,
        totalImages,
        planId: data.planId,
        storeId: data.storeId,
        userId: data.userId,
      });

      // Additional validation
      if (pendingImages.length === 0) {
        console.log(
          "⚠️ No pending images found - this might be expected if no images were uploaded"
        );
      } else {
        console.log(
          "✅ Found pending images to upload - proceeding with image upload"
        );
      }

      // CRITICAL DEBUG: Log detailed pending images info
      console.log("📸 DETAILED PENDING IMAGES INFO:");
      pendingImages.forEach((questionImages, index) => {
        console.log(`📸 Pending images ${index + 1}:`, {
          questionId: questionImages.questionId,
          imagesCount: questionImages.images.length,
          images: questionImages.images.map((img) => ({
            id: img.id,
            name: img.name,
            isUploaded: img.isUploaded,
            uri: img.uri.substring(0, 50) + "...", // Truncate URI for logging
          })),
        });
      });

      // Stage 2: Compress images if needed
      if (totalImages > 0) {
        this.updateProgress({
          stage: "compressing",
          message: `Đang nén ${totalImages} ảnh...`,
          progress: 10,
        });

        await this.compressAllImages(pendingImages);

        if (this.aborted)
          return { success: false, error: "Submission aborted" };
      }

      // Stage 3: Upload images
      let uploadedImages: { questionId: string; files: UploadedFile[] }[] = [];

      if (totalImages > 0) {
        this.updateProgress({
          stage: "uploading",
          message: `Đang tải lên ${totalImages} ảnh...`,
          progress: 30,
        });

        uploadedImages = await this.uploadAllImages(pendingImages);

        if (this.aborted)
          return { success: false, error: "Submission aborted" };
      }

      // Stage 4: Submit form data
      this.updateProgress({
        stage: "submitting",
        message: "Đang gửi dữ liệu form...",
        progress: 80,
      });

      const submissionResult = await this.submitFormData(data, uploadedImages);

      if (this.aborted) return { success: false, error: "Submission aborted" };

      // Stage 4.5: Update plan status to completed
      this.updateProgress({
        stage: "submitting",
        message: "Đang cập nhật trạng thái kế hoạch...",
        progress: 85,
      });

      try {
        console.log(
          "🔄 FormSubmissionHandler: Updating plan status to completed..."
        );
        // FIXED: Use static import to avoid Metro bundling issues
        await PlanService.updatePlanStatus(data.planId, "completed");
        console.log(
          "✅ FormSubmissionHandler: Plan status updated successfully"
        );
      } catch (statusError) {
        console.error(
          "❌ FormSubmissionHandler: Failed to update plan status:",
          statusError
        );
        // Don't fail the entire submission if plan status update fails
        // The form data was saved successfully
      }

      // Stage 5: Cleanup
      this.updateProgress({
        stage: "cleanup",
        message: "Đang dọn dẹp dữ liệu tạm...",
        progress: 95,
      });

      await this.cleanupAfterSubmission(pendingImages);

      // Stage 6: Complete
      this.updateProgress({
        stage: "complete",
        message: "Gửi thành công!",
        progress: 100,
      });

      console.log("Form submission completed successfully");

      return {
        success: true,
        submissionId: submissionResult.id,
        uploadedImages,
      };
    } catch (error: any) {
      console.error("Form submission failed:", error);

      return {
        success: false,
        error: error.message || "Có lỗi xảy ra khi gửi form",
      };
    }
  }

  /**
   * Compress all images for all questions
   */
  private async compressAllImages(
    pendingImages: { questionId: string; images: StoredImage[] }[]
  ): Promise<void> {
    let processedCount = 0;
    const totalImages = pendingImages.reduce(
      (sum, item) => sum + item.images.length,
      0
    );

    for (const { questionId, images } of pendingImages) {
      for (const image of images) {
        if (this.aborted) return;

        this.updateProgress({
          stage: "compressing",
          message: `Đang nén ảnh ${processedCount + 1}/${totalImages}...`,
          progress: 10 + (processedCount / totalImages) * 15,
          currentItem: image.name,
        });

        // Image is already compressed during storage, so we just update progress
        processedCount++;
      }
    }
  }

  /**
   * Upload all images for all questions
   */
  private async uploadAllImages(
    pendingImages: { questionId: string; images: StoredImage[] }[]
  ): Promise<{ questionId: string; files: UploadedFile[] }[]> {
    console.log("🔄 uploadAllImages: Starting image upload process");
    console.log("📤 Questions with images to upload:", pendingImages.length);

    const uploadResults: { questionId: string; files: UploadedFile[] }[] = [];
    let processedQuestions = 0;

    for (const { questionId, images } of pendingImages) {
      if (this.aborted) break;

      console.log(
        `📤 Processing question ${processedQuestions + 1}/${
          pendingImages.length
        }:`,
        {
          questionId,
          imagesCount: images.length,
          images: images.map((img) => ({
            id: img.id,
            name: img.name,
            isUploaded: img.isUploaded,
            size: img.size,
          })),
        }
      );

      this.updateProgress({
        stage: "uploading",
        message: `Đang tải ảnh cho câu hỏi ${processedQuestions + 1}/${
          pendingImages.length
        }...`,
        progress: 30 + (processedQuestions / pendingImages.length) * 45,
        currentItem: `${images.length} ảnh`,
      });

      try {
        console.log(
          `📤 Calling uploadMultipleQuestionImages for question: ${questionId}`
        );
        const uploadedFiles = await uploadMultipleQuestionImages(
          [{ questionId, images }],
          (questionIndex, fileIndex, progress) => {
            console.log(
              `📤 Upload progress: question ${questionIndex}, file ${fileIndex}, ${progress.percentage}%`
            );
            this.updateProgress({
              stage: "uploading",
              message: `Đang tải ảnh ${fileIndex + 1}/${images.length}...`,
              progress:
                30 +
                (processedQuestions / pendingImages.length) * 45 +
                (progress.percentage / 100) * (45 / pendingImages.length),
              currentItem: `${progress.percentage}%`,
            });
          }
        );

        console.log(
          `✅ uploadMultipleQuestionImages completed for question: ${questionId}`
        );
        console.log("📤 Upload result:", uploadedFiles);

        if (uploadedFiles.length > 0) {
          // uploadMultipleQuestionImages returns { questionId, uploadedFiles }[]
          const result = uploadedFiles[0];
          console.log(
            `✅ Successfully uploaded ${result.uploadedFiles.length} files for question: ${questionId}`
          );
          console.log(
            "📤 Uploaded file details:",
            result.uploadedFiles.map((f) => ({
              id: f.id,
              url: f.url,
              name: f.name,
            }))
          );

          uploadResults.push({
            questionId: result.questionId,
            files: result.uploadedFiles,
          });

          // Mark images as uploaded in storage
          const imageIds = images.map((img) => img.id);
          const uploadedFileIds = result.uploadedFiles.map((file) => file.id);

          console.log(`🔄 Marking images as uploaded in storage:`, {
            questionId,
            imageIds,
            uploadedFileIds,
          });

          await ImageStorageManager.markImagesAsUploaded(
            questionId,
            imageIds,
            uploadedFileIds
          );

          console.log(
            `✅ Images marked as uploaded in storage for question: ${questionId}`
          );
        } else {
          console.log(`⚠️ No files uploaded for question: ${questionId}`);
        }

        processedQuestions++;
      } catch (error) {
        console.error(
          `❌ Failed to upload images for question ${questionId}:`,
          error
        );
        throw new Error(
          `Không thể tải ảnh cho câu hỏi ${processedQuestions + 1}`
        );
      }
    }

    return uploadResults;
  }

  /**
   * Submit form data with image associations
   */
  private async submitFormData(
    data: FormSubmissionData,
    uploadedImages: { questionId: string; files: UploadedFile[] }[]
  ): Promise<any> {
    console.log("🚨🚨🚨 REAL-TIME MONITORING: submitFormData CALLED 🚨🚨🚨");
    console.log(
      "🔄 submitFormData: Building user answers with image associations"
    );
    console.log("📤 Uploaded images to associate:", uploadedImages.length);
    console.log("📋 Form answers count:", data.answers.length);

    // CRITICAL: Log uploaded images details with enhanced info
    console.log("🔍 DETAILED UPLOADED IMAGES ANALYSIS:");
    uploadedImages.forEach((imgGroup, index) => {
      console.log(`📤 Uploaded images group ${index + 1}:`, {
        questionId: imgGroup.questionId,
        filesCount: imgGroup.files.length,
        files: imgGroup.files.map((f) => ({
          id: f.id,
          url: f.url,
          name: f.name,
          size: f.size,
          mime: f.mime,
        })),
      });
    });

    // CRITICAL: Log form answers before processing
    console.log("🔍 FORM ANSWERS BEFORE PROCESSING:");
    data.answers.forEach((answer, index) => {
      console.log(`📋 Answer ${index + 1}:`, {
        questionId: answer.questionId,
        questionType: answer.questionType,
        answerValue: answer.answerValue,
        hasImageUris: !!answer.imageUris,
        imageUrisCount: answer.imageUris?.length || 0,
      });
    });

    // Build user answers with image associations
    const userAnswers = data.answers.map((answer, answerIndex) => {
      console.log(
        `🔄 Processing answer ${answerIndex + 1} for question: ${
          answer.questionId
        }`
      );

      const questionImages = uploadedImages.find(
        (img) => img.questionId === answer.questionId
      );

      console.log(`🔍 Image lookup for question ${answer.questionId}:`, {
        found: !!questionImages,
        filesCount: questionImages?.files.length || 0,
        files:
          questionImages?.files.map((f) => ({ id: f.id, name: f.name })) || [],
      });

      const userAnswer = {
        question: answer.questionId,
        answer_value: answer.answerValue,
        question_type: answer.questionType,
        photo_value: questionImages?.files || [], // Associate uploaded images
        plan: data.planId,
        store: data.storeId,
        user: data.userId,
      };

      console.log(`📋 Built user answer for question ${answer.questionId}:`, {
        questionId: answer.questionId,
        questionType: answer.questionType,
        answerValue: answer.answerValue,
        photoValueCount: userAnswer.photo_value.length,
        photoValue: userAnswer.photo_value.map((f) => ({
          id: f.id,
          url: f.url,
          name: f.name,
        })),
        hasQuestionImages: !!questionImages,
      });

      return userAnswer;
    });

    console.log("🚨🚨🚨 CRITICAL: FINAL USER ANSWERS ANALYSIS 🚨🚨🚨");
    console.log("🔄 Final user answers to submit:", userAnswers.length);

    // Enhanced logging for final user answers
    userAnswers.forEach((ua, index) => {
      console.log(`📋 Final User Answer ${index + 1}:`, {
        questionId: ua.question,
        questionType: ua.question_type,
        answerValue: ua.answer_value,
        photoValueCount: ua.photo_value.length,
        photoValueDetails: ua.photo_value.map((f) => ({
          id: f.id,
          name: f.name,
          url: f.url,
          size: f.size,
        })),
        hasPhotos: ua.photo_value.length > 0,
        planId: ua.plan,
        storeId: ua.store,
        userId: ua.user,
      });
    });

    // Submit to Strapi
    console.log("🚨🚨🚨 SUBMITTING TO STRAPI VIA submitUserAnswers 🚨🚨🚨");
    console.log("📤 Submission payload:", {
      planId: data.planId,
      storeId: data.storeId,
      userId: data.userId,
      answersCount: userAnswers.length,
    });

    const result = await submitUserAnswers({
      planId: data.planId,
      storeId: data.storeId,
      userId: data.userId,
      answers: userAnswers,
    });

    console.log("🚨🚨🚨 submitUserAnswers COMPLETED 🚨🚨🚨");
    console.log("✅ Result:", result);
    return result;
  }

  /**
   * Cleanup after successful submission
   */
  private async cleanupAfterSubmission(
    pendingImages: { questionId: string; images: StoredImage[] }[]
  ): Promise<void> {
    try {
      // Clean up temporary image files
      const allImageUris = pendingImages.flatMap((item) =>
        item.images.map((img) => img.uri)
      );

      await cleanupTempImages(allImageUris);

      // Clear stored images (they're now uploaded)
      for (const { questionId } of pendingImages) {
        await ImageStorageManager.clearQuestionImages(questionId);
      }

      console.log("Cleanup completed successfully");
    } catch (error) {
      console.warn("Cleanup failed (non-critical):", error);
    }
  }

  /**
   * Update progress callback
   */
  private updateProgress(progress: SubmissionProgress): void {
    this.onProgress?.(progress);
  }

  /**
   * Abort submission
   */
  abort(): void {
    this.aborted = true;
    console.log("Form submission aborted");
  }

  /**
   * Check if submission is aborted
   */
  isAborted(): boolean {
    return this.aborted;
  }
}
