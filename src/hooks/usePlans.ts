import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useMemo } from "react";
import { PlanService, PlanQueryParams } from "../api/plans";
import { queryKeys } from "../providers/QueryProvider";
import { FlatPlan } from "../types/entities";
import { useAuthContext } from "../providers/AuthProvider";

// Hook for fetching multiple plans
export const usePlans = (params?: PlanQueryParams) => {
  return useQuery({
    queryKey: queryKeys.plans.list(params),
    queryFn: () => PlanService.getPlans(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error: any) => {
      // Get status from different possible error structures
      const status = error?.status || error?.response?.status || 0;

      // Don't retry on authentication errors
      if (status === 401 || status === 403) {
        return false;
      }

      // Don't retry on 4xx errors except 408, 429
      if (status >= 400 && status < 500) {
        if (status === 408 || status === 429) {
          return failureCount < 2;
        }
        return false;
      }

      // Don't retry if we've already failed too many times
      if (failureCount >= 3) {
        return false;
      }

      // Retry on network errors and 5xx errors
      return true;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Hook for fetching plans for the current authenticated user
export const useUserPlans = (
  params?: Omit<PlanQueryParams, "filters"> & {
    filters?: Omit<PlanQueryParams["filters"], "merchandiser">;
  }
) => {
  const { user, isAuthenticated, isLoading } = useAuthContext();

  return useQuery({
    queryKey: queryKeys.plans.list({
      ...params,
      userId: user?.id,
    }),
    queryFn: () => {
      if (!user?.id) {
        throw new Error("User not authenticated");
      }
      return PlanService.getUserPlans(user.id, params);
    },
    enabled: !!user?.id && isAuthenticated && !isLoading, // Only run query if user is authenticated and not loading
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error: any) => {
      // Get status from different possible error structures
      const status = error?.status || error?.response?.status || 0;

      // Don't retry on authentication errors or user not authenticated
      if (
        status === 401 ||
        status === 403 ||
        error?.message === "User not authenticated"
      ) {
        return false;
      }

      // Don't retry on 4xx errors except 408, 429
      if (status >= 400 && status < 500) {
        if (status === 408 || status === 429) {
          return failureCount < 2;
        }
        return false;
      }

      // Don't retry if we've already failed too many times
      if (failureCount >= 3) {
        return false;
      }

      // Retry on network errors and 5xx errors
      return true;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Hook for fetching a single plan by ID
export const usePlan = (id: number | string, enabled = true) => {
  return useQuery({
    queryKey: queryKeys.plans.detail(id),
    queryFn: () => PlanService.getPlan(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error: any) => {
      // Get status from different possible error structures
      const status = error?.status || error?.response?.status || 0;

      // Don't retry on authentication errors
      if (status === 401 || status === 403) {
        return false;
      }

      // Don't retry on 4xx errors except 408, 429
      if (status >= 400 && status < 500) {
        if (status === 408 || status === 429) {
          return failureCount < 2;
        }
        return false;
      }

      // Don't retry if we've already failed too many times
      if (failureCount >= 3) {
        return false;
      }

      // Retry on network errors and 5xx errors
      return true;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Hook for filtering plans by status
export const useFilteredUserPlans = (
  status?: "pending" | "in_progress" | "completed" | "expired",
  enabled: boolean = true
) => {
  const { user, isAuthenticated, isLoading } = useAuthContext();

  const filters = status
    ? {
        plan_status: {
          $eq: status,
        },
      }
    : undefined;

  return useQuery({
    queryKey: queryKeys.plans.list({
      filters,
      userId: user?.id,
      status,
    }),
    queryFn: () => {
      if (!user?.id) {
        throw new Error("User not authenticated");
      }
      return PlanService.getUserPlans(user.id, { filters });
    },
    enabled: !!user?.id && isAuthenticated && !isLoading && enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error: any) => {
      // Get status from different possible error structures
      const status = error?.status || error?.response?.status || 0;

      // Don't retry on authentication errors or user not authenticated
      if (
        status === 401 ||
        status === 403 ||
        error?.message === "User not authenticated"
      ) {
        return false;
      }

      // Don't retry on 4xx errors except 408, 429
      if (status >= 400 && status < 500) {
        if (status === 408 || status === 429) {
          return failureCount < 2;
        }
        return false;
      }

      // Don't retry if we've already failed too many times
      if (failureCount >= 3) {
        return false;
      }

      // Retry on network errors and 5xx errors
      return true;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Hook for upcoming plans (within next 7 days)
export const useUpcomingUserPlans = (enabled: boolean = true) => {
  const { user, isAuthenticated, isLoading } = useAuthContext();

  // Memoize the date range to prevent infinite re-renders
  const dateRange = useMemo(() => {
    const now = new Date();
    // Set to start of today to avoid constant changes
    now.setHours(0, 0, 0, 0);
    const sevenDaysFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);

    return {
      start: now.toISOString(),
      end: sevenDaysFromNow.toISOString(),
    };
  }, []); // Empty dependency array - only calculate once per component mount

  const filters = {
    assigned_date: {
      $gte: dateRange.start,
      $lte: dateRange.end,
    },
  };

  return useQuery({
    queryKey: queryKeys.plans.list({
      filters,
      userId: user?.id,
      type: "upcoming",
    }),
    queryFn: () => {
      if (!user?.id) {
        throw new Error("User not authenticated");
      }
      return PlanService.getUserPlans(user.id, { filters });
    },
    enabled: !!user?.id && isAuthenticated && !isLoading && enabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchInterval: false, // Disable automatic refetching
    refetchOnMount: false, // Don't refetch on component mount
    refetchOnWindowFocus: false, // Don't refetch on window focus
    retry: (failureCount, error: any) => {
      // Get status from different possible error structures
      const status = error?.status || error?.response?.status || 0;

      // Don't retry on authentication errors or user not authenticated
      if (
        status === 401 ||
        status === 403 ||
        error?.message === "User not authenticated"
      ) {
        return false;
      }

      // Don't retry on 4xx errors except 408, 429
      if (status >= 400 && status < 500) {
        if (status === 408 || status === 429) {
          return failureCount < 2;
        }
        return false;
      }

      // Don't retry if we've already failed too many times
      if (failureCount >= 3) {
        return false;
      }

      // Retry on network errors and 5xx errors
      return true;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Utility hook for invalidating plan queries
export const usePlanMutations = () => {
  const queryClient = useQueryClient();

  const invalidatePlans = () => {
    queryClient.invalidateQueries({ queryKey: queryKeys.plans.all() });
  };

  const invalidateUserPlans = (userId?: number) => {
    if (userId) {
      queryClient.invalidateQueries({
        queryKey: queryKeys.plans.lists(),
        predicate: (query) => {
          const queryKey = query.queryKey as any[];
          return queryKey.some(
            (key) => typeof key === "object" && key?.userId === userId
          );
        },
      });
    } else {
      queryClient.invalidateQueries({ queryKey: queryKeys.plans.lists() });
    }
  };

  const invalidatePlan = (id: number | string) => {
    queryClient.invalidateQueries({ queryKey: queryKeys.plans.detail(id) });
  };

  return {
    invalidatePlans,
    invalidateUserPlans,
    invalidatePlan,
  };
};
