import { useState, useCallback } from "react";
import * as ImagePicker from "expo-image-picker";
import { uploadService, UploadedFile } from "~/src/api/upload";
import { Alert } from "react-native";
import { FILE_UPLOAD } from "~/src/types/constants";

export interface LocalFile {
  uri: string;
  name: string;
  size: number;
  type: string;
  id: string;
}

export interface UploadedFileWithLocal extends UploadedFile {
  localUri?: string;
}

export const useFileUpload = () => {
  const [localFiles, setLocalFiles] = useState<LocalFile[]>([]);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFileWithLocal[]>(
    []
  );
  const [isUploading, setIsUploading] = useState(false);

  const pickImages = useCallback(
    async (maxFiles: number = FILE_UPLOAD.MAX_IMAGES_DEFAULT) => {
      try {
        // Request permissions
        const { status } =
          await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== "granted") {
          Alert.alert(
            "Permission Required",
            "Sorry, we need camera roll permissions to make this work!"
          );
          return;
        }

        // Launch image picker
        const result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: ImagePicker.MediaTypeOptions.Images,
          allowsMultipleSelection: true,
          quality: 0.8,
          aspect: [4, 3],
        });

        if (!result.canceled && result.assets) {
          const newFiles: LocalFile[] = result.assets.map((asset, index) => ({
            uri: asset.uri,
            name: asset.fileName || `image_${Date.now()}_${index}.jpg`,
            size: asset.fileSize || 0,
            type: asset.type || "image/jpeg",
            id: `local_${Date.now()}_${index}`,
          }));

          // Check if adding these files would exceed maxFiles
          if (localFiles.length + newFiles.length > maxFiles) {
            Alert.alert(
              "Too Many Files",
              `You can only select up to ${maxFiles} images.`
            );
            return;
          }

          setLocalFiles((prev) => [...prev, ...newFiles]);
        }
      } catch (error) {
        console.error("Error picking images:", error);
        Alert.alert("Error", "Failed to pick images. Please try again.");
      }
    },
    [localFiles.length]
  );

  const takePhoto = useCallback(async () => {
    try {
      // Request permissions
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== "granted") {
        Alert.alert(
          "Permission Required",
          "Sorry, we need camera permissions to make this work!"
        );
        return;
      }

      // Launch camera
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        quality: 0.8,
        aspect: [4, 3],
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        const asset = result.assets[0];
        const newFile: LocalFile = {
          uri: asset.uri,
          name: asset.fileName || `photo_${Date.now()}.jpg`,
          size: asset.fileSize || 0,
          type: asset.type || "image/jpeg",
          id: `local_${Date.now()}`,
        };

        setLocalFiles((prev) => [...prev, newFile]);
      }
    } catch (error) {
      console.error("Error taking photo:", error);
      Alert.alert("Error", "Failed to take photo. Please try again.");
    }
  }, []);

  const removeLocalFile = useCallback((fileId: string) => {
    setLocalFiles((prev) => prev.filter((file) => file.id !== fileId));
  }, []);

  const uploadFiles = useCallback(async (): Promise<
    UploadedFileWithLocal[]
  > => {
    if (localFiles.length === 0) {
      return [];
    }

    setIsUploading(true);

    try {
      // Convert local files to File objects for upload
      const files: File[] = [];

      for (const localFile of localFiles) {
        // Create a file from the local URI
        const response = await fetch(localFile.uri);
        const blob = await response.blob();
        const file = new File([blob], localFile.name, { type: localFile.type });
        files.push(file);
      }

      // Upload to Strapi
      const uploadResponse = await uploadService.uploadFiles(files);

      // Map uploaded files with local URIs for reference
      const uploadedWithLocal: UploadedFileWithLocal[] =
        uploadResponse.data.map((uploadedFile, index) => ({
          ...uploadedFile,
          localUri: localFiles[index]?.uri,
        }));

      setUploadedFiles((prev) => [...prev, ...uploadedWithLocal]);
      setLocalFiles([]); // Clear local files after successful upload

      return uploadedWithLocal;
    } catch (error) {
      console.error("Error uploading files:", error);
      Alert.alert("Upload Error", "Failed to upload files. Please try again.");
      throw error;
    } finally {
      setIsUploading(false);
    }
  }, [localFiles]);

  const clearAll = useCallback(() => {
    setLocalFiles([]);
    setUploadedFiles([]);
  }, []);

  return {
    localFiles,
    uploadedFiles,
    isUploading,
    pickImages,
    takePhoto,
    removeLocalFile,
    uploadFiles,
    clearAll,
  };
};
