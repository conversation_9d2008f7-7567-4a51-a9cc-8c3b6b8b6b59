import { useAuthContext } from "../providers/AuthProvider";

export const useAuth = () => {
  const context = useAuthContext();
  
  return {
    // State
    user: context.user,
    isLoading: context.isLoading,
    isAuthenticated: context.isAuthenticated,
    error: context.error,

    // Actions
    login: context.login,
    logout: context.logout,
    checkAuth: context.checkAuth,
    clearError: context.clearError,
  };
};
