import { useState, useMemo } from "react";
import { FlatPlan } from "~/src/types/entities";
import { FilterValues } from "~/src/components/search/FilterSheet";

export interface UseSearchAndFilterProps {
  data: FlatPlan[];
}

export function useSearchAndFilter({ data }: UseSearchAndFilterProps) {
  const [filters, setFilters] = useState<FilterValues>({
    search: "",
    status: [],
    dateRange: {
      startDate: undefined,
      endDate: undefined,
    },
    assignedDateRange: {
      startDate: undefined,
      endDate: undefined,
    },
    stores: [],
    merchandisers: [],
  });

  // Calculate active filter count
  const activeFilterCount = useMemo(() => {
    let count = 0;

    if (filters.search.trim()) count++;
    if (filters.status.length > 0) count++;
    if (filters.dateRange.startDate || filters.dateRange.endDate) count++;
    if (
      filters.assignedDateRange.startDate ||
      filters.assignedDateRange.endDate
    )
      count++;
    if (filters.stores.length > 0) count++;
    if (filters.merchandisers.length > 0) count++;

    return count;
  }, [filters]);

  // Filter and search the data
  const filteredData = useMemo(() => {
    let result = [...data];

    // Search filter
    if (filters.search.trim()) {
      const searchTerm = filters.search.toLowerCase().trim();
      result = result.filter((plan) => {
        const searchableText = [
          plan.plan_title || "",
          plan.store?.store_name || "",
          plan.store?.store_code || "",
          plan.merchandiser?.username || "",
          plan.merchandiser?.email || "",
        ]
          .join(" ")
          .toLowerCase();

        return searchableText.includes(searchTerm);
      });
    }

    // Status filter
    if (filters.status.length > 0) {
      result = result.filter((plan) =>
        filters.status.includes(plan.plan_status || "")
      );
    }

    // Date range filter (start_date and end_date)
    if (filters.dateRange.startDate || filters.dateRange.endDate) {
      result = result.filter((plan) => {
        const planStartDate = plan.start_date
          ? new Date(plan.start_date)
          : null;
        const planEndDate = plan.end_date ? new Date(plan.end_date) : null;

        let matchesStart = true;
        let matchesEnd = true;

        if (filters.dateRange.startDate && planStartDate) {
          matchesStart = planStartDate >= filters.dateRange.startDate;
        }

        if (filters.dateRange.endDate && planEndDate) {
          matchesEnd = planEndDate <= filters.dateRange.endDate;
        }

        return matchesStart && matchesEnd;
      });
    }

    // Assigned date range filter
    if (
      filters.assignedDateRange.startDate ||
      filters.assignedDateRange.endDate
    ) {
      result = result.filter((plan) => {
        const assignedDate = plan.assigned_date
          ? new Date(plan.assigned_date)
          : null;

        if (!assignedDate) return false;

        let matchesStart = true;
        let matchesEnd = true;

        if (filters.assignedDateRange.startDate) {
          matchesStart = assignedDate >= filters.assignedDateRange.startDate;
        }

        if (filters.assignedDateRange.endDate) {
          matchesEnd = assignedDate <= filters.assignedDateRange.endDate;
        }

        return matchesStart && matchesEnd;
      });
    }

    // Store filter
    if (filters.stores.length > 0) {
      result = result.filter((plan) =>
        filters.stores.includes(plan.store?.id?.toString() || "")
      );
    }

    // Merchandiser filter
    if (filters.merchandisers.length > 0) {
      result = result.filter((plan) =>
        filters.merchandisers.includes(plan.merchandiser?.id?.toString() || "")
      );
    }

    return result;
  }, [data, filters]);

  // Update filters
  const updateFilters = (newFilters: Partial<FilterValues>) => {
    setFilters((prev) => ({ ...prev, ...newFilters }));
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      search: "",
      status: [],
      dateRange: {
        startDate: undefined,
        endDate: undefined,
      },
      assignedDateRange: {
        startDate: undefined,
        endDate: undefined,
      },
      stores: [],
      merchandisers: [],
    });
  };

  // Update search
  const updateSearch = (search: string) => {
    updateFilters({ search });
  };

  return {
    filters,
    filteredData,
    activeFilterCount,
    updateFilters,
    updateSearch,
    clearFilters,
    resultCount: filteredData.length,
    totalCount: data.length,
  };
}
