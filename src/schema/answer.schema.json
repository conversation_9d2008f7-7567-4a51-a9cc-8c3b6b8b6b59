{"kind": "collectionType", "collectionName": "answers", "info": {"singularName": "answer", "pluralName": "answers", "displayName": "Answer"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"text_value": {"type": "string"}, "boolean_value": {"type": "boolean"}, "photo_value": {"type": "media", "multiple": true, "allowedTypes": ["images", "files", "videos", "audios"]}, "multi_choice_values": {"type": "json"}, "yes_no_multi": {"type": "json"}, "visit": {"type": "relation", "relation": "oneToOne", "target": "api::visit.visit", "inversedBy": "answer"}, "question": {"type": "relation", "relation": "oneToOne", "target": "api::question.question", "mappedBy": "answer"}}}