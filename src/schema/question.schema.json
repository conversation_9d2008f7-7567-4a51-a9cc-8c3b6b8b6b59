{"kind": "collectionType", "collectionName": "questions", "info": {"singularName": "question", "pluralName": "questions", "displayName": "Question"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"question_text": {"type": "string", "required": true}, "type": {"type": "enumeration", "required": true, "enum": ["text", "number", "photo", "single_choice", "multi_choice", "yes_no", "rating"]}, "is_required": {"type": "boolean", "default": false}, "options": {"type": "json"}, "question_image": {"type": "media", "multiple": true, "allowedTypes": ["images", "files", "videos", "audios"]}, "audit_forms": {"type": "relation", "relation": "manyToMany", "target": "api::audit-form.audit-form", "mappedBy": "questions"}, "answer": {"type": "relation", "relation": "oneToOne", "target": "api::answer.answer", "inversedBy": "question"}, "stores": {"type": "relation", "relation": "oneToMany", "target": "api::store.store"}}}