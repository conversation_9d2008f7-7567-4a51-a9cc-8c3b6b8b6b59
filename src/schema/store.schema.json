{"kind": "collectionType", "collectionName": "stores", "info": {"singularName": "store", "pluralName": "stores", "displayName": "Store"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"store_name": {"type": "string", "required": true}, "store_code": {"type": "string", "required": true, "unique": true}, "address": {"type": "string"}, "is_active": {"type": "boolean", "default": true}, "plans": {"type": "relation", "relation": "oneToMany", "target": "api::plan.plan", "mappedBy": "store"}}}