{"kind": "collectionType", "collectionName": "user_answers", "info": {"singularName": "user-answer", "pluralName": "user-answers", "displayName": "UserAnswer"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"merchandiser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "plan": {"type": "relation", "relation": "manyToOne", "target": "api::plan.plan", "inversedBy": "user_answers"}, "text_value": {"type": "string"}, "boolean_value": {"type": "boolean"}, "photo_value": {"type": "media", "multiple": true, "allowedTypes": ["images", "files", "videos", "audios"]}, "multi_choice_values": {"type": "json"}, "yes_no_multi": {"type": "json"}, "question": {"type": "relation", "relation": "oneToOne", "target": "api::question.question"}}}