{"kind": "collectionType", "collectionName": "audit_forms", "info": {"singularName": "audit-form", "pluralName": "audit-forms", "displayName": "AuditForm"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "version": {"type": "string"}, "form_status": {"type": "enumeration", "default": "draft", "enum": ["draft", "active", "archived"]}, "questions": {"type": "relation", "relation": "manyToMany", "target": "api::question.question", "inversedBy": "audit_forms"}, "plan": {"type": "relation", "relation": "oneToOne", "target": "api::plan.plan", "mappedBy": "audit_form"}}}