{"kind": "collectionType", "collectionName": "plans", "info": {"singularName": "plan", "pluralName": "plans", "displayName": "Plan"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"assigned_date": {"type": "datetime"}, "start_date": {"type": "datetime"}, "end_date": {"type": "datetime"}, "plan_status": {"type": "enumeration", "enum": ["pending", "in_progress", "completed", "expired"]}, "visits_target": {"type": "integer"}, "store": {"type": "relation", "relation": "manyToOne", "target": "api::store.store", "inversedBy": "plans"}, "audit_form": {"type": "relation", "relation": "oneToOne", "target": "api::audit-form.audit-form", "inversedBy": "plan"}, "merchandiser": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "plans"}, "user_answers": {"type": "relation", "relation": "oneToMany", "target": "api::user-answer.user-answer", "mappedBy": "plan"}, "plan_title": {"type": "string"}}}