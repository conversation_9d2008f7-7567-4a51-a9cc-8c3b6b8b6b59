import React, { useState, useMemo } from "react";
import {
  View,
  Text,
  ScrollView,
  Pressable,
  ActivityIndicator,
  RefreshControl,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter, useFocusEffect } from "expo-router";
import { LinearGradient } from "expo-linear-gradient";
import { ScreenWithBottomNav } from "~/components/shared/ScreenWithBottomNav";
import { Card, CardContent } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import {
  CheckCircle,
  Clock,
  Circle,
  ChevronRight,
  Calendar,
  Store,
  User,
  AlertCircle,
  RefreshCw,
  Eye,
  TrendingUp,
  BarChart3,
  Smartphone,
  Sparkles,
} from "lucide-react-native";
import AuthHeader from "~/components/shared/AuthHeader";

import { auditTabs } from "~/src/mockdata";
import { useFilteredUserPlans } from "~/src/hooks/usePlans";
import { FlatPlan } from "~/src/types/entities";

// Search and Filter Components
import { SearchBar } from "~/src/components/search/SearchBar";
import { FilterButton } from "~/src/components/search/FilterButton";
import { FilterSheet } from "~/src/components/search/FilterSheet";
import { SearchResultsHeader } from "~/src/components/search/SearchResultsHeader";
import { useSearchAndFilter } from "~/src/hooks/useSearchAndFilter";

const tabs = auditTabs;

export default function HomeScreen() {
  const [activeTab, setActiveTab] = useState("pending");
  const [showFilterSheet, setShowFilterSheet] = useState(false);
  const router = useRouter();

  // Fetch data based on active tab - only fetch the active tab to prevent multiple simultaneous calls
  const pendingPlansQuery = useFilteredUserPlans(
    "pending",
    activeTab === "pending"
  );
  const completedPlansQuery = useFilteredUserPlans(
    "completed",
    activeTab === "completed"
  );

  // Function to refresh all plan queries
  const refreshPlans = () => {
    console.log("Refreshing plan queries...");
    if (activeTab === "pending") pendingPlansQuery.refetch();
    if (activeTab === "completed") completedPlansQuery.refetch();
  };

  // Auto-refresh when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      console.log("HomeScreen focused, refreshing plans...");
      refreshPlans();
    }, [activeTab])
  );

  // Get current query based on active tab
  const getCurrentQuery = () => {
    switch (activeTab) {
      case "pending":
        return pendingPlansQuery;
      case "completed":
        return completedPlansQuery;
      default:
        return pendingPlansQuery;
    }
  };

  const currentQuery = getCurrentQuery();
  const { data: plansResponse, isLoading, error, refetch } = currentQuery;
  const plans = plansResponse?.data || [];

  // Search and Filter Logic
  const {
    filters,
    filteredData,
    activeFilterCount,
    updateFilters,
    updateSearch,
    clearFilters,
    resultCount,
    totalCount,
  } = useSearchAndFilter({ data: plans });

  // Debug logging
  React.useEffect(() => {
    if (error) {
      console.error("Plan API Error:", error);
    }
    if (plansResponse) {
      console.log("Plan API Response:", plansResponse);
      console.log("Plans data:", plans);
    }
  }, [error, plansResponse, plans]);

  const handleAuditPress = (audit: any, planStatus?: string) => {
    // Navigate based on plan status
    if (planStatus === "completed") {
      // For completed plans, go to view submitted answers
      const planIdentifier = audit.id; // Use numeric ID instead of documentId
      console.log(
        "Navigating to submitted answers with planId:",
        planIdentifier
      );
      router.push(`/(audit)/submitted-answers?planId=${planIdentifier}`);
    } else {
      // For other statuses, go to audit detail/form
      const planIdentifier = audit.id; // Use numeric ID instead of documentId
      console.log(
        "Navigating to audit detail with documentId:",
        planIdentifier
      );
      router.push(`/(audit)/audit-detail?documentId=${planIdentifier}`);
    }
  };

  const handleViewAnswers = (audit: any, event: any) => {
    event.stopPropagation(); // Prevent triggering the card press
    const planIdentifier = audit.id; // Use numeric ID instead of documentId
    console.log("Navigating to submitted answers with planId:", planIdentifier);
    router.push(`/(audit)/submitted-answers?planId=${planIdentifier}`);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle size={20} color="#10b981" />;
      case "in_progress":
        return <Clock size={20} color="#3b82f6" />;
      case "pending":
        return <Circle size={20} color="#f59e0b" />;
      case "expired":
        return <AlertCircle size={20} color="#ef4444" />;
      default:
        return null;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "completed":
        return "Hoàn thành";
      case "in_progress":
        return "Đang thực hiện";
      case "pending":
        return "Cần thực hiện";
      case "expired":
        return "Đã hết hạn";
      default:
        return "";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "in_progress":
        return "bg-blue-100 text-blue-800";
      case "pending":
        return "bg-orange-100 text-orange-800";
      case "expired":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getCardStyle = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-gradient-to-r from-green-50 to-emerald-50 border-green-200";
      case "in_progress":
        return "bg-gradient-to-r from-blue-50 to-cyan-50 border-blue-200";
      case "pending":
        return "bg-gradient-to-r from-orange-50 to-amber-50 border-orange-200";
      case "expired":
        return "bg-gradient-to-r from-red-50 to-rose-50 border-red-200";
      default:
        return "bg-gradient-to-r from-gray-50 to-slate-50 border-gray-200";
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("vi-VN");
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString("vi-VN", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Transform plan data to match the expected format for UI components
  const transformedPlans = useMemo(() => {
    return filteredData.map((plan: FlatPlan) => ({
      id: plan.id.toString(),
      plan_title: plan.plan_title || "Untitled Plan",
      assigned_date:
        plan.assigned_date || plan.start_date || new Date().toISOString(),
      start_date: plan.start_date || new Date().toISOString(),
      end_date: plan.end_date || new Date().toISOString(),
      plan_status: plan.plan_status || "pending",
      visits_target: plan.visits_target || 1,
      store: {
        id: plan.store?.id?.toString() || "unknown",
        store_name: plan.store?.store_name || "Unknown Store",
        store_code: plan.store?.store_code || "N/A",
        address: plan.store?.address || "",
      },
      audit_form: plan.audit_form
        ? {
          id: plan.audit_form.id.toString(),
          name: plan.audit_form.name,
          version: plan.audit_form.version || "1.0",
          form_status: plan.audit_form.form_status,
        }
        : undefined,
      merchandiser: plan.merchandiser
        ? {
          id: plan.merchandiser.id.toString(),
          name: plan.merchandiser.username,
          email: plan.merchandiser.email,
        }
        : undefined,
    }));
  }, [filteredData]);

  return (
    <View className="flex-1 justify-center items-center">
      <LinearGradient
        colors={['#f8fafc', '#e2e8f0']}
        className="flex-1"
        style={{ width: "100%", height: "100%" }}
      >
        <SafeAreaView className="flex-1">
          <View className="flex-1">
            {/* Enhanced Header */}
            <View className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50">
              <View className="px-6 py-4">
                <View className="flex-row items-center justify-between">
                  <View className="flex-row items-center">
                    <View className="w-10 h-10 bg-purple-600 rounded-xl items-center justify-center mr-3">
                      <Smartphone size={20} color="white" />
                    </View>
                    <View>
                      <Text className="text-xl font-bold text-gray-900">
                        Cellphones Audit
                      </Text>
                      <Text className="text-sm text-gray-600">
                        Hệ thống kiểm toán thông minh
                      </Text>
                    </View>
                  </View>
                  <View className="flex-row items-center space-x-2">
                    <View className="w-8 h-8 bg-green-100 rounded-full items-center justify-center">
                      <TrendingUp size={16} color="#10b981" />
                    </View>
                    <View className="w-8 h-8 bg-blue-100 rounded-full items-center justify-center">
                      <BarChart3 size={16} color="#3b82f6" />
                    </View>
                  </View>
                </View>
              </View>
            </View>

            {/* Search and Filter Section */}
            <View className="px-4 py-2">
              <View className="flex-row items-center space-x-3">
                <View className="flex-1">
                  <SearchBar
                    value={filters.search}
                    onChangeText={updateSearch}
                    placeholder="Tìm kiếm kế hoạch, cửa hàng, nhân viên..."
                  />
                </View>
                <FilterButton
                  onPress={() => setShowFilterSheet(true)}
                  activeFilterCount={activeFilterCount}
                />
              </View>
            </View>

            {/* Search Results Header */}
            <SearchResultsHeader
              resultCount={resultCount}
              totalCount={totalCount}
              searchTerm={filters.search}
              activeFilterCount={activeFilterCount}
            />

            {/* Enhanced Tab Navigation */}
            <View className="py-4 px-4">
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View className="flex-row space-x-2">
                  {auditTabs.map((tab) => (
                    <Pressable
                      key={tab.key}
                      onPress={() => setActiveTab(tab.key)}
                      className={`px-6 py-3 rounded-full border ${activeTab === tab.key
                        ? "bg-purple-600 border-transparent"
                        : "bg-white/80 border-gray-200"
                        }`}
                    >
                      <Text
                        className={`font-semibold ${activeTab === tab.key ? "text-white" : "text-gray-700"
                          }`}
                      >
                        {tab.label}
                      </Text>
                    </Pressable>
                  ))}
                </View>
              </ScrollView>
            </View>

            {/* Audit List */}
            <ScrollView
              className="flex-1 px-4 pt-0"
              showsVerticalScrollIndicator={false}
              refreshControl={
                <RefreshControl
                  refreshing={isLoading}
                  onRefresh={refreshPlans}
                  colors={["#3B82F6"]}
                  tintColor="#3B82F6"
                />
              }
            >
              {/* Loading State */}
              {isLoading && (
                <View className="flex-1 justify-center items-center py-12">
                  <View className="w-16 h-16 bg-purple-600 rounded-full items-center justify-center mb-4">
                    <ActivityIndicator size="large" color="white" />
                  </View>
                  <Text className="text-gray-600 text-lg font-medium">Đang tải dữ liệu...</Text>
                  <Text className="text-gray-500 text-sm mt-2">Vui lòng chờ trong giây lát</Text>
                </View>
              )}

              {/* Error State */}
              {error && !isLoading && (
                <View className="flex-1 justify-center items-center py-12">
                  <View className="w-16 h-16 bg-red-100 rounded-full items-center justify-center mb-4">
                    <AlertCircle size={32} color="#ef4444" />
                  </View>
                  <Text className="text-gray-800 font-semibold text-lg mt-4 text-center">
                    Không thể tải dữ liệu
                  </Text>
                  <Text className="text-gray-600 mt-2 text-center px-4">
                    {error instanceof Error
                      ? error.message
                      : "Đã xảy ra lỗi khi tải dữ liệu"}
                  </Text>
                  <Pressable
                    onPress={() => refetch()}
                    className="mt-6 bg-purple-600 px-8 py-4 rounded-xl flex-row items-center"
                  >
                    <RefreshCw size={18} color="white" />
                    <Text className="text-white font-semibold ml-2">Thử lại</Text>
                  </Pressable>
                </View>
              )}

              {/* Empty State */}
              {!isLoading && !error && filteredData.length === 0 && (
                <View className="flex-1 justify-center items-center py-12">
                  <View className="w-16 h-16 bg-gray-100 rounded-full items-center justify-center mb-4">
                    <Circle size={32} color="#6b7280" />
                  </View>
                  <Text className="text-gray-800 font-semibold text-lg mt-4 text-center">
                    Không có kế hoạch nào
                  </Text>
                  <Text className="text-gray-600 mt-2 text-center px-4">
                    Hiện tại không có kế hoạch kiểm toán nào trong danh mục này.
                  </Text>
                  <View className="mt-6 flex-row items-center">
                    <Sparkles size={16} color="#6b7280" />
                    <Text className="text-gray-500 text-sm ml-2">
                      Hãy kiểm tra lại sau
                    </Text>
                  </View>
                </View>
              )}

              {/* Data List */}
              {!isLoading &&
                !error &&
                filteredData.map((audit, index) => (
                  <Pressable
                    key={audit.id}
                    onPress={() => handleAuditPress(audit, audit.plan_status)}
                    className="mb-4"
                    style={{
                      transform: [{ scale: 0.98 }],
                    }}
                  >
                    <Card
                      className={`${getCardStyle(
                        audit.plan_status
                      )} shadow-lg border-0`}
                      >
                      <CardContent className="p-6">
                        <View className="flex-row justify-between items-start mb-4">
                          <View className="flex-1 mr-4">
                            <Text className="text-xl font-bold text-gray-900 mb-3">
                              {audit.plan_title}
                            </Text>

                            {/* Store Info */}
                            <View className="flex-row items-center mb-3">
                              <View className="w-8 h-8 bg-blue-100 rounded-full items-center justify-center mr-3">
                                <Store size={16} color="#3b82f6" />
                              </View>
                              <View className="flex-1">
                                <Text className="text-sm font-semibold text-gray-800">
                                  {audit.store.store_name}
                                </Text>
                                <Text className="text-xs text-gray-500">
                                  Mã: {audit.store.store_code}
                                </Text>
                              </View>
                            </View>

                            {/* Assigned Date */}
                            <View className="flex-row items-center mb-3">
                              <View className="w-8 h-8 bg-green-100 rounded-full items-center justify-center mr-3">
                                <Calendar size={16} color="#10b981" />
                              </View>
                              <View className="flex-1">
                                <Text className="text-sm font-medium text-gray-800">
                                  Ngày thực hiện
                                </Text>
                                <Text className="text-xs text-gray-600">
                                  {formatDateTime(audit.assigned_date)}
                                </Text>
                              </View>
                            </View>

                            {/* Merchandiser */}
                            <View className="flex-row items-center">
                              <View className="w-8 h-8 bg-purple-100 rounded-full items-center justify-center mr-3">
                                <User size={16} color="#8b5cf6" />
                              </View>
                              <View className="flex-1">
                                <Text className="text-sm font-medium text-gray-800">
                                  Người thực hiện
                                </Text>
                                <Text className="text-xs text-gray-600">
                                  {audit.merchandiser?.name || "N/A"}
                                </Text>
                              </View>
                            </View>
                          </View>

                          <View className="items-end">
                            <View className="flex-row items-center mb-2">
                              {getStatusIcon(audit.plan_status)}
                              <Text className="text-sm ml-2 font-semibold">
                                {getStatusLabel(audit.plan_status)}
                              </Text>
                            </View>
                            <Text className="text-xs text-gray-500">
                              ID: {audit.id}
                            </Text>
                          </View>
                        </View>

                        <View className="flex-row justify-between items-center pt-4 border-t border-gray-100">
                          <View className="flex-row items-center">
                            {audit.plan_status === "completed" && (
                              <Button
                                variant="outline"
                                size="sm"
                                onPress={(event) => handleViewAnswers(audit, event)}
                                className="mr-3 px-4 py-2 bg-blue-50 border-blue-200"
                              >
                                <View className="flex-row items-center">
                                  <Eye size={14} color="#3b82f6" />
                                  <Text className="ml-1 text-xs font-medium text-blue-600">
                                    Xem câu trả lời
                                  </Text>
                                </View>
                              </Button>
                            )}
                          </View>
                          <View className="w-8 h-8 bg-gray-100 rounded-full items-center justify-center">
                            <ChevronRight size={16} color="#6b7280" />
                          </View>
                        </View>
                      </CardContent>
                    </Card>
                  </Pressable>
                ))}
            </ScrollView>
          </View>
        </SafeAreaView>
        
        {/* Filter Sheet Modal */}
      <FilterSheet
        visible={showFilterSheet}
        onClose={() => setShowFilterSheet(false)}
        filters={filters}
        onFiltersChange={updateFilters}
        onApplyFilters={() => {
          // Filters are applied in real-time, so just close the sheet
        }}
        onClearFilters={clearFilters}
        activeFilterCount={activeFilterCount}
      />
      </LinearGradient>
    </View>
  );
}
