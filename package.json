{"name": "cellphones-audit", "main": "index.js", "version": "1.0.0", "scripts": {"dev": "expo start -c", "dev:web": "expo start -c --web", "dev:android": "expo start -c --android", "android": "expo start -c --android", "ios": "expo start -c --ios", "web": "expo start -c --web", "clean": "rm -rf .expo node_modules", "export": "npx expo export", "postinstall": "npx tailwindcss -i ./global.css -o ./node_modules/.cache/nativewind/global.css"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/datetimepicker": "^8.4.3", "@react-navigation/native": "^7.0.0", "@rn-primitives/avatar": "~1.2.0", "@rn-primitives/checkbox": "^1.2.0", "@rn-primitives/portal": "~1.3.0", "@rn-primitives/progress": "~1.2.0", "@rn-primitives/select": "^1.2.0", "@rn-primitives/slot": "~1.2.0", "@rn-primitives/tabs": "^1.2.0", "@rn-primitives/tooltip": "~1.2.0", "@tanstack/react-query": "^5.84.1", "axios": "^1.11.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^4.1.0", "expo": "^53.0.9", "expo-camera": "~16.1.11", "expo-document-picker": "^13.1.6", "expo-file-system": "~18.1.1", "expo-image-manipulator": "~13.1.0", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "^14.1.5", "expo-linking": "~7.1.5", "expo-navigation-bar": "~4.2.4", "expo-router": "~5.1.4", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.7", "lucide-react-native": "^0.511.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "~0.20.0", "tailwind-merge": "^2.2.1", "tailwindcss": "3.3.5", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.7", "expo-blur": "~14.1.5", "expo-av": "~15.1.7"}, "devDependencies": {"@babel/core": "^7.26.0", "@types/react": "~19.0.14", "typescript": "^5.8.3"}, "private": true}