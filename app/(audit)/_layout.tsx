import { Stack } from "expo-router";

export default function AuditLayout() {
  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen
        name="index"
        options={{
          title: "Audit",
        }}
      />
      <Stack.Screen
        name="create"
        options={{
          title: "Tạo mới",
        }}
      />
      <Stack.Screen
        name="audit-detail"
        options={{
          title: "Chi tiết",
        }}
      />
      <Stack.Screen
        name="plan"
        options={{
          title: "Kế hoạch",
        }}
      />
      <Stack.Screen
        name="audit-form"
        options={{
          title: "Biểu mẫu kiểm toán",
        }}
      />
      <Stack.Screen
        name="submitted-answers"
        options={{
          title: "Câu trả lời đã gửi",
        }}
      />
    </Stack>
  );
}
