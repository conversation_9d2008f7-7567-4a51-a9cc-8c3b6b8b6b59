import { router, Stack } from "expo-router";
import { View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Button } from "~/components/ui/button";
import { Text } from "~/components/ui/text";

export default function NotFoundScreen() {
  return (
    <>
      <Stack.Screen options={{ title: "Oops!" }} />

      <SafeAreaView className="flex-1 justify-center items-center">
        <View className="flex-1 justify-center items-center">
          <Text className="mb-3">Nội dung bạn đang tìm không tồn tại</Text>

          <Button onPress={() => router.push("/")}>
            <Text>Trở lại trang chủ</Text>
          </Button>
        </View>
      </SafeAreaView>
    </>
  );
}
