import React from "react";
import { View, Text, ScrollView } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { ThemeToggle } from "~/components/ThemeToggle";

export default function SettingsScreen() {
  return (
    <SafeAreaView className="flex-1 bg-background">
      <ScrollView className="flex-1">
        <View className="p-4">
          <Text className="text-2xl font-bold text-foreground mb-4">
            Cài đặt
          </Text>
          <View className="bg-card p-4 rounded-lg">
            <View className="flex-row items-center justify-between mb-4">
              <Text className="text-foreground">Chế độ tối</Text>
              <ThemeToggle />
            </View>
            <Text className="text-foreground">
              <PERSON><PERSON><PERSON> tùy chọn cài đặt khác sẽ được hiển thị ở đây
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
