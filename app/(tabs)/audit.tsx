import React from "react";
import { View, Text, ScrollView } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import { Button } from "~/components/ui/button";

export default function AuditTabScreen() {
  const handleCreateAudit = () => {
    router.push("/(audit)");
  };

  const handleViewPlan = () => {
    router.push("/(audit)/plan");
  };

  const handleViewDetail = () => {
    router.push("/(audit)/audit-detail?id=1");
  };

  return (
    <SafeAreaView className="flex-1 bg-background">
      <ScrollView className="flex-1">
        <View className="p-4">
          <Text className="text-2xl font-bold text-foreground mb-6">
            Kiểm toán Cellphones
          </Text>

          <View className="space-y-4">
            <Button onPress={handleCreateAudit} className="mb-4">
              <Text className="text-white font-medium">T<PERSON><PERSON> kiểm toán mới</Text>
            </Button>

            <Button onPress={handleViewPlan} className="mb-4">
              <Text className="text-white font-medium">Xem kế hoạch</Text>
            </Button>

            <Button onPress={handleViewDetail} className="mb-4">
              <Text className="text-white font-medium">Xem chi tiết audit</Text>
            </Button>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
