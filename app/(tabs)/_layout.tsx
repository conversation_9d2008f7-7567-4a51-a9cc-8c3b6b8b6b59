import React from "react";
import { View } from "react-native";
import { Stack } from "expo-router";
import { BottomNavigation } from "~/components/shared/BottomNavigation";

export default function TabLayout() {
  return (
    <View className="flex-1">
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen name="home" />
        <Stack.Screen name="audit" />
        <Stack.Screen name="notification" />
        <Stack.Screen name="profile" />
      </Stack>
      <BottomNavigation />
    </View>
  );
}