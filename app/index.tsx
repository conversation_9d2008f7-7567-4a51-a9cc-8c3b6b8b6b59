import { LoginScreen } from "~/src/modules/auth";
import { router } from "expo-router";

export default function LoginRoute() {
  const handleRegisterPress = () => {
    router.push("/(auth)/register");
  };

  const handleForgotPasswordPress = () => {
    router.push("/(auth)/forgot-password");
  };

  return (
    <LoginScreen
      onRegisterPress={handleRegisterPress}
      onForgotPasswordPress={handleForgotPasswordPress}
    />
  );
}
