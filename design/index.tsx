import React, { useState } from 'react';
import { Home, User, ShoppingCart, BookText, Calendar } from 'lucide-react'; // Added BookText and Calendar

// Map icon names from the config to Lucide React components
const iconMap = {
  home: Home,
  user: User,
  cart: ShoppingCart,
  recipe_book: BookText, // New icon mapping for recipes
  calendar: Calendar,    // New icon mapping for meal plan
};

// Main App component
function App() {
  // The updated configuration for the bottom navigation
  const navConfig = {
    "config": {
      "defaultStrokeWidth": 2,
      "defaultSize": 24,
      "defaultVariant": "square",
      "defaultBackgroundMode": "icon-only",
      "defaultSplitGap": "8px",
      "activeCondition": "activeTab === '{{tab}}'",
      "clickHandler": "setActiveTab('{{tab}}')",
      "variants": {
        "square": {
          "borderRadius": "4px",
          "defaultColors": {
            "iconColor": "#3E8BFF",
            "backgroundColor": "#EBF3FF",
            "labelColor": "#333333"
          },
          "activeConfig": {
            "colors": {
              "iconColor": "#FFFFFF",
              "backgroundColor": "#3E8BFF",
              "labelColor": "#3E8BFF"
            },
            "size": 26,
            "strokeWidth": 2.5
          }
        },
        "round": {
          "borderRadius": "50%",
          "backgroundMode": "split",
          "splitGap": "6px",
          "defaultColors": {
            "iconColor": "#8F5FE8",
            "backgroundColor": "#F5EFFD",
            "labelColor": "#333333"
          },
          "activeConfig": {
            "colors": {
              "iconColor": "#FFFFFF",
              "backgroundColor": "#8F5FE8",
              "labelColor": "#8F5FE8"
            },
            "size": 28,
            "strokeWidth": 2.5
          }
        }
      }
    },
    "icons": [ // This 'icons' array is still present in the provided JSON, but the app will now use 'bottom_navigation.items'
      {
        "icon": "home",
        "label": "Home",
        "tab": "home"
      },
      {
        "icon": "user",
        "label": "Profile",
        "tab": "profile"
      },
      {
        "icon": "order",
        "label": "Orders",
        "tab": "order"
      },
      {
        "icon": "cart",
        "label": "Cart",
        "tab": "cart",
      },
      {
        "icon": "category",
        "label": "Category",
        "tab": "category",
        "variant": "round"
      }
    ],
    "bottom_navigation": {
      "items": [
        {
          "id": "home",
          "icon": "home",
          "destination": "home_screen"
        },
        {
          "id": "recipes",
          "icon": "recipe_book",
          "destination": "recipe_screen"
        },
        {
          "id": "meal_plan",
          "icon": "calendar",
          "destination": "meal_plan_screen"
        },
        {
          "id": "shopping_list",
          "icon": "cart",
          "destination": "shopping_list_screen"
        },
        {
          "id": "profile",
          "icon": "user",
          "destination": "profile_screen"
        }
      ]
    }
  };

  // State to manage the currently active tab, initialized to the first item's ID from the new structure
  const [activeTab, setActiveTab] = useState(navConfig.bottom_navigation.items[0].id);

  // Function to render content based on the active tab's destination
  const renderContent = () => {
    // Find the active item to get its destination
    const activeItem = navConfig.bottom_navigation.items.find(item => item.id === activeTab);
    const destination = activeItem ? activeItem.destination : '';

    switch (destination) {
      case 'home_screen':
        return <div className="p-8 text-center text-lg font-semibold text-gray-700">Welcome to the Home Screen!</div>;
      case 'recipe_screen':
        return <div className="p-8 text-center text-lg font-semibold text-gray-700">Discover Delicious Recipes!</div>;
      case 'meal_plan_screen':
        return <div className="p-8 text-center text-lg font-semibold text-gray-700">Plan Your Meals!</div>;
      case 'shopping_list_screen':
        return <div className="p-8 text-center text-lg font-semibold text-gray-700">Your Shopping List</div>;
      case 'profile_screen':
        return <div className="p-8 text-center text-lg font-semibold text-gray-700">Your Profile Information</div>;
      default:
        return <div className="p-8 text-center text-lg font-semibold text-gray-700">Select a tab</div>;
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col font-inter">
      {/* Main content area, takes available space */}
      <div className="flex-grow flex items-center justify-center">
        {renderContent()}
      </div>

      {/* Bottom Navigation Bar */}
      <nav className="fixed bottom-0 left-0 right-0 w-full bg-white shadow-lg p-2 rounded-t-xl z-50">
        <div className="flex justify-around items-center h-16">
          {/* Iterate over the new bottom_navigation.items */}
          {navConfig.bottom_navigation.items.map((item, index) => {
            // Determine the variant for the current item, defaulting to the global config
            // Note: 'variant' property is not present in the new 'bottom_navigation.items',
            // so all items will use the defaultVariant (square).
            const variantType = item.variant || navConfig.config.defaultVariant;
            const variantConfig = navConfig.config.variants[variantType];

            // Check if the current item is active using its 'id'
            const isActive = activeTab === item.id;

            // Get colors and sizes based on active state
            const colors = isActive ? variantConfig.activeConfig.colors : variantConfig.defaultColors;
            const iconSize = isActive ? variantConfig.activeConfig.size : navConfig.config.defaultSize;
            const strokeWidth = isActive ? variantConfig.activeConfig.strokeWidth : navConfig.config.defaultStrokeWidth;
            const borderRadius = variantConfig.borderRadius;
            const backgroundMode = variantConfig.backgroundMode || navConfig.config.defaultBackgroundMode;

            // Get the Lucide icon component using item.icon
            const IconComponent = iconMap[item.icon];

            // Generate a display label from the item.id (e.g., "home" -> "Home", "meal_plan" -> "Meal Plan")
            const displayLabel = item.id.replace(/_/g, ' ').split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');

            return (
              <div
                key={index}
                className="flex flex-col items-center justify-center cursor-pointer transition-all duration-300 flex-1"
                onClick={() => setActiveTab(item.id)} // Use item.id for setting the active tab
              >
                {/* Icon Container */}
                <div
                  className="flex items-center justify-center p-2 transition-all duration-300"
                  style={{
                    backgroundColor: isActive && backgroundMode === 'icon-only' ? colors.backgroundColor : 'transparent',
                    borderRadius: borderRadius,
                  }}
                >
                  {IconComponent && (
                    <IconComponent
                      size={iconSize}
                      strokeWidth={strokeWidth}
                      color={colors.iconColor}
                    />
                  )}
                </div>
                {/* Label */}
                <span
                  className="text-xs mt-1 font-medium transition-all duration-300"
                  style={{ color: colors.labelColor }}
                >
                  {displayLabel} {/* Use the generated display label */}
                </span>
              </div>
            );
          })}
        </div>
      </nav>
    </div>
  );
}

export default App;
